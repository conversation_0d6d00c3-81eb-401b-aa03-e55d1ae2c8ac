<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Admin Authentication Test</h1>
        <p>This page tests the admin authentication system to identify issues.</p>

        <div class="test-section">
            <h3>1. Backend Health Check</h3>
            <button onclick="testBackendHealth()">Test Backend Health</button>
            <div id="health-result"></div>
        </div>

        <div class="test-section">
            <h3>2. Admin Login Test</h3>
            <div>
                <input type="email" id="email" placeholder="Email" value="<EMAIL>">
                <input type="password" id="password" placeholder="Password" value="admin123">
                <button onclick="testAdminLogin()">Test Admin Login</button>
            </div>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>3. Admin Profile Test</h3>
            <button onclick="testAdminProfile()">Test Admin Profile</button>
            <div id="profile-result"></div>
        </div>

        <div class="test-section">
            <h3>4. Cookie Test</h3>
            <button onclick="testCookies()">Check Cookies</button>
            <div id="cookie-result"></div>
        </div>

        <div class="test-section">
            <h3>5. CORS Test</h3>
            <button onclick="testCORS()">Test CORS</button>
            <div id="cors-result"></div>
        </div>
    </div>

    <script>
        async function testBackendHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<p>Testing backend health...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/health', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Backend Health Check Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Backend Health Check Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Backend Health Check Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            resultDiv.innerHTML = '<p>Testing admin login...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Login Successful</h4>
                            <p>Status: ${response.status}</p>
                            <p>Admin: ${data.data.admin.name} (${data.data.admin.email})</p>
                            <p>Role: ${data.data.admin.role}</p>
                            <p>Main Admin: ${data.data.admin.isMainAdmin}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Login Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testAdminProfile() {
            const resultDiv = document.getElementById('profile-result');
            resultDiv.innerHTML = '<p>Testing admin profile...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/current', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Profile Retrieved</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Profile Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Profile Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        async function testCookies() {
            const resultDiv = document.getElementById('cookie-result');
            const cookies = document.cookie;
            
            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🍪 Current Cookies</h4>
                    <pre>${cookies || 'No cookies found'}</pre>
                </div>
            `;
        }

        async function testCORS() {
            const resultDiv = document.getElementById('cors-result');
            resultDiv.innerHTML = '<p>Testing CORS...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/health', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:3000',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ CORS Preflight Successful</h4>
                        <p>Status: ${response.status}</p>
                        <p>Access-Control-Allow-Origin: ${response.headers.get('Access-Control-Allow-Origin')}</p>
                        <p>Access-Control-Allow-Credentials: ${response.headers.get('Access-Control-Allow-Credentials')}</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ CORS Test Error</h4>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        // Auto-run health check on page load
        window.onload = function() {
            testBackendHealth();
        };
    </script>
</body>
</html>
