// Minimal test server to identify the issue
import express from 'express';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

console.log('Starting minimal test server...');

try {
  // Test database connection
  console.log('Testing database connection...');
  
  import('./src/config/database').then(async (db) => {
    console.log('Database module loaded');

    try {
      // Initialize database first
      db.initializeDatabase();
      console.log('Database initialized');

      const isConnected = await db.testConnection();
      console.log('Database connection test result:', isConnected);
      
      // Test Admin model
      console.log('Testing Admin model...');
      const { Admin } = await import('./src/models/Admin');
      const adminCount = await Admin.count();
      console.log('Admin count:', adminCount);
      
      // Start Express server
      const app = express();
      
      app.get('/test', (req, res) => {
        res.json({ 
          message: 'Minimal server working',
          database: 'connected',
          adminCount 
        });
      });
      
      const PORT = 5002;
      app.listen(PORT, () => {
        console.log(`✅ Minimal test server running on port ${PORT}`);
        console.log(`Test URL: http://localhost:${PORT}/test`);
      });
      
    } catch (dbError) {
      console.error('❌ Database error:', dbError);
    }
    
  }).catch(error => {
    console.error('❌ Module import error:', error);
  });
  
} catch (error) {
  console.error('❌ Server startup error:', error);
}
