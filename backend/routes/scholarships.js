const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const fs = require('fs').promises;
const path = require('path');

// Helper function to read/write JSON file
const dbPath = path.join(__dirname, '../database/scholarships.json');

async function readDB() {
    try {
        const data = await fs.readFile(dbPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

async function writeDB(data) {
    await fs.writeFile(dbPath, JSON.stringify(data, null, 2));
}

// Get all scholarships
router.get('/', async (req, res) => {
    try {
        const scholarships = await readDB();
        res.json(scholarships);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching scholarships' });
    }
});

// Get featured scholarships
router.get('/featured', async (req, res) => {
    try {
        const scholarships = await readDB();
        const featured = scholarships.filter(s => s.featured).slice(0, 3);
        res.json(featured);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching featured scholarships' });
    }
});

// Get single scholarship
router.get('/:id', async (req, res) => {
    try {
        const scholarships = await readDB();
        const scholarship = scholarships.find(s => s.id === req.params.id);
        
        if (!scholarship) {
            return res.status(404).json({ message: 'Scholarship not found' });
        }
        
        res.json(scholarship);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching scholarship' });
    }
});

// Create new scholarship
router.post('/', [
    body('title').notEmpty().trim(),
    body('description').notEmpty().trim(),
    body('amount').isNumeric(),
    body('deadline').isISO8601(),
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        const scholarships = await readDB();
        const newScholarship = {
            id: Date.now().toString(),
            ...req.body,
            createdAt: new Date().toISOString()
        };
        
        scholarships.push(newScholarship);
        await writeDB(scholarships);
        
        res.status(201).json(newScholarship);
    } catch (error) {
        res.status(500).json({ message: 'Error creating scholarship' });
    }
});

// Update scholarship
router.put('/:id', [
    body('title').optional().trim(),
    body('description').optional().trim(),
    body('amount').optional().isNumeric(),
    body('deadline').optional().isISO8601(),
], async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        const scholarships = await readDB();
        const index = scholarships.findIndex(s => s.id === req.params.id);
        
        if (index === -1) {
            return res.status(404).json({ message: 'Scholarship not found' });
        }
        
        scholarships[index] = {
            ...scholarships[index],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeDB(scholarships);
        res.json(scholarships[index]);
    } catch (error) {
        res.status(500).json({ message: 'Error updating scholarship' });
    }
});

// Delete scholarship
router.delete('/:id', async (req, res) => {
    try {
        const scholarships = await readDB();
        const filtered = scholarships.filter(s => s.id !== req.params.id);
        
        if (filtered.length === scholarships.length) {
            return res.status(404).json({ message: 'Scholarship not found' });
        }
        
        await writeDB(filtered);
        res.json({ message: 'Scholarship deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting scholarship' });
    }
});

module.exports = router; 