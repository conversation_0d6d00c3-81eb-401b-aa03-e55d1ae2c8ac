const express = require('express');
const router = express.Router();
const nodemailer = require('nodemailer');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Email configuration
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
});

// Get current admin info
router.get('/current', async (req, res) => {
  try {
    const admin = await prisma.admin.findUnique({
      where: { email: req.user.email }
    });
    res.json(admin);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch admin info' });
  }
});

// Admin user management
router.post('/create-admin', async (req, res) => {
  try {
    // Check if the requesting admin is the main admin
    const requestingAdmin = await prisma.admin.findUnique({
      where: { email: req.user.email }
    });

    if (!requestingAdmin?.isMainAdmin) {
      return res.status(403).json({ error: 'Only the main admin can create new admins' });
    }

    const { email, name, role } = req.body;
    const admin = await prisma.admin.create({
      data: {
        email,
        name,
        role,
        privileges: role === 'super_admin' ? ['all'] : ['manage_scholarships', 'manage_messages'],
        isMainAdmin: false // Ensure new admins are not main admins
      }
    });
    res.json(admin);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create admin' });
  }
});

// Get all admins
router.get('/admins', async (req, res) => {
  try {
    const requestingAdmin = await prisma.admin.findUnique({
      where: { email: req.user.email }
    });

    if (!requestingAdmin?.isMainAdmin) {
      return res.status(403).json({ error: 'Only the main admin can view admin list' });
    }

    const admins = await prisma.admin.findMany();
    res.json(admins);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch admins' });
  }
});

// Delete admin
router.delete('/admins/:id', async (req, res) => {
  try {
    const requestingAdmin = await prisma.admin.findUnique({
      where: { email: req.user.email }
    });

    if (!requestingAdmin?.isMainAdmin) {
      return res.status(403).json({ error: 'Only the main admin can delete admins' });
    }

    const adminToDelete = await prisma.admin.findUnique({
      where: { id: parseInt(req.params.id) }
    });

    if (adminToDelete?.isMainAdmin) {
      return res.status(403).json({ error: 'Cannot delete the main admin' });
    }

    await prisma.admin.delete({
      where: { id: parseInt(req.params.id) }
    });
    res.json({ message: 'Admin deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete admin' });
  }
});

// Message management
router.get('/messages', async (req, res) => {
  try {
    const messages = await prisma.message.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    res.json(messages);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

router.post('/messages/:id/reply', async (req, res) => {
  try {
    const { id } = req.params;
    const { reply } = req.body;
    
    const message = await prisma.message.findUnique({
      where: { id: parseInt(id) }
    });

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    // Send email reply
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: message.email,
      subject: 'Reply to your message',
      text: reply
    });

    // Update message status
    await prisma.message.update({
      where: { id: parseInt(id) },
      data: { status: 'replied' }
    });

    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Failed to send reply' });
  }
});

// Newsletter notification
router.post('/notify-subscribers', async (req, res) => {
  try {
    const { scholarshipId } = req.body;
    const scholarship = await prisma.scholarship.findUnique({
      where: { id: parseInt(scholarshipId) }
    });

    const subscribers = await prisma.newsletter.findMany();

    for (const subscriber of subscribers) {
      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: subscriber.email,
        subject: 'New Scholarship Available!',
        html: `
          <h2>New Scholarship: ${scholarship.title}</h2>
          <p>${scholarship.description}</p>
          <p>Visit our website to learn more!</p>
        `
      });
    }

    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Failed to notify subscribers' });
  }
});

module.exports = router; 