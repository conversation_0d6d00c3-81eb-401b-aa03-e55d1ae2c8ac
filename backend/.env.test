PORT=5001
JWT_SECRET=mabourse-test-jwt-secret-key
JWT_EXPIRATION=1d
REFRESH_TOKEN_SECRET=mabourse-test-refresh-token-key
REFRESH_TOKEN_EXPIRATION=7d
NODE_ENV=test

# Email configuration
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test-password
EMAIL_FROM=MaBourse Test <<EMAIL>>

# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=mabourse_test

# Prisma database URL
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# Admin configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=test123
ADMIN_NAME=Test Administrator

# CORS configuration
CORS_ORIGIN=http://localhost:3000

# Frontend URL for password reset links
FRONTEND_URL=http://localhost:3000

# Logging
LOG_LEVEL=debug

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
