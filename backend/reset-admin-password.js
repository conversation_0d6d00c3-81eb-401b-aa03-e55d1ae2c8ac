const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mabourse',
  password: 'postgres',
  port: 5432,
});

async function resetAdminPassword() {
  try {
    console.log('🔐 Resetting admin password...');
    
    // Hash the password
    const password = 'admin123';
    const hashedPassword = await bcrypt.hash(password, 10);
    
    console.log('Password to set:', password);
    console.log('Hashed password:', hashedPassword);
    
    // Update the admin password
    const result = await pool.query(
      'UPDATE admins SET password = $1 WHERE email = $2 RETURNING id, name, email',
      [hashedPassword, '<EMAIL>']
    );
    
    if (result.rows.length > 0) {
      console.log('✅ Admin password updated successfully!');
      console.log('Admin details:', result.rows[0]);
      console.log('');
      console.log('🔑 Login credentials:');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
    } else {
      console.log('❌ No admin found <NAME_EMAIL>');
    }
    
  } catch (error) {
    console.error('❌ Error resetting admin password:', error);
  } finally {
    await pool.end();
  }
}

resetAdminPassword();
