{"root": ["./src/app.ts", "./src/index.ts", "./src/config/database.ts", "./src/controllers/admin.password.controller.ts", "./src/controllers/auth.controller.new.ts", "./src/controllers/devicetrust.controller.ts", "./src/controllers/security.dashboard.controller.ts", "./src/database/migrate.ts", "./src/middleware/adaptiveratelimit.ts", "./src/middleware/advancedcsp.ts", "./src/middleware/apicache.middleware.ts", "./src/middleware/auth.new.ts", "./src/middleware/csrf.middleware.ts", "./src/middleware/error.middleware.ts", "./src/middleware/metrics.middleware.ts", "./src/middleware/ratelimiting.middleware.ts", "./src/middleware/requestsigning.ts", "./src/middleware/upload.middleware.ts", "./src/middleware/validation.middleware.ts", "./src/middleware/validation.ts", "./src/models/admin.ts", "./src/models/message.ts", "./src/models/newsletter.ts", "./src/models/scholarship.ts", "./src/models/user.ts", "./src/routes/admin.password.routes.ts", "./src/routes/admin.routes.ts", "./src/routes/auth.new.ts", "./src/routes/devicetrust.routes.ts", "./src/routes/messages.ts", "./src/routes/newsletter.ts", "./src/routes/scholarship.routes.ts", "./src/routes/scholarships.ts", "./src/routes/security.csp.routes.ts", "./src/routes/security.dashboard.routes.ts", "./src/routes/twofactor.routes.ts", "./src/routes/user.routes.ts", "./src/tests/security.comprehensive.test.ts", "./src/types/express.d.ts", "./src/types/multer.d.ts", "./src/utils/anomalydetection.ts", "./src/utils/apiresponse.ts", "./src/utils/authlogger.ts", "./src/utils/dateutils.ts", "./src/utils/email.ts", "./src/utils/envvalidator.ts", "./src/utils/geolocation.ts", "./src/utils/logger.ts", "./src/utils/mlanomalydetection.ts", "./src/utils/passwordpolicy.ts", "./src/utils/performance.ts", "./src/utils/securitymonitor.ts", "./src/utils/twofactor.ts", "./src/utils/validaterequest.ts"], "errors": true, "version": "5.8.3"}