import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import { initializeDatabase, testConnection } from './src/config/database';

console.log('Starting core test server...');

async function startServer() {
  try {
    // Initialize database
    const db = initializeDatabase();
    console.log('Database initialized');
    
    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful');

    // Create Express app
    const app = express();
    
    // Basic middleware
    app.use(cors());
    app.use(express.json());
    
    // Import and use only the core routes
    const scholarshipRoutes = await import('./src/routes/scholarship.routes');
    const messagesRoutes = await import('./src/routes/messages');
    const newsletterRoutes = await import('./src/routes/newsletter');
    
    app.use('/api/scholarships', scholarshipRoutes.default);
    app.use('/api/messages', messagesRoutes.default);
    app.use('/api/newsletter', newsletterRoutes.default);
    
    app.get('/health', (req, res) => {
      res.json({ status: 'ok', message: 'Core server is running' });
    });

    const PORT = 5004;
    app.listen(PORT, () => {
      console.log(`✅ Core server running on port ${PORT}`);
    });
  } catch (error) {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
  }
}

startServer();
