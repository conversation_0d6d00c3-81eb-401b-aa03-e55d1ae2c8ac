const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function setupMainAdmin() {
  try {
    const mainAdminEmail = '<EMAIL>';
    
    // Check if main admin already exists
    const existingAdmin = await prisma.admin.findFirst({
      where: { email: mainAdminEmail }
    });

    if (existingAdmin) {
      // Update existing admin to be main admin
      await prisma.admin.update({
        where: { id: existingAdmin.id },
        data: {
          isMainAdmin: true,
          role: 'super_admin',
          privileges: 'all'
        }
      });
      console.log('Existing admin updated to main admin');
    } else {
      // Create new main admin
      const mainAdmin = await prisma.admin.create({
        data: {
          email: mainAdminEmail,
          name: 'Main Admin',
          role: 'super_admin',
          privileges: 'all',
          isMainAdmin: true
        }
      });
      console.log('Main admin created successfully:', mainAdmin);
    }
    process.exit(0);
  } catch (error) {
    console.error('Error setting up main admin:', error);
    process.exit(1);
  }
}

setupMainAdmin(); 