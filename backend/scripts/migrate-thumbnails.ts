#!/usr/bin/env ts-node

/**
 * Thumbnail Migration CLI Script
 * 
 * This script migrates base64 thumbnails to file storage to ensure
 * compliance with industry production standards.
 * 
 * Usage:
 *   npm run migrate:thumbnails
 *   or
 *   npx ts-node scripts/migrate-thumbnails.ts
 */

import { ThumbnailMigrationService } from '../src/utils/migrateThumbnails';
import { initializeDatabase } from '../src/config/database';

async function main() {
  console.log('🚀 MaBourse Thumbnail Migration Tool');
  console.log('📋 Ensuring compliance with industry production standards\n');

  try {
    // Initialize database connection
    console.log('🔌 Initializing database connection...');
    await initializeDatabase();
    console.log('✅ Database connected successfully\n');

    // Run the migration
    const results = await ThumbnailMigrationService.migrateAllThumbnails();

    // Validate the migration
    const isValid = await ThumbnailMigrationService.validateMigration();

    if (isValid && results.errors === 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('✅ Database now complies with industry standards');
      process.exit(0);
    } else {
      console.log('\n⚠️  Migration completed with issues');
      console.log('🔍 Please review the logs above for details');
      process.exit(1);
    }

  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
