import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * Seed the database with test data
 */
async function seedTestData() {
  console.log('Starting database seeding...');
  
  try {
    // Create test users
    console.log('Creating test users...');
    const users = await createTestUsers();
    console.log(`Created ${users.length} test users`);
    
    // Create test admin users
    console.log('Creating test admin users...');
    const admins = await createTestAdmins();
    console.log(`Created ${admins.length} test admin users`);
    
    // Create test scholarships
    console.log('Creating test scholarships...');
    const scholarships = await createTestScholarships(users[0].id);
    console.log(`Created ${scholarships.length} test scholarships`);
    
    // Create test messages
    console.log('Creating test messages...');
    const messages = await createTestMessages();
    console.log(`Created ${messages.length} test messages`);
    
    // Create test newsletter subscriptions
    console.log('Creating test newsletter subscriptions...');
    const newsletters = await createTestNewsletters();
    console.log(`Created ${newsletters.length} test newsletter subscriptions`);
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Create test users
 */
async function createTestUsers() {
  // Hash password once
  const hashedPassword = await bcrypt.hash('Password123!', 10);
  
  // Create test users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Test User 1',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user',
        lastLogin: new Date(),
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Test User 2',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user',
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Test User 3',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user',
      },
    }),
  ]);
  
  return users;
}

/**
 * Create test admin users
 */
async function createTestAdmins() {
  // Hash password once
  const hashedPassword = await bcrypt.hash('AdminPass123!', 10);
  
  // Create test admin users
  const admins = await Promise.all([
    prisma.admin.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Main Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        privileges: JSON.stringify(['users', 'scholarships', 'messages', 'newsletters', 'settings']),
        isMainAdmin: true,
        twoFactorEnabled: false,
        lastLogin: new Date(),
      },
    }),
    prisma.admin.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Content Editor',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        privileges: JSON.stringify(['scholarships', 'messages', 'newsletters']),
        isMainAdmin: false,
        twoFactorEnabled: false,
      },
    }),
  ]);
  
  return admins;
}

/**
 * Create test scholarships
 */
async function createTestScholarships(userId: number) {
  // Create test scholarships
  const scholarships = await Promise.all([
    prisma.scholarship.upsert({
      where: { id: 1 },
      update: {},
      create: {
        title: 'Engineering Excellence Scholarship',
        description: 'Scholarship for outstanding engineering students',
        level: 'Undergraduate',
        country: 'United States',
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        isOpen: true,
        financial_benefits_summary: 'Full tuition coverage plus $5,000 stipend',
        eligibility_summary: 'GPA 3.5+, Engineering major, US citizen',
        scholarship_link: 'https://example.com/engineering-scholarship',
        createdBy: userId,
      },
    }),
    prisma.scholarship.upsert({
      where: { id: 2 },
      update: {},
      create: {
        title: 'Global Leadership Program',
        description: 'Scholarship for future global leaders',
        level: 'Graduate',
        country: 'Multiple',
        deadline: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        isOpen: true,
        financial_benefits_summary: 'Full tuition, living expenses, and travel stipend',
        eligibility_summary: 'Demonstrated leadership experience, any field of study',
        scholarship_link: 'https://example.com/leadership-program',
        createdBy: userId,
      },
    }),
    prisma.scholarship.upsert({
      where: { id: 3 },
      update: {},
      create: {
        title: 'Arts and Humanities Fellowship',
        description: 'Fellowship for arts and humanities researchers',
        level: 'PhD',
        country: 'France',
        deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        isOpen: true,
        financial_benefits_summary: 'Monthly stipend of €1,500 for 12 months',
        eligibility_summary: 'Arts or humanities focus, research proposal required',
        scholarship_link: 'https://example.com/arts-fellowship',
        createdBy: userId,
      },
    }),
    prisma.scholarship.upsert({
      where: { id: 4 },
      update: {},
      create: {
        title: 'Women in STEM Scholarship',
        description: 'Supporting women pursuing STEM careers',
        level: 'Undergraduate',
        country: 'Canada',
        deadline: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago (closed)
        isOpen: false,
        financial_benefits_summary: '$10,000 per year for up to 4 years',
        eligibility_summary: 'Women in STEM fields, minimum GPA 3.0',
        scholarship_link: 'https://example.com/women-stem',
        createdBy: userId,
      },
    }),
  ]);
  
  return scholarships;
}

/**
 * Create test messages
 */
async function createTestMessages() {
  // Create test messages
  const messages = await Promise.all([
    prisma.message.upsert({
      where: { id: 1 },
      update: {},
      create: {
        name: 'John Smith',
        email: '<EMAIL>',
        subject: 'Scholarship Inquiry',
        content: 'I would like to know more about the Engineering Excellence Scholarship.',
        status: 'pending',
      },
    }),
    prisma.message.upsert({
      where: { id: 2 },
      update: {},
      create: {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        subject: 'Application Process',
        content: 'Could you provide more details about the application process for the Global Leadership Program?',
        status: 'replied',
      },
    }),
    prisma.message.upsert({
      where: { id: 3 },
      update: {},
      create: {
        name: 'Michael Brown',
        email: '<EMAIL>',
        subject: 'Technical Issue',
        content: 'I am having trouble submitting my application on the website.',
        status: 'pending',
      },
    }),
  ]);
  
  return messages;
}

/**
 * Create test newsletter subscriptions
 */
async function createTestNewsletters() {
  // Create test newsletter subscriptions
  const newsletters = await Promise.all([
    prisma.newsletter.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
      },
    }),
    prisma.newsletter.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
      },
    }),
    prisma.newsletter.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
      },
    }),
  ]);
  
  return newsletters;
}

// Run the seed function if this script is executed directly
if (require.main === module) {
  seedTestData()
    .then(() => {
      console.log('Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

export default seedTestData;
