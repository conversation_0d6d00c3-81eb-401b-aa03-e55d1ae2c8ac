/**
 * Setup Admin Script
 * 
 * Creates the main admin user for the MaBourse application
 */

import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';
import { initializeDatabase, query, closeDatabase } from '../src/config/database';

// Load environment variables
dotenv.config();

async function setupMainAdmin() {
  try {
    console.log('Setting up main admin...');
    
    // Initialize database connection
    initializeDatabase();
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'Zw1@x$y{l8pomVF6'; // From memory
    const adminName = 'Main Administrator';
    
    // Check if admin already exists
    const existingAdmin = await query(
      'SELECT * FROM admins WHERE email = $1',
      [adminEmail]
    );
    
    if (existingAdmin.rows.length > 0) {
      console.log('Admin already exists. Updating password...');
      
      // Hash the new password
      const hashedPassword = await bcrypt.hash(adminPassword, 10);
      
      // Update existing admin
      await query(`
        UPDATE admins 
        SET 
          password = $1,
          is_main_admin = true,
          role = 'super_admin',
          privileges = $2,
          failed_login_attempts = 0,
          lock_until = NULL,
          updated_at = NOW()
        WHERE email = $3
      `, [hashedPassword, JSON.stringify(['all']), adminEmail]);
      
      console.log('✅ Admin updated successfully');
    } else {
      console.log('Creating new admin...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash(adminPassword, 10);
      
      // Create new admin
      await query(`
        INSERT INTO admins (
          name, email, password, role, privileges, is_main_admin,
          failed_login_attempts, two_factor_enabled, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
      `, [
        adminName,
        adminEmail,
        hashedPassword,
        'super_admin',
        JSON.stringify(['all']),
        true,
        0,
        false
      ]);
      
      console.log('✅ Admin created successfully');
    }
    
    // Verify admin was created/updated
    const admin = await query(
      'SELECT id, name, email, role, is_main_admin FROM admins WHERE email = $1',
      [adminEmail]
    );
    
    if (admin.rows.length > 0) {
      console.log('Admin details:', {
        id: admin.rows[0].id,
        name: admin.rows[0].name,
        email: admin.rows[0].email,
        role: admin.rows[0].role,
        isMainAdmin: admin.rows[0].is_main_admin
      });
    }
    
    console.log('✅ Admin setup completed successfully');
    
  } catch (error) {
    console.error('❌ Error setting up admin:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
    process.exit(0);
  }
}

// Run the setup
setupMainAdmin();
