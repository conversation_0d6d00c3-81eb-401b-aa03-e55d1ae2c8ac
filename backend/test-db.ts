import dotenv from 'dotenv';
dotenv.config();

import { initializeDatabase, testConnection } from './src/config/database';

console.log('Testing database connection...');
console.log('DB_NAME from env:', process.env.DB_NAME);

async function testDB() {
  try {
    const db = initializeDatabase();
    console.log('Database initialized');
    
    const isConnected = await testConnection();
    console.log('Connection test result:', isConnected);
    
    if (isConnected) {
      console.log('✅ Database connection successful!');
    } else {
      console.log('❌ Database connection failed');
    }
  } catch (error) {
    console.error('❌ Database test error:', error);
  }
}

testDB();
