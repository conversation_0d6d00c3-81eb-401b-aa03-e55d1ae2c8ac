import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('Environment variables:');
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DATABASE_URL:', process.env.DATABASE_URL);

// Test the getEnv function
import { getEnv } from './src/utils/envValidator';
console.log('\nUsing getEnv function:');
console.log('DB_NAME via getEnv:', getEnv('DB_NAME', 'mabourse_dev'));
