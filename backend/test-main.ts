import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import { initializeDatabase, testConnection } from './src/config/database';

console.log('Starting test main server...');

async function startServer() {
  try {
    // Initialize database
    const db = initializeDatabase();
    console.log('Database initialized');

    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful');

    // Create Express app
    const app = express();
    
    app.get('/health', (req, res) => {
      res.json({ status: 'ok', message: 'Server is running' });
    });

    const PORT = 5003;
    app.listen(PORT, () => {
      console.log(`✅ Test server running on port ${PORT}`);
    });
  } catch (error) {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
  }
}

startServer();
