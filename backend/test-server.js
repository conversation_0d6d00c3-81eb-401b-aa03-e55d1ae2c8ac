/**
 * Simple test server to verify admin login functionality
 */

const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(express.json());
app.use(cookieParser());

// CORS middleware with specific configuration
app.use((req, res, next) => {
  // Get the origin from the request
  const origin = req.headers.origin;
  console.log('Request origin:', origin);

  // Set CORS headers
  res.header('Access-Control-Allow-Origin', origin || 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  next();
});

// Mock admin data
const admins = [
  {
    id: 1,
    name: 'Main Admin',
    email: '<EMAIL>',
    password: '$2a$10$XgNuWtdlXbXnd0Hk.2dT8.ek1zSPsRyXyouPbHbHxUwgG9fVxKLrq', // admin123
    role: 'super_admin',
    privileges: ['view', 'edit', 'delete', 'manage'],
    isMainAdmin: true,
    twoFactorEnabled: false
  },
  {
    id: 2,
    name: 'Regular Admin',
    email: '<EMAIL>',
    password: '$2a$10$XgNuWtdlXbXnd0Hk.2dT8.ek1zSPsRyXyouPbHbHxUwgG9fVxKLrq', // admin123
    role: 'admin',
    privileges: ['view', 'edit'],
    isMainAdmin: false,
    twoFactorEnabled: false
  },
  {
    id: 3,
    name: 'Abdoulaye Admin',
    email: '<EMAIL>',
    password: '$2a$10$XgNuWtdlXbXnd0Hk.2dT8.ek1zSPsRyXyouPbHbHxUwgG9fVxKLrq', // admin123
    role: 'super_admin',
    privileges: ['view', 'edit', 'delete', 'manage'],
    isMainAdmin: true,
    twoFactorEnabled: false
  }
];

// Admin login route
app.post('/api/admin/login', async (req, res) => {
  try {
    console.log('Admin login attempt');
    console.log('Body:', req.body);
    console.log('Headers:', req.headers);
    console.log('Cookies:', req.cookies);

    const { email, password } = req.body;

    // Find admin
    const admin = admins.find(a => a.email === email);
    console.log('Admin found:', admin ? admin.email : 'Not found');

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        error: 'Admin not found'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, admin.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        error: 'Password does not match'
      });
    }

    // Generate token
    const token = jwt.sign(
      {
        id: admin.id,
        email: admin.email,
        role: admin.role,
        isMainAdmin: admin.isMainAdmin
      },
      'your-secret-key',
      { expiresIn: '1d' }
    );

    // Set token in HTTP-only cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax', // Changed from strict to lax to allow cross-site navigation
      maxAge: 24 * 60 * 60 * 1000, // 1 day
      path: '/'
    });

    // Return admin info
    return res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin,
          privileges: admin.privileges,
          twoFactorEnabled: admin.twoFactorEnabled
        }
      }
    });
  } catch (error) {
    console.error('Admin login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

// Get current admin profile
app.get('/api/admin/current', (req, res) => {
  try {
    console.log('Getting admin profile');
    console.log('Cookies:', req.cookies);
    console.log('Headers:', req.headers);
    // Get token from cookies
    const token = req.cookies.token;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'No token found'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, 'your-secret-key');

    // Find admin
    const admin = admins.find(a => a.id === decoded.id);

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found',
        error: 'Admin account not found'
      });
    }

    // Return admin info
    return res.status(200).json({
      success: true,
      message: 'Admin profile retrieved successfully',
      data: {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin,
          privileges: admin.privileges,
          twoFactorEnabled: admin.twoFactorEnabled
        }
      }
    });
  } catch (error) {
    console.error('Get admin profile error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve admin profile',
      error: error.message
    });
  }
});

// Logout route
app.post('/api/admin/logout', (req, res) => {
  try {
    // Clear the token cookie
    res.clearCookie('token', {
      httpOnly: true,
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax', // Changed from strict to lax to allow cross-site navigation
      path: '/'
    });

    return res.status(200).json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Test server is running on port ${PORT}`);
});
