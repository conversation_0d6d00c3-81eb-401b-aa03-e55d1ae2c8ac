"use strict";
/**
 * Guide Model
 *
 * This model handles all database operations for guides (CV writing, document preparation, etc.)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Guide = void 0;
const database_1 = require("../config/database");
class Guide {
    /**
     * Create a new guide
     */
    static async create(guideData) {
        const result = await (0, database_1.query)(`
      INSERT INTO guides (
        title, content, category, slug, excerpt, thumbnail, is_published,
        read_time, tags, created_by, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
            guideData.title,
            guideData.content,
            guideData.category,
            guideData.slug,
            guideData.excerpt,
            guideData.thumbnail,
            guideData.isPublished !== undefined ? guideData.isPublished : true,
            guideData.readTime,
            guideData.tags ? JSON.stringify(guideData.tags) : null,
            guideData.createdBy,
            guideData.createdByAdmin,
        ]);
        return this.mapRowToGuide(result.rows[0]);
    }
    /**
     * Get all guides
     */
    static async findAll(options) {
        let queryText = 'SELECT * FROM guides WHERE 1=1';
        const params = [];
        let paramCount = 1;
        // Add filters
        if ((options === null || options === void 0 ? void 0 : options.isPublished) !== undefined) {
            queryText += ` AND is_published = $${paramCount}`;
            params.push(options.isPublished);
            paramCount++;
        }
        if (options === null || options === void 0 ? void 0 : options.category) {
            queryText += ` AND category = $${paramCount}`;
            params.push(options.category);
            paramCount++;
        }
        // Add ordering
        const orderBy = (options === null || options === void 0 ? void 0 : options.orderBy) || 'created_at';
        const orderDirection = (options === null || options === void 0 ? void 0 : options.orderDirection) || 'DESC';
        queryText += ` ORDER BY ${orderBy} ${orderDirection}`;
        // Add pagination
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
        }
        if (options === null || options === void 0 ? void 0 : options.offset) {
            queryText += ` OFFSET $${paramCount}`;
            params.push(options.offset);
            paramCount++;
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToGuide(row));
    }
    /**
     * Get guide by ID
     */
    static async findById(id) {
        const result = await (0, database_1.query)('SELECT * FROM guides WHERE id = $1', [id]);
        return result.rows.length > 0 ? this.mapRowToGuide(result.rows[0]) : null;
    }
    /**
     * Get guide by slug
     */
    static async findBySlug(slug) {
        const result = await (0, database_1.query)('SELECT * FROM guides WHERE slug = $1', [slug]);
        return result.rows.length > 0 ? this.mapRowToGuide(result.rows[0]) : null;
    }
    /**
     * Update guide
     */
    static async update(id, updateData) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        // Build dynamic update query
        Object.entries(updateData).forEach(([key, value]) => {
            if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
                const dbField = this.mapFieldToColumn(key);
                fields.push(`${dbField} = $${paramCount}`);
                if (key === 'tags' && Array.isArray(value)) {
                    values.push(JSON.stringify(value));
                }
                else {
                    values.push(value);
                }
                paramCount++;
            }
        });
        if (fields.length === 0) {
            return this.findById(id);
        }
        fields.push(`updated_at = NOW()`);
        values.push(id);
        const queryText = `
      UPDATE guides 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;
        const result = await (0, database_1.query)(queryText, values);
        return result.rows.length > 0 ? this.mapRowToGuide(result.rows[0]) : null;
    }
    /**
     * Delete guide
     */
    static async delete(id) {
        const result = await (0, database_1.query)('DELETE FROM guides WHERE id = $1', [id]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Get guides by category
     */
    static async findByCategory(category, limit) {
        let queryText = 'SELECT * FROM guides WHERE category = $1 AND is_published = true ORDER BY created_at DESC';
        const params = [category];
        if (limit) {
            queryText += ' LIMIT $2';
            params.push(limit.toString());
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToGuide(row));
    }
    /**
     * Search guides
     */
    static async search(searchTerm, options) {
        let queryText = `
      SELECT * FROM guides 
      WHERE is_published = true 
      AND (title ILIKE $1 OR content ILIKE $1 OR excerpt ILIKE $1)
    `;
        const params = [`%${searchTerm}%`];
        let paramCount = 2;
        if (options === null || options === void 0 ? void 0 : options.category) {
            queryText += ` AND category = $${paramCount}`;
            params.push(options.category);
            paramCount++;
        }
        queryText += ' ORDER BY created_at DESC';
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit.toString());
            paramCount++;
        }
        if (options === null || options === void 0 ? void 0 : options.offset) {
            queryText += ` OFFSET $${paramCount}`;
            params.push(options.offset.toString());
            paramCount++;
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToGuide(row));
    }
    /**
     * Map field names to database column names
     */
    static mapFieldToColumn(field) {
        const fieldMap = {
            isPublished: 'is_published',
            readTime: 'read_time',
            createdBy: 'created_by',
            createdByAdmin: 'created_by_admin',
            createdAt: 'created_at',
            updatedAt: 'updated_at',
        };
        return fieldMap[field] || field;
    }
    /**
     * Map database row to GuideData
     */
    static mapRowToGuide(row) {
        return {
            id: row.id,
            title: row.title,
            content: row.content,
            category: row.category,
            slug: row.slug,
            excerpt: row.excerpt,
            thumbnail: row.thumbnail,
            isPublished: row.is_published,
            readTime: row.read_time,
            tags: row.tags ? JSON.parse(row.tags) : [],
            createdBy: row.created_by,
            createdByAdmin: row.created_by_admin,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
        };
    }
}
exports.Guide = Guide;
