"use strict";
/**
 * Scholarship Model
 *
 * This model handles all database operations for scholarships
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Scholarship = void 0;
const database_1 = require("../config/database");
class Scholarship {
    /**
     * Create a new scholarship
     */
    static async create(scholarshipData) {
        const result = await (0, database_1.query)(`
      INSERT INTO scholarships (
        title, description, level, country, deadline, is_open, thumbnail,
        coverage, financial_benefits_summary, eligibility_summary,
        scholarship_link, youtube_link, created_by, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *
    `, [
            scholarshipData.title,
            scholarshipData.description,
            scholarshipData.level,
            scholarshipData.country,
            scholarshipData.deadline,
            scholarshipData.isOpen !== undefined ? scholarshipData.isOpen : true,
            scholarshipData.thumbnail,
            scholarshipData.coverage,
            scholarshipData.financialBenefitsSummary,
            scholarshipData.eligibilitySummary,
            scholarshipData.scholarshipLink,
            scholarshipData.youtubeLink,
            scholarshipData.createdBy,
            scholarshipData.createdByAdmin
        ]);
        return this.mapRowToScholarship(result.rows[0]);
    }
    /**
     * Find scholarship by ID
     */
    static async findById(id) {
        const result = await (0, database_1.query)('SELECT * FROM scholarships WHERE id = $1', [id]);
        return result.rows.length > 0 ? this.mapRowToScholarship(result.rows[0]) : null;
    }
    /**
     * Get all scholarships
     */
    static async findAll(options) {
        let queryText = 'SELECT * FROM scholarships WHERE 1=1';
        const params = [];
        let paramCount = 1;
        // Add filters
        if ((options === null || options === void 0 ? void 0 : options.isOpen) !== undefined) {
            queryText += ` AND is_open = $${paramCount}`;
            params.push(options.isOpen);
            paramCount++;
        }
        if (options === null || options === void 0 ? void 0 : options.level) {
            queryText += ` AND level = $${paramCount}`;
            params.push(options.level);
            paramCount++;
        }
        if (options === null || options === void 0 ? void 0 : options.country) {
            queryText += ` AND country = $${paramCount}`;
            params.push(options.country);
            paramCount++;
        }
        // Add ordering
        const orderBy = (options === null || options === void 0 ? void 0 : options.orderBy) || 'created_at';
        const orderDirection = (options === null || options === void 0 ? void 0 : options.orderDirection) || 'DESC';
        queryText += ` ORDER BY ${orderBy} ${orderDirection}`;
        // Add pagination
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
            if (options === null || options === void 0 ? void 0 : options.offset) {
                queryText += ` OFFSET $${paramCount}`;
                params.push(options.offset);
                paramCount++;
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToScholarship(row));
    }
    /**
     * Search scholarships
     */
    static async search(searchTerm, options) {
        let queryText = `
      SELECT * FROM scholarships 
      WHERE (title ILIKE $1 OR description ILIKE $1 OR country ILIKE $1 OR level ILIKE $1)
    `;
        const params = [`%${searchTerm}%`];
        let paramCount = 2;
        if ((options === null || options === void 0 ? void 0 : options.isOpen) !== undefined) {
            queryText += ` AND is_open = $${paramCount}`;
            params.push(options.isOpen);
            paramCount++;
        }
        queryText += ' ORDER BY created_at DESC';
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
            if (options === null || options === void 0 ? void 0 : options.offset) {
                queryText += ` OFFSET $${paramCount}`;
                params.push(options.offset);
                paramCount++;
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToScholarship(row));
    }
    /**
     * Update scholarship
     */
    static async update(id, updates) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        // Build dynamic update query
        Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
                const dbKey = this.camelToSnake(key);
                fields.push(`${dbKey} = $${paramCount}`);
                values.push(value);
                paramCount++;
            }
        });
        if (fields.length === 0) {
            return this.findById(id);
        }
        values.push(id);
        const result = await (0, database_1.query)(`
      UPDATE scholarships 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);
        return result.rows.length > 0 ? this.mapRowToScholarship(result.rows[0]) : null;
    }
    /**
     * Delete scholarship
     */
    static async delete(id) {
        const result = await (0, database_1.query)('DELETE FROM scholarships WHERE id = $1', [id]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Count total scholarships
     */
    static async count(filters) {
        let queryText = 'SELECT COUNT(*) as count FROM scholarships WHERE 1=1';
        const params = [];
        let paramCount = 1;
        if ((filters === null || filters === void 0 ? void 0 : filters.isOpen) !== undefined) {
            queryText += ` AND is_open = $${paramCount}`;
            params.push(filters.isOpen);
            paramCount++;
        }
        if (filters === null || filters === void 0 ? void 0 : filters.level) {
            queryText += ` AND level = $${paramCount}`;
            params.push(filters.level);
            paramCount++;
        }
        if (filters === null || filters === void 0 ? void 0 : filters.country) {
            queryText += ` AND country = $${paramCount}`;
            params.push(filters.country);
            paramCount++;
        }
        const result = await (0, database_1.query)(queryText, params);
        return parseInt(result.rows[0].count);
    }
    /**
     * Get scholarships by deadline (upcoming)
     */
    static async findUpcoming(limit) {
        const result = await (0, database_1.query)(`
      SELECT * FROM scholarships 
      WHERE deadline > CURRENT_TIMESTAMP AND is_open = TRUE
      ORDER BY deadline ASC
      ${limit ? 'LIMIT $1' : ''}
    `, limit ? [limit] : []);
        return result.rows.map(row => this.mapRowToScholarship(row));
    }
    /**
     * Get scholarships by level
     */
    static async findByLevel(level, limit) {
        const result = await (0, database_1.query)(`
      SELECT * FROM scholarships 
      WHERE level = $1 AND is_open = TRUE
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [level, limit] : [level]);
        return result.rows.map(row => this.mapRowToScholarship(row));
    }
    /**
     * Get scholarships by country
     */
    static async findByCountry(country, limit) {
        const result = await (0, database_1.query)(`
      SELECT * FROM scholarships 
      WHERE country = $1 AND is_open = TRUE
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [country, limit] : [country]);
        return result.rows.map(row => this.mapRowToScholarship(row));
    }
    /**
     * Get countries with scholarship counts
     */
    static async getCountriesWithCounts() {
        const result = await (0, database_1.query)(`
      SELECT country, COUNT(*) as count
      FROM scholarships
      WHERE country IS NOT NULL AND country != ''
      GROUP BY country
      ORDER BY count DESC, country ASC
    `);
        return result.rows.map(row => ({
            country: row.country,
            count: parseInt(row.count)
        }));
    }
    /**
     * Search countries by name
     */
    static async searchCountries(searchTerm) {
        const result = await (0, database_1.query)(`
      SELECT country, COUNT(*) as count
      FROM scholarships
      WHERE country IS NOT NULL AND country != '' AND country ILIKE $1
      GROUP BY country
      ORDER BY count DESC, country ASC
    `, [`%${searchTerm}%`]);
        return result.rows.map(row => ({
            country: row.country,
            count: parseInt(row.count)
        }));
    }
    /**
     * Map database row to ScholarshipData
     */
    static mapRowToScholarship(row) {
        return {
            id: row.id,
            title: row.title,
            description: row.description,
            level: row.level,
            country: row.country,
            deadline: row.deadline,
            isOpen: row.is_open,
            thumbnail: row.thumbnail,
            coverage: row.coverage,
            financialBenefitsSummary: row.financial_benefits_summary,
            eligibilitySummary: row.eligibility_summary,
            scholarshipLink: row.scholarship_link,
            youtubeLink: row.youtube_link,
            createdBy: row.created_by,
            createdByAdmin: row.created_by_admin,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
        };
    }
    /**
     * Convert camelCase to snake_case
     */
    static camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    }
}
exports.Scholarship = Scholarship;
