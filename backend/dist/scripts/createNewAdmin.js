"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function createNewAdmin() {
    try {
        console.log('Creating new admin user...');
        // Check if admin already exists
        const existingAdmin = await prisma.admin.findFirst({
            where: { email: '<EMAIL>' }
        });
        if (existingAdmin) {
            console.log('Admin already exists, updating password...');
            // Hash the password
            const hashedPassword = await bcryptjs_1.default.hash('password123', 10);
            // Update admin
            await prisma.admin.update({
                where: { id: existingAdmin.id },
                data: {
                    password: hashedPassword,
                    role: 'super_admin',
                    privileges: JSON.stringify(['all']),
                    isMainAdmin: true
                }
            });
            console.log('Admin updated successfully.');
        }
        else {
            console.log('Creating new admin...');
            // Hash the password
            const hashedPassword = await bcryptjs_1.default.hash('password123', 10);
            // Create admin
            await prisma.admin.create({
                data: {
                    name: 'New Admin',
                    email: '<EMAIL>',
                    password: hashedPassword,
                    role: 'super_admin',
                    privileges: JSON.stringify(['all']),
                    isMainAdmin: true
                }
            });
            console.log('Admin created successfully.');
        }
        // Verify the admin was created/updated
        const admin = await prisma.admin.findFirst({
            where: { email: '<EMAIL>' }
        });
        console.log('Admin details:');
        console.log(`- ID: ${admin === null || admin === void 0 ? void 0 : admin.id}`);
        console.log(`- Name: ${admin === null || admin === void 0 ? void 0 : admin.name}`);
        console.log(`- Email: ${admin === null || admin === void 0 ? void 0 : admin.email}`);
        console.log(`- Role: ${admin === null || admin === void 0 ? void 0 : admin.role}`);
        console.log(`- Is Main Admin: ${admin === null || admin === void 0 ? void 0 : admin.isMainAdmin}`);
        console.log(`- Password (hashed): ${admin === null || admin === void 0 ? void 0 : admin.password.substring(0, 20)}...`);
    }
    catch (error) {
        console.error('Error creating admin:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
createNewAdmin();
