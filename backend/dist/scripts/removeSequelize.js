"use strict";
/**
 * Sequelize Removal <PERSON>t
 *
 * This script removes Sequelize dependencies and files after migration to Prisma is complete.
 * It should only be run after all data has been migrated and all controllers have been updated.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeSequelize = removeSequelize;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const child_process_1 = require("child_process");
// Paths
const BACKEND_DIR = path_1.default.join(__dirname, '../..');
const SRC_DIR = path_1.default.join(BACKEND_DIR, 'src');
const MODELS_DIR = path_1.default.join(SRC_DIR, 'models');
const CONFIG_DIR = path_1.default.join(SRC_DIR, 'config');
const BACKUP_DIR = path_1.default.join(BACKEND_DIR, 'sequelize-backup');
// Log file
const LOG_FILE = path_1.default.join(BACKEND_DIR, 'logs', `sequelize-removal-${new Date().toISOString().replace(/:/g, '-')}.log`);
/**
 * Log message to console and file
 */
function log(message) {
    const timestampedMessage = `[${new Date().toISOString()}] ${message}`;
    console.log(timestampedMessage);
    // Create logs directory if it doesn't exist
    const logsDir = path_1.default.dirname(LOG_FILE);
    if (!fs_1.default.existsSync(logsDir)) {
        fs_1.default.mkdirSync(logsDir, { recursive: true });
    }
    fs_1.default.appendFileSync(LOG_FILE, timestampedMessage + '\n');
}
/**
 * Create backup directory for Sequelize files
 */
function createBackupDirectory() {
    log(`Creating backup directory at ${BACKUP_DIR}...`);
    if (!fs_1.default.existsSync(BACKUP_DIR)) {
        fs_1.default.mkdirSync(BACKUP_DIR, { recursive: true });
        fs_1.default.mkdirSync(path_1.default.join(BACKUP_DIR, 'models'), { recursive: true });
        fs_1.default.mkdirSync(path_1.default.join(BACKUP_DIR, 'config'), { recursive: true });
    }
    log('Backup directory created.');
}
/**
 * Backup and remove Sequelize model files
 */
function backupAndRemoveModels() {
    log('Backing up and removing Sequelize model files...');
    if (!fs_1.default.existsSync(MODELS_DIR)) {
        log(`Models directory not found at ${MODELS_DIR}`);
        return;
    }
    const modelFiles = fs_1.default.readdirSync(MODELS_DIR);
    for (const file of modelFiles) {
        const filePath = path_1.default.join(MODELS_DIR, file);
        const backupPath = path_1.default.join(BACKUP_DIR, 'models', file);
        // Read file content to check if it uses Sequelize
        const content = fs_1.default.readFileSync(filePath, 'utf8');
        if (content.includes('sequelize') || content.includes('Sequelize')) {
            log(`Backing up Sequelize model: ${file}`);
            fs_1.default.copyFileSync(filePath, backupPath);
            log(`Removing Sequelize model: ${file}`);
            fs_1.default.unlinkSync(filePath);
        }
    }
    log('Model backup and removal completed.');
}
/**
 * Backup and remove Sequelize configuration files
 */
function backupAndRemoveConfig() {
    log('Backing up and removing Sequelize configuration files...');
    if (!fs_1.default.existsSync(CONFIG_DIR)) {
        log(`Config directory not found at ${CONFIG_DIR}`);
        return;
    }
    const configFiles = fs_1.default.readdirSync(CONFIG_DIR);
    for (const file of configFiles) {
        const filePath = path_1.default.join(CONFIG_DIR, file);
        const backupPath = path_1.default.join(BACKUP_DIR, 'config', file);
        // Read file content to check if it uses Sequelize
        const content = fs_1.default.readFileSync(filePath, 'utf8');
        if (content.includes('sequelize') || content.includes('Sequelize')) {
            log(`Backing up Sequelize config: ${file}`);
            fs_1.default.copyFileSync(filePath, backupPath);
            log(`Removing Sequelize config: ${file}`);
            fs_1.default.unlinkSync(filePath);
        }
    }
    log('Config backup and removal completed.');
}
/**
 * Remove Sequelize database file
 */
function removeSequelizeDatabase() {
    log('Removing Sequelize database file...');
    const dbFile = path_1.default.join(BACKEND_DIR, 'database.sqlite');
    if (fs_1.default.existsSync(dbFile)) {
        const backupPath = path_1.default.join(BACKUP_DIR, 'database.sqlite');
        log(`Backing up Sequelize database to ${backupPath}`);
        fs_1.default.copyFileSync(dbFile, backupPath);
        log(`Removing Sequelize database: ${dbFile}`);
        fs_1.default.unlinkSync(dbFile);
    }
    else {
        log('Sequelize database file not found.');
    }
    log('Database removal completed.');
}
/**
 * Update package.json to remove Sequelize dependencies
 */
function updatePackageJson() {
    log('Updating package.json to remove Sequelize dependencies...');
    const packageJsonPath = path_1.default.join(BACKEND_DIR, 'package.json');
    if (!fs_1.default.existsSync(packageJsonPath)) {
        log(`package.json not found at ${packageJsonPath}`);
        return;
    }
    const packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
    // Create backup of package.json
    const backupPath = path_1.default.join(BACKUP_DIR, 'package.json');
    fs_1.default.writeFileSync(backupPath, JSON.stringify(packageJson, null, 2));
    // Remove Sequelize dependencies
    const dependencies = packageJson.dependencies || {};
    const sequelizeDeps = ['sequelize', 'sqlite3', 'pg-hstore'];
    let removed = false;
    for (const dep of sequelizeDeps) {
        if (dependencies[dep]) {
            log(`Removing dependency: ${dep}`);
            delete dependencies[dep];
            removed = true;
        }
    }
    if (removed) {
        // Write updated package.json
        fs_1.default.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        // Run npm install to update package-lock.json
        log('Running npm install to update package-lock.json...');
        try {
            (0, child_process_1.execSync)('npm install', { cwd: BACKEND_DIR, stdio: 'inherit' });
            log('npm install completed successfully.');
        }
        catch (error) {
            log(`ERROR: npm install failed: ${error}`);
        }
    }
    else {
        log('No Sequelize dependencies found in package.json.');
    }
    log('package.json update completed.');
}
/**
 * Run the Sequelize removal process
 */
async function removeSequelize() {
    try {
        log('Starting Sequelize removal process...');
        // Step 1: Create backup directory
        createBackupDirectory();
        // Step 2: Backup and remove model files
        backupAndRemoveModels();
        // Step 3: Backup and remove config files
        backupAndRemoveConfig();
        // Step 4: Remove Sequelize database file
        removeSequelizeDatabase();
        // Step 5: Update package.json
        updatePackageJson();
        log('Sequelize removal process completed successfully.');
    }
    catch (error) {
        log(`ERROR: Sequelize removal failed: ${error}`);
    }
}
// Run the removal if this script is executed directly
if (require.main === module) {
    removeSequelize()
        .then(() => {
        log('Removal script execution completed');
        process.exit(0);
    })
        .catch((error) => {
        log(`Unhandled error in removal script: ${error}`);
        process.exit(1);
    });
}
