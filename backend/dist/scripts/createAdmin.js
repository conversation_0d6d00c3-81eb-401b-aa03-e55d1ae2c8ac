"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function createAdmin() {
    try {
        const email = '<EMAIL>';
        const password = 'admin123'; // You should change this in production
        // Check if admin already exists
        const existingAdmin = await prisma.admin.findUnique({
            where: { email }
        });
        if (existingAdmin) {
            console.log('Admin user already exists');
            return;
        }
        // Hash password
        const hashedPassword = await bcryptjs_1.default.hash(password, 10);
        // Create admin user
        const admin = await prisma.admin.create({
            data: {
                email,
                password: hashedPassword,
                name: 'Admin User',
                role: 'super_admin',
                privileges: 'all',
                isMainAdmin: true
            }
        });
        console.log('Admin user created successfully:', {
            id: admin.id,
            email: admin.email,
            name: admin.name,
            role: admin.role
        });
    }
    catch (error) {
        console.error('Error creating admin user:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
createAdmin();
