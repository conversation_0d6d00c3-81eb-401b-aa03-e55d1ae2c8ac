"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedScholarships() {
    try {
        // Delete existing scholarships
        await prisma.scholarship.deleteMany({});
        console.log('Deleted existing scholarships');
        // Create sample scholarships
        const scholarships = [
            {
                title: 'Fulbright Scholarship Program',
                description: 'The Fulbright Program is the flagship international educational exchange program sponsored by the U.S. government.',
                thumbnail: 'https://example.com/fulbright.jpg',
                deadline: new Date('2025-12-31'),
                isOpen: true,
                level: 'Graduate',
                country: 'United States',
                coverage: 'Full',
                financial_benefits_summary: 'Full tuition coverage, Monthly stipend, Health insurance, Travel allowance',
                eligibility_summary: 'Bachelor\'s degree, English proficiency, Research proposal',
                scholarship_link: 'https://fulbright.org',
                youtube_link: 'https://www.youtube.com/watch?v=example1',
                createdBy: 1
            },
            {
                title: 'Chevening Scholarship',
                description: 'Chevening is the UK government\'s international awards program aimed at developing global leaders.',
                thumbnail: 'https://example.com/chevening.jpg',
                deadline: new Date('2025-11-02'),
                isOpen: true,
                level: 'Master\'s',
                country: 'United Kingdom',
                coverage: 'Full',
                financial_benefits_summary: 'Full tuition fees, Living allowance, Return flights, Networking opportunities',
                eligibility_summary: 'Non-UK citizen, Bachelor\'s degree holder, Work experience',
                scholarship_link: 'https://www.chevening.org',
                youtube_link: 'https://www.youtube.com/watch?v=example2',
                createdBy: 1
            },
            {
                title: 'DAAD Scholarship',
                description: 'The German Academic Exchange Service (DAAD) offers scholarships for international students to study in Germany.',
                thumbnail: 'https://example.com/daad.jpg',
                deadline: new Date('2025-10-15'),
                isOpen: true,
                level: 'PhD',
                country: 'Germany',
                coverage: 'Partial',
                financial_benefits_summary: 'Monthly stipend, Health insurance, Travel allowance, Research grants',
                eligibility_summary: 'Non-German citizen, Master\'s degree holder, Language proficiency',
                scholarship_link: 'https://www.daad.de/en/',
                youtube_link: 'https://www.youtube.com/watch?v=example3',
                createdBy: 1
            }
        ];
        // Insert scholarships
        for (const scholarship of scholarships) {
            await prisma.scholarship.create({
                data: scholarship
            });
        }
        console.log(`Added ${scholarships.length} sample scholarships`);
    }
    catch (error) {
        console.error('Error seeding scholarships:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
seedScholarships();
