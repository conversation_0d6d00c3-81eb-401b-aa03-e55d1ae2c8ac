"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const Admin_1 = __importDefault(require("../models/Admin"));
const database_1 = __importDefault(require("../config/database"));
async function createAdmin() {
    try {
        // Ensure database connection
        await database_1.default.authenticate();
        console.log('Database connection established successfully.');
        // Sync the model with the database
        await database_1.default.sync();
        console.log('Database models synchronized successfully.');
        const email = '<EMAIL>';
        const password = 'admin123'; // You should change this in production
        // Check if admin already exists
        const existingAdmin = await Admin_1.default.findOne({ where: { email } });
        if (existingAdmin) {
            console.log('Admin user already exists');
            return;
        }
        // Hash password
        const hashedPassword = await bcryptjs_1.default.hash(password, 10);
        // Create admin user
        const admin = await Admin_1.default.create({
            email,
            password: hashedPassword,
            name: 'Admin User',
            role: 'admin',
            privileges: ['all'],
            isMainAdmin: true
        });
        console.log('Admin user created successfully:', {
            id: admin.id,
            email: admin.email,
            name: admin.name,
            role: admin.role,
            isMainAdmin: admin.isMainAdmin
        });
    }
    catch (error) {
        console.error('Error creating admin user:', error);
    }
    finally {
        await database_1.default.close();
    }
}
createAdmin();
