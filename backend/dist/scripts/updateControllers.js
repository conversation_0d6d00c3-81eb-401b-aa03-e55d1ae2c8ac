"use strict";
/**
 * Controller Update Script
 *
 * This script updates the application to use Prisma controllers and routes exclusively.
 * It renames Prisma-specific files and updates imports in other files.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateControllers = updateControllers;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
// Paths
const BACKEND_DIR = path_1.default.join(__dirname, '../..');
const SRC_DIR = path_1.default.join(BACKEND_DIR, 'src');
const CONTROLLERS_DIR = path_1.default.join(SRC_DIR, 'controllers');
const ROUTES_DIR = path_1.default.join(SRC_DIR, 'routes');
const INDEX_FILE = path_1.default.join(SRC_DIR, 'index.ts');
// Log file
const LOG_FILE = path_1.default.join(BACKEND_DIR, 'logs', `controller-update-${new Date().toISOString().replace(/:/g, '-')}.log`);
/**
 * Log message to console and file
 */
function log(message) {
    const timestampedMessage = `[${new Date().toISOString()}] ${message}`;
    console.log(timestampedMessage);
    // Create logs directory if it doesn't exist
    const logsDir = path_1.default.dirname(LOG_FILE);
    if (!fs_1.default.existsSync(logsDir)) {
        fs_1.default.mkdirSync(logsDir, { recursive: true });
    }
    fs_1.default.appendFileSync(LOG_FILE, timestampedMessage + '\n');
}
/**
 * Rename Prisma-specific files by removing the .prisma suffix
 */
function renamePrismaFiles() {
    log('Renaming Prisma-specific files...');
    // Find all .prisma.ts files in controllers and routes directories
    const controllerFiles = fs_1.default.readdirSync(CONTROLLERS_DIR);
    const routeFiles = fs_1.default.readdirSync(ROUTES_DIR);
    // Process controller files
    for (const file of controllerFiles) {
        if (file.includes('.prisma.ts')) {
            const oldPath = path_1.default.join(CONTROLLERS_DIR, file);
            const newPath = path_1.default.join(CONTROLLERS_DIR, file.replace('.prisma.ts', '.ts'));
            // Check if there's already a non-prisma version
            if (fs_1.default.existsSync(newPath)) {
                // Backup the old file
                const backupPath = path_1.default.join(CONTROLLERS_DIR, `${file.replace('.prisma.ts', '.sequelize.ts')}`);
                log(`Backing up ${newPath} to ${backupPath}`);
                fs_1.default.renameSync(newPath, backupPath);
            }
            // Rename the prisma file
            log(`Renaming ${oldPath} to ${newPath}`);
            fs_1.default.renameSync(oldPath, newPath);
        }
    }
    // Process route files
    for (const file of routeFiles) {
        if (file.includes('.prisma.ts')) {
            const oldPath = path_1.default.join(ROUTES_DIR, file);
            const newPath = path_1.default.join(ROUTES_DIR, file.replace('.prisma.ts', '.ts'));
            // Check if there's already a non-prisma version
            if (fs_1.default.existsSync(newPath)) {
                // Backup the old file
                const backupPath = path_1.default.join(ROUTES_DIR, `${file.replace('.prisma.ts', '.sequelize.ts')}`);
                log(`Backing up ${newPath} to ${backupPath}`);
                fs_1.default.renameSync(newPath, backupPath);
            }
            // Rename the prisma file
            log(`Renaming ${oldPath} to ${newPath}`);
            fs_1.default.renameSync(oldPath, newPath);
        }
    }
    log('File renaming completed.');
}
/**
 * Update imports in the main index.ts file
 */
function updateMainImports() {
    log('Updating imports in main index.ts file...');
    if (!fs_1.default.existsSync(INDEX_FILE)) {
        log(`ERROR: Main index file not found at ${INDEX_FILE}`);
        return;
    }
    let content = fs_1.default.readFileSync(INDEX_FILE, 'utf8');
    // Replace imports that reference .prisma files
    content = content.replace(/import\s+(\w+)\s+from\s+['"](.+)\.prisma['"]/g, 'import $1 from "$2"');
    // Write updated content back to file
    fs_1.default.writeFileSync(INDEX_FILE, content);
    log('Main imports updated.');
}
/**
 * Update all files that import controllers or routes
 */
function updateAllImports() {
    log('Updating imports in all files...');
    // Get all .ts files in the src directory and subdirectories
    function getAllFiles(dir) {
        const files = [];
        const items = fs_1.default.readdirSync(dir);
        for (const item of items) {
            const fullPath = path_1.default.join(dir, item);
            const stat = fs_1.default.statSync(fullPath);
            if (stat.isDirectory()) {
                files.push(...getAllFiles(fullPath));
            }
            else if (item.endsWith('.ts')) {
                files.push(fullPath);
            }
        }
        return files;
    }
    const allFiles = getAllFiles(SRC_DIR);
    let updatedCount = 0;
    for (const file of allFiles) {
        let content = fs_1.default.readFileSync(file, 'utf8');
        const originalContent = content;
        // Replace imports that reference .prisma files
        content = content.replace(/import\s+(\{[^}]+\}|\w+)\s+from\s+['"](.+)\.prisma['"]/g, 'import $1 from "$2"');
        // If content changed, write it back
        if (content !== originalContent) {
            fs_1.default.writeFileSync(file, content);
            log(`Updated imports in ${file}`);
            updatedCount++;
        }
    }
    log(`Updated imports in ${updatedCount} files.`);
}
/**
 * Run the controller update process
 */
async function updateControllers() {
    try {
        log('Starting controller update process...');
        // Step 1: Rename Prisma files
        renamePrismaFiles();
        // Step 2: Update imports in main index.ts
        updateMainImports();
        // Step 3: Update imports in all files
        updateAllImports();
        log('Controller update process completed successfully.');
    }
    catch (error) {
        log(`ERROR: Controller update failed: ${error}`);
    }
}
// Run the update if this script is executed directly
if (require.main === module) {
    updateControllers()
        .then(() => {
        log('Update script execution completed');
        process.exit(0);
    })
        .catch((error) => {
        log(`Unhandled error in update script: ${error}`);
        process.exit(1);
    });
}
