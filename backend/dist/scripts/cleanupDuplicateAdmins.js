"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = __importDefault(require("../config/database"));
const Admin_1 = __importDefault(require("../models/Admin"));
async function cleanupDuplicateAdmins() {
    try {
        // Ensure database connection
        await database_1.default.authenticate();
        console.log('Database connection established successfully.');
        // Find all admins
        const admins = await Admin_1.default.findAll();
        console.log(`Found ${admins.length} admin(s) in the database.`);
        // Check for duplicate main admins
        const mainAdmins = admins.filter(admin => admin.isMainAdmin);
        console.log(`Found ${mainAdmins.length} main admin(s).`);
        if (mainAdmins.length > 1) {
            console.log('Multiple main admins found. Keeping only the first one...');
            // Keep the first main admin and remove the rest
            const adminToKeep = mainAdmins[0];
            console.log(`Keeping main admin: ${adminToKeep.name} (${adminToKeep.email})`);
            // Remove other main admins
            for (let i = 1; i < mainAdmins.length; i++) {
                const adminToRemove = mainAdmins[i];
                console.log(`Removing duplicate main admin: ${adminToRemove.name} (${adminToRemove.email})`);
                await adminToRemove.destroy();
            }
            console.log('Duplicate main admins removed successfully.');
        }
        else if (mainAdmins.length === 0) {
            console.log('No main admin found. Creating one...');
            // Create a main admin
            const hashedPassword = await require('bcryptjs').hash('admin123', 10);
            await Admin_1.default.create({
                name: 'Main Admin',
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'super_admin',
                privileges: ['all'],
                isMainAdmin: true,
            });
            console.log('Main admin created successfully.');
        }
        else {
            console.log('No duplicate main admins found.');
        }
        // Check for duplicate email addresses
        const emailCounts = {};
        admins.forEach(admin => {
            if (!emailCounts[admin.email]) {
                emailCounts[admin.email] = [];
            }
            emailCounts[admin.email].push(admin);
        });
        // Find emails with multiple accounts
        const duplicateEmails = Object.keys(emailCounts).filter(email => emailCounts[email].length > 1);
        if (duplicateEmails.length > 0) {
            console.log(`Found ${duplicateEmails.length} email(s) with duplicate accounts.`);
            // Process each duplicate email
            for (const email of duplicateEmails) {
                const duplicates = emailCounts[email];
                console.log(`Email ${email} has ${duplicates.length} accounts.`);
                // Keep the first account and remove the rest
                const accountToKeep = duplicates[0];
                console.log(`Keeping account: ${accountToKeep.name} (ID: ${accountToKeep.id})`);
                // Remove other accounts
                for (let i = 1; i < duplicates.length; i++) {
                    const accountToRemove = duplicates[i];
                    console.log(`Removing duplicate account: ${accountToRemove.name} (ID: ${accountToRemove.id})`);
                    await accountToRemove.destroy();
                }
            }
            console.log('Duplicate accounts removed successfully.');
        }
        else {
            console.log('No duplicate email accounts found.');
        }
        // Final check
        const finalAdmins = await Admin_1.default.findAll();
        console.log(`Final admin count: ${finalAdmins.length}`);
        finalAdmins.forEach(admin => {
            console.log(`- ${admin.name} (${admin.email}), isMainAdmin: ${admin.isMainAdmin}`);
        });
    }
    catch (error) {
        console.error('Error cleaning up duplicate admins:', error);
    }
    finally {
        await database_1.default.close();
    }
}
// Run the cleanup
cleanupDuplicateAdmins();
