"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = __importDefault(require("../config/database"));
const Admin_1 = __importDefault(require("../models/Admin"));
const User_1 = __importDefault(require("../models/User"));
const scholarship_model_1 = require("../models/scholarship.model");
async function checkDatabase() {
    try {
        // Ensure database connection
        await database_1.default.authenticate();
        console.log('Database connection established successfully.');
        // Check Admin table
        const admins = await Admin_1.default.findAll();
        console.log(`Found ${admins.length} admin(s):`);
        admins.forEach(admin => {
            console.log(`- ${admin.name} (${admin.email}), isMainAdmin: ${admin.isMainAdmin}`);
        });
        // Check User table
        const users = await User_1.default.findAll();
        console.log(`\nFound ${users.length} user(s):`);
        users.forEach(user => {
            console.log(`- ${user.name} (${user.email}), role: ${user.role}`);
        });
        // Check Scholarship table
        const scholarships = await scholarship_model_1.Scholarship.findAll();
        console.log(`\nFound ${scholarships.length} scholarship(s):`);
        scholarships.forEach(scholarship => {
            console.log(`- ${scholarship.title}, createdBy: ${scholarship.createdBy}`);
        });
    }
    catch (error) {
        console.error('Error checking database:', error);
    }
    finally {
        await database_1.default.close();
    }
}
checkDatabase();
