"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_fetch_1 = __importDefault(require("node-fetch"));
async function testLogin() {
    try {
        console.log('Testing admin login API...');
        const response = await (0, node_fetch_1.default)('http://localhost:5000/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123',
            }),
        });
        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Response data:', JSON.stringify(data, null, 2));
        if (response.ok) {
            console.log('Login successful!');
        }
        else {
            console.log('Login failed!');
        }
    }
    catch (error) {
        console.error('Error testing login:', error);
    }
}
testLogin();
