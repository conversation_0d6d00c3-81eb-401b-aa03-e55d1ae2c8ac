"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const userController = __importStar(require("../../controllers/user.controller"));
const test_utils_1 = require("../helpers/test-utils");
// Mock PrismaClient
globals_1.jest.mock('@prisma/client', () => {
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => (0, test_utils_1.mockPrismaClient)()),
    };
});
// Mock bcryptjs
globals_1.jest.mock('bcryptjs', () => ({
    hash: globals_1.jest.fn().mockResolvedValue('hashed_password'),
    compare: globals_1.jest.fn().mockResolvedValue(true),
}));
// Mock jsonwebtoken
globals_1.jest.mock('jsonwebtoken', () => ({
    sign: globals_1.jest.fn().mockReturnValue('mock_token'),
}));
(0, globals_1.describe)('User Controller', () => {
    let mockReq;
    let mockRes;
    let prisma;
    let testUser;
    let adminUser;
    (0, globals_1.beforeEach)(() => {
        mockReq = (0, test_utils_1.mockRequest)();
        mockRes = (0, test_utils_1.mockResponse)();
        prisma = (0, test_utils_1.mockPrismaClient)();
        // Create test users
        testUser = {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            password: 'hashed_password',
            role: 'user',
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        adminUser = {
            id: 2,
            name: 'Admin User',
            email: '<EMAIL>',
            password: 'hashed_password',
            role: 'admin',
            isMainAdmin: true,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        // Reset mock implementations for each test
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('getAllUsers', () => {
        (0, globals_1.it)('should return all users (admin only)', async () => {
            // Mock data
            const mockUsers = [testUser, adminUser];
            // Setup mocks
            mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
            prisma.user.findMany.mockResolvedValue(mockUsers);
            // Call the controller
            await userController.getAllUsers(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(mockUsers);
        });
        (0, globals_1.it)('should handle errors', async () => {
            // Mock error
            const error = new Error('Database error');
            mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
            prisma.user.findMany.mockRejectedValue(error);
            // Call the controller
            await userController.getAllUsers(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(500);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Error getting users' });
        });
    });
    (0, globals_1.describe)('getUserById', () => {
        (0, globals_1.it)('should return a user by ID', async () => {
            // Setup mocks
            mockReq.params = { id: '1' };
            prisma.user.findUnique.mockResolvedValue(testUser);
            // Call the controller
            await userController.getUserById(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(testUser);
        });
        (0, globals_1.it)('should return 404 if user not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            prisma.user.findUnique.mockResolvedValue(null);
            // Call the controller
            await userController.getUserById(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'User not found' });
        });
    });
    (0, globals_1.describe)('createUser', () => {
        (0, globals_1.it)('should create a new user', async () => {
            // Mock data
            const userData = {
                name: 'New User',
                email: '<EMAIL>',
                password: 'Password123!',
            };
            const createdUser = {
                id: 3,
                name: 'New User',
                email: '<EMAIL>',
                role: 'user',
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            // Setup mocks
            mockReq.body = userData;
            prisma.user.findUnique.mockResolvedValue(null);
            prisma.user.create.mockResolvedValue(createdUser);
            // Call the controller
            await userController.createUser(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(201);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                user: {
                    id: createdUser.id,
                    name: createdUser.name,
                    email: createdUser.email,
                    role: createdUser.role,
                    createdAt: createdUser.createdAt,
                    updatedAt: createdUser.updatedAt
                },
                token: 'mock_token'
            });
        });
        (0, globals_1.it)('should return 409 if user with email already exists', async () => {
            // Setup mocks
            mockReq.body = {
                name: 'Existing User',
                email: '<EMAIL>',
                password: 'Password123!',
            };
            prisma.user.findUnique.mockResolvedValue(testUser);
            // Call the controller
            await userController.createUser(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(409);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'User with this email already exists' });
        });
    });
    (0, globals_1.describe)('deleteUser', () => {
        (0, globals_1.it)('should delete a user (admin only)', async () => {
            // Setup mocks
            mockReq.params = { id: '1' };
            mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
            prisma.user.findUnique.mockResolvedValue(testUser);
            prisma.user.delete.mockResolvedValue(testUser);
            // Call the controller
            await userController.deleteUser(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'User deleted successfully' });
        });
        (0, globals_1.it)('should return 404 if user not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
            prisma.user.findUnique.mockResolvedValue(null);
            // Call the controller
            await userController.deleteUser(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'User not found' });
        });
    });
});
