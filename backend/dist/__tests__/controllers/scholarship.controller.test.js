"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const client_1 = require("@prisma/client");
const scholarshipController = __importStar(require("../../controllers/scholarship.controller"));
// Mock PrismaClient
globals_1.jest.mock('@prisma/client', () => {
    const mockScholarship = {
        findMany: globals_1.jest.fn(),
        findUnique: globals_1.jest.fn(),
        findFirst: globals_1.jest.fn(),
        create: globals_1.jest.fn(),
        update: globals_1.jest.fn(),
        delete: globals_1.jest.fn(),
    };
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => ({
            scholarship: mockScholarship,
            $connect: globals_1.jest.fn(),
            $disconnect: globals_1.jest.fn(),
        })),
    };
});
(0, globals_1.describe)('Scholarship Controller', () => {
    let mockReq;
    let mockRes;
    let prisma;
    (0, globals_1.beforeEach)(() => {
        mockReq = {
            params: {},
            query: {},
            body: {},
            user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true },
        };
        mockRes = {
            status: globals_1.jest.fn().mockReturnThis(),
            json: globals_1.jest.fn(),
        };
        prisma = new client_1.PrismaClient();
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('getScholarships', () => {
        (0, globals_1.it)('should return scholarships with pagination', async () => {
            const mockScholarships = [
                { id: 1, title: 'Scholarship 1', description: 'Description 1', createdBy: 1 },
                { id: 2, title: 'Scholarship 2', description: 'Description 2', createdBy: 1 },
            ];
            const mockCount = 2;
            prisma.scholarship.findMany.mockResolvedValue(mockScholarships);
            prisma.scholarship.findMany.mockResolvedValueOnce(mockCount);
            await scholarshipController.getScholarships(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                scholarships: mockScholarships,
                pagination: {
                    total: mockCount,
                    page: 1,
                    limit: 10,
                    pages: 1,
                },
            });
        });
        (0, globals_1.it)('should handle errors', async () => {
            const error = new Error('Database error');
            prisma.scholarship.findMany.mockRejectedValue(error);
            await scholarshipController.getScholarships(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(500);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Server error' });
        });
    });
    (0, globals_1.describe)('getScholarshipById', () => {
        (0, globals_1.it)('should return a scholarship by ID', async () => {
            const mockScholarship = {
                id: 1,
                title: 'Scholarship 1',
                description: 'Description 1',
                createdBy: 1,
            };
            mockReq.params = { id: '1' };
            prisma.scholarship.findUnique.mockResolvedValue(mockScholarship);
            await scholarshipController.getScholarshipById(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(mockScholarship);
        });
        (0, globals_1.it)('should return 404 if scholarship not found', async () => {
            mockReq.params = { id: '999' };
            prisma.scholarship.findUnique.mockResolvedValue(null);
            await scholarshipController.getScholarshipById(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
        });
    });
    (0, globals_1.describe)('createScholarship', () => {
        (0, globals_1.it)('should create a new scholarship', async () => {
            const newScholarship = {
                title: 'New Scholarship',
                description: 'New Description',
                deadline: new Date(),
                createdBy: 1,
            };
            mockReq.body = newScholarship;
            const createdScholarship = {
                id: 1,
                ...newScholarship,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            prisma.scholarship.create.mockResolvedValue(createdScholarship);
            await scholarshipController.createScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(201);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(createdScholarship);
        });
    });
    (0, globals_1.describe)('updateScholarship', () => {
        (0, globals_1.it)('should update an existing scholarship', async () => {
            const updateData = {
                title: 'Updated Scholarship',
                description: 'Updated Description',
            };
            mockReq.params = { id: '1' };
            mockReq.body = updateData;
            const updatedScholarship = {
                id: 1,
                ...updateData,
                deadline: new Date(),
                createdBy: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            prisma.scholarship.findUnique.mockResolvedValue({ id: 1 });
            prisma.scholarship.update.mockResolvedValue(updatedScholarship);
            await scholarshipController.updateScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(updatedScholarship);
        });
        (0, globals_1.it)('should return 404 if scholarship to update not found', async () => {
            mockReq.params = { id: '999' };
            mockReq.body = { title: 'Updated Scholarship' };
            prisma.scholarship.findUnique.mockResolvedValue(null);
            await scholarshipController.updateScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
        });
    });
    (0, globals_1.describe)('deleteScholarship', () => {
        (0, globals_1.it)('should delete an existing scholarship', async () => {
            mockReq.params = { id: '1' };
            prisma.scholarship.findUnique.mockResolvedValue({ id: 1 });
            prisma.scholarship.delete.mockResolvedValue({ id: 1 });
            await scholarshipController.deleteScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship deleted successfully' });
        });
        (0, globals_1.it)('should return 404 if scholarship to delete not found', async () => {
            mockReq.params = { id: '999' };
            prisma.scholarship.findUnique.mockResolvedValue(null);
            await scholarshipController.deleteScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
        });
    });
});
