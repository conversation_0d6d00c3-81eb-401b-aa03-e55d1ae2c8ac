"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
// Create mock functions before importing the controller
const mockPrisma = {
    scholarship: {
        findMany: globals_1.jest.fn(),
        findUnique: globals_1.jest.fn(),
        create: globals_1.jest.fn(),
        update: globals_1.jest.fn(),
        delete: globals_1.jest.fn(),
        count: globals_1.jest.fn(),
    },
    $connect: globals_1.jest.fn(),
    $disconnect: globals_1.jest.fn(),
};
// Mock PrismaClient before importing the controller
globals_1.jest.mock('@prisma/client', () => ({
    PrismaClient: globals_1.jest.fn().mockImplementation(() => mockPrisma),
}));
// Now import the controller
const scholarshipController = __importStar(require("../../controllers/scholarship.controller"));
(0, globals_1.describe)('Scholarship Controller', () => {
    let mockReq;
    let mockRes;
    (0, globals_1.beforeEach)(() => {
        // Create mock request and response
        mockReq = {
            params: {},
            query: {},
            body: {},
            user: { id: 1, email: '<EMAIL>', role: 'user', isMainAdmin: false },
        };
        mockRes = {
            status: globals_1.jest.fn().mockReturnThis(),
            json: globals_1.jest.fn().mockReturnThis(),
            send: globals_1.jest.fn().mockReturnThis(),
        };
        // Reset mock implementations for each test
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('getScholarships', () => {
        (0, globals_1.it)('should return all scholarships with pagination', async () => {
            // Mock data
            const mockScholarships = [
                {
                    id: 1,
                    title: 'Scholarship 1',
                    description: 'Description 1',
                    createdBy: 1,
                },
                {
                    id: 2,
                    title: 'Scholarship 2',
                    description: 'Description 2',
                    createdBy: 1,
                },
            ];
            const mockCount = 2;
            // Setup mocks
            mockPrisma.scholarship.findMany.mockResolvedValue(mockScholarships);
            mockPrisma.scholarship.count.mockResolvedValue(mockCount);
            // Call the controller
            await scholarshipController.getScholarships(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                scholarships: mockScholarships,
                pagination: {
                    total: mockCount,
                    page: 1,
                    limit: 10,
                    totalPages: 1,
                },
            });
        });
        (0, globals_1.it)('should handle errors', async () => {
            // Mock error
            const error = new Error('Database error');
            mockPrisma.scholarship.findMany.mockRejectedValue(error);
            // Call the controller
            await scholarshipController.getScholarships(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(500);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Server error' });
        });
    });
    (0, globals_1.describe)('getScholarshipById', () => {
        (0, globals_1.it)('should return a scholarship by ID', async () => {
            // Mock data
            const mockScholarship = {
                id: 1,
                title: 'Scholarship 1',
                description: 'Description 1',
                createdBy: 1,
            };
            // Setup mocks
            mockReq.params = { id: '1' };
            mockPrisma.scholarship.findUnique.mockResolvedValue(mockScholarship);
            // Call the controller
            await scholarshipController.getScholarshipById(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(mockScholarship);
        });
        (0, globals_1.it)('should return 404 if scholarship not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            mockPrisma.scholarship.findUnique.mockResolvedValue(null);
            // Call the controller
            await scholarshipController.getScholarshipById(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
        });
    });
    (0, globals_1.describe)('createScholarship', () => {
        (0, globals_1.it)('should create a new scholarship', async () => {
            // Mock data
            const scholarshipData = {
                title: 'New Scholarship',
                description: 'New Description',
                deadline: new Date(),
                level: 'Undergraduate',
                country: 'United States',
            };
            const createdScholarship = {
                id: 1,
                ...scholarshipData,
                createdBy: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            // Setup mocks
            mockReq.body = scholarshipData;
            mockReq.user = { id: 1, email: '<EMAIL>', role: 'user', isMainAdmin: false };
            mockPrisma.scholarship.create.mockResolvedValue(createdScholarship);
            // Call the controller
            await scholarshipController.createScholarship(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(201);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(createdScholarship);
        });
    });
    (0, globals_1.describe)('updateScholarship', () => {
        (0, globals_1.it)('should update a scholarship', async () => {
            // Mock data
            const updateData = {
                title: 'Updated Scholarship',
                description: 'Updated Description',
            };
            const updatedScholarship = {
                id: 1,
                title: 'Updated Scholarship',
                description: 'Updated Description',
                deadline: new Date(),
                createdBy: 1,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            // Setup mocks
            mockReq.params = { id: '1' };
            mockReq.body = updateData;
            mockPrisma.scholarship.findUnique.mockResolvedValue({ id: 1 });
            mockPrisma.scholarship.update.mockResolvedValue(updatedScholarship);
            // Call the controller
            await scholarshipController.updateScholarship(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(updatedScholarship);
        });
        (0, globals_1.it)('should return 404 if scholarship not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            mockReq.body = { title: 'Updated Scholarship' };
            mockPrisma.scholarship.findUnique.mockResolvedValue(null);
            // Call the controller
            await scholarshipController.updateScholarship(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
        });
    });
    (0, globals_1.describe)('deleteScholarship', () => {
        (0, globals_1.it)('should delete a scholarship', async () => {
            // Setup mocks
            mockReq.params = { id: '1' };
            mockPrisma.scholarship.findUnique.mockResolvedValue({ id: 1 });
            mockPrisma.scholarship.delete.mockResolvedValue({ id: 1 });
            // Call the controller
            await scholarshipController.deleteScholarship(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship deleted successfully' });
        });
        (0, globals_1.it)('should return 404 if scholarship not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            mockPrisma.scholarship.findUnique.mockResolvedValue(null);
            // Call the controller
            await scholarshipController.deleteScholarship(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
        });
    });
});
