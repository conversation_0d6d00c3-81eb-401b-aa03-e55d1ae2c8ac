"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
// Mock dependencies
globals_1.jest.mock('@prisma/client', () => {
    const mockAdmin = {
        findMany: globals_1.jest.fn(),
        findUnique: globals_1.jest.fn(),
        create: globals_1.jest.fn(),
        update: globals_1.jest.fn(),
        delete: globals_1.jest.fn(),
    };
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => ({
            admin: mockAdmin,
            $connect: globals_1.jest.fn(),
            $disconnect: globals_1.jest.fn(),
        })),
    };
});
globals_1.jest.mock('bcryptjs', () => ({
    hash: globals_1.jest.fn().mockResolvedValue('hashed_password'),
    compare: globals_1.jest.fn().mockResolvedValue(true),
}));
globals_1.jest.mock('jsonwebtoken', () => ({
    sign: globals_1.jest.fn().mockReturnValue('mock_token'),
}));
// Import the controller after mocking dependencies
const adminController = __importStar(require("../../controllers/admin.controller"));
(0, globals_1.describe)('Admin Controller', () => {
    let mockReq;
    let mockRes;
    let prisma;
    (0, globals_1.beforeEach)(() => {
        mockReq = {
            params: {},
            query: {},
            body: {},
            user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true },
        };
        mockRes = {
            status: globals_1.jest.fn().mockReturnThis(),
            json: globals_1.jest.fn(),
        };
        prisma = new client_1.PrismaClient();
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('getAllAdmins', () => {
        (0, globals_1.it)('should return all admins', async () => {
            const mockAdmins = [
                { id: 1, name: 'Admin 1', email: '<EMAIL>', role: 'admin' },
                { id: 2, name: 'Admin 2', email: '<EMAIL>', role: 'admin' },
            ];
            prisma.admin.findMany.mockResolvedValue(mockAdmins);
            await adminController.getAllAdmins(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(mockAdmins);
        });
        (0, globals_1.it)('should handle errors', async () => {
            const error = new Error('Database error');
            prisma.admin.findMany.mockRejectedValue(error);
            await adminController.getAllAdmins(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(500);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Server error' });
        });
    });
    (0, globals_1.describe)('createAdmin', () => {
        (0, globals_1.it)('should create a new admin', async () => {
            const newAdmin = {
                name: 'New Admin',
                email: '<EMAIL>',
                password: 'password123',
                role: 'admin',
            };
            mockReq.body = newAdmin;
            const createdAdmin = {
                id: 3,
                name: newAdmin.name,
                email: newAdmin.email,
                role: newAdmin.role,
                isMainAdmin: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            prisma.admin.findUnique.mockResolvedValue(null);
            prisma.admin.create.mockResolvedValue(createdAdmin);
            await adminController.createAdmin(mockReq, mockRes);
            (0, globals_1.expect)(bcryptjs_1.default.hash).toHaveBeenCalledWith(newAdmin.password, 10);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(201);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(globals_1.expect.objectContaining({
                id: createdAdmin.id,
                name: createdAdmin.name,
                email: createdAdmin.email,
            }));
        });
        (0, globals_1.it)('should return 400 if admin already exists', async () => {
            mockReq.body = {
                name: 'Existing Admin',
                email: '<EMAIL>',
                password: 'password123',
                role: 'admin',
            };
            prisma.admin.findUnique.mockResolvedValue({ id: 1 });
            await adminController.createAdmin(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin already exists with this email' });
        });
    });
    (0, globals_1.describe)('updateAdmin', () => {
        (0, globals_1.it)('should update an existing admin', async () => {
            const updateData = {
                name: 'Updated Admin',
            };
            mockReq.params = { id: '1' };
            mockReq.body = updateData;
            const existingAdmin = {
                id: 1,
                name: 'Admin 1',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: false,
            };
            const updatedAdmin = {
                ...existingAdmin,
                name: updateData.name,
                updatedAt: new Date(),
            };
            prisma.admin.findUnique.mockResolvedValue(existingAdmin);
            prisma.admin.update.mockResolvedValue(updatedAdmin);
            await adminController.updateAdmin(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(updatedAdmin);
        });
        (0, globals_1.it)('should return 404 if admin to update not found', async () => {
            mockReq.params = { id: '999' };
            mockReq.body = { name: 'Updated Admin' };
            prisma.admin.findUnique.mockResolvedValue(null);
            await adminController.updateAdmin(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
        });
    });
    (0, globals_1.describe)('deleteAdmin', () => {
        (0, globals_1.it)('should delete an existing admin', async () => {
            mockReq.params = { id: '2' };
            const adminToDelete = {
                id: 2,
                name: 'Admin 2',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: false,
            };
            prisma.admin.findUnique.mockResolvedValue(adminToDelete);
            prisma.admin.delete.mockResolvedValue(adminToDelete);
            await adminController.deleteAdmin(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(200);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin deleted successfully' });
        });
        (0, globals_1.it)('should return 404 if admin to delete not found', async () => {
            mockReq.params = { id: '999' };
            prisma.admin.findUnique.mockResolvedValue(null);
            await adminController.deleteAdmin(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
        });
        (0, globals_1.it)('should not allow deleting the main admin', async () => {
            mockReq.params = { id: '1' };
            const mainAdmin = {
                id: 1,
                name: 'Main Admin',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: true,
            };
            prisma.admin.findUnique.mockResolvedValue(mainAdmin);
            await adminController.deleteAdmin(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(403);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Cannot delete the main admin account' });
        });
    });
});
