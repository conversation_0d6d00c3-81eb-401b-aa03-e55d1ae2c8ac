"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const user_controller_1 = require("../../controllers/user.controller");
const user_model_1 = __importDefault(require("../../models/user.model"));
const auth_middleware_1 = require("../../middleware/auth.middleware");
const app = (0, express_1.default)();
app.use(express_1.default.json());
// Mock auth middleware
globals_1.jest.mock('../../middleware/auth.middleware', () => ({
    auth: (req, res, next) => {
        req.user = req.headers.user ? JSON.parse(req.headers.user) : null;
        next();
    },
    adminAuth: (req, res, next) => next(),
}));
// Routes
app.put('/profile', auth_middleware_1.auth, user_controller_1.updateProfile);
app.put('/change-password', auth_middleware_1.auth, user_controller_1.changePassword);
app.get('/users', auth_middleware_1.auth, auth_middleware_1.adminAuth, user_controller_1.getUsers);
app.delete('/users/:id', auth_middleware_1.auth, auth_middleware_1.adminAuth, user_controller_1.deleteUser);
(0, globals_1.describe)('User Controller', () => {
    let testUser;
    let adminUser;
    (0, globals_1.beforeEach)(async () => {
        // Create test user
        testUser = await user_model_1.default.create({
            name: 'Test User',
            email: '<EMAIL>',
            password: 'password123',
            role: 'user',
        });
        // Create admin user
        adminUser = await user_model_1.default.create({
            name: 'Admin User',
            email: '<EMAIL>',
            password: 'admin123',
            role: 'admin',
        });
    });
    (0, globals_1.describe)('updateProfile', () => {
        (0, globals_1.it)('should update user profile', async () => {
            const response = await (0, supertest_1.default)(app)
                .put('/profile')
                .send({
                name: 'Updated Name',
                email: '<EMAIL>',
            })
                .set('user', JSON.stringify({ id: testUser._id.toString() }));
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body.user.name).toBe('Updated Name');
            (0, globals_1.expect)(response.body.user.email).toBe('<EMAIL>');
        });
    });
    (0, globals_1.describe)('changePassword', () => {
        (0, globals_1.it)('should change user password', async () => {
            const response = await (0, supertest_1.default)(app)
                .put('/change-password')
                .send({
                currentPassword: 'password123',
                newPassword: 'newpassword123',
            })
                .set('user', JSON.stringify({ id: testUser._id.toString() }));
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body.message).toBe('Password changed successfully');
        });
        (0, globals_1.it)('should fail with incorrect current password', async () => {
            const response = await (0, supertest_1.default)(app)
                .put('/change-password')
                .send({
                currentPassword: 'wrongpassword',
                newPassword: 'newpassword123',
            })
                .set('user', JSON.stringify({ id: testUser._id.toString() }));
            (0, globals_1.expect)(response.status).toBe(400);
            (0, globals_1.expect)(response.body.message).toBe('Current password is incorrect');
        });
    });
    (0, globals_1.describe)('getUsers', () => {
        (0, globals_1.it)('should get all users', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/users')
                .set('user', JSON.stringify({ id: adminUser._id.toString(), role: 'admin' }));
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body).toHaveLength(2);
            (0, globals_1.expect)(response.body[0]).not.toHaveProperty('password');
        });
    });
    (0, globals_1.describe)('deleteUser', () => {
        (0, globals_1.it)('should delete a user', async () => {
            const response = await (0, supertest_1.default)(app)
                .delete(`/users/${testUser._id}`)
                .set('user', JSON.stringify({ id: adminUser._id.toString(), role: 'admin' }));
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body.message).toBe('User deleted successfully');
            const deletedUser = await user_model_1.default.findById(testUser._id);
            (0, globals_1.expect)(deletedUser).toBeNull();
        });
    });
});
