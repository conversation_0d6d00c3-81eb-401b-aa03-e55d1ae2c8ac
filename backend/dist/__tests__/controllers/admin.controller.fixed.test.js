"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const adminController = __importStar(require("../../controllers/admin.controller"));
const test_utils_1 = require("../helpers/test-utils");
// Mock PrismaClient
globals_1.jest.mock('@prisma/client', () => {
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => (0, test_utils_1.mockPrismaClient)()),
    };
});
// Mock bcryptjs
globals_1.jest.mock('bcryptjs', () => ({
    hash: globals_1.jest.fn().mockResolvedValue('hashed_password'),
    compare: globals_1.jest.fn().mockResolvedValue(true),
}));
(0, globals_1.describe)('Admin Controller', () => {
    let mockReq;
    let mockRes;
    let prisma;
    (0, globals_1.beforeEach)(() => {
        mockReq = (0, test_utils_1.mockRequest)({
            user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true }
        });
        mockRes = (0, test_utils_1.mockResponse)();
        prisma = (0, test_utils_1.mockPrismaClient)();
        // Reset mock implementations for each test
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('getAdmins', () => {
        (0, globals_1.it)('should return all admins', async () => {
            // Mock data
            const mockAdmins = [
                {
                    id: 1,
                    name: 'Admin 1',
                    email: '<EMAIL>',
                    role: 'admin',
                },
                {
                    id: 2,
                    name: 'Admin 2',
                    email: '<EMAIL>',
                    role: 'admin',
                },
            ];
            // Setup mocks
            prisma.admin.findMany.mockResolvedValue(mockAdmins);
            // Call the controller
            await adminController.getAdmins(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(mockAdmins);
        });
        (0, globals_1.it)('should handle errors', async () => {
            // Mock error
            const error = new Error('Database error');
            prisma.admin.findMany.mockRejectedValue(error);
            // Call the controller
            await adminController.getAdmins(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(500);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Error getting admins' });
        });
    });
    (0, globals_1.describe)('createAdmin', () => {
        (0, globals_1.it)('should create a new admin', async () => {
            // Mock data
            const adminData = {
                name: 'New Admin',
                email: '<EMAIL>',
                password: 'Password123!',
                privileges: ['users', 'scholarships'],
                isMainAdmin: false,
            };
            const createdAdmin = {
                id: 3,
                name: 'New Admin',
                email: '<EMAIL>',
                role: 'admin',
                privileges: JSON.stringify(['users', 'scholarships']),
                isMainAdmin: false,
                twoFactorEnabled: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            // Setup mocks
            mockReq.body = adminData;
            prisma.admin.findUnique.mockResolvedValue(null);
            prisma.admin.create.mockResolvedValue(createdAdmin);
            // Call the controller
            await adminController.createAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(201);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                id: createdAdmin.id,
                name: createdAdmin.name,
                email: createdAdmin.email,
                role: createdAdmin.role,
                privileges: createdAdmin.privileges,
                isMainAdmin: createdAdmin.isMainAdmin,
                twoFactorEnabled: createdAdmin.twoFactorEnabled,
                createdAt: createdAdmin.createdAt,
                updatedAt: createdAdmin.updatedAt
            });
        });
        (0, globals_1.it)('should return 409 if admin with email already exists', async () => {
            // Setup mocks
            mockReq.body = {
                name: 'Existing Admin',
                email: '<EMAIL>',
                password: 'Password123!',
                privileges: ['users'],
            };
            prisma.admin.findUnique.mockResolvedValue({ id: 1 });
            // Call the controller
            await adminController.createAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(409);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin with this email already exists' });
        });
    });
    (0, globals_1.describe)('updateAdmin', () => {
        (0, globals_1.it)('should update an admin', async () => {
            // Mock data
            const updateData = {
                name: 'Updated Admin',
            };
            const existingAdmin = {
                id: 1,
                name: 'Admin 1',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: false,
            };
            const updatedAdmin = {
                id: 1,
                name: 'Updated Admin',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: false,
                updatedAt: new Date(),
            };
            // Setup mocks
            mockReq.params = { id: '1' };
            mockReq.body = updateData;
            prisma.admin.findUnique.mockResolvedValue(existingAdmin);
            prisma.admin.update.mockResolvedValue(updatedAdmin);
            // Call the controller
            await adminController.updateAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith(updatedAdmin);
        });
        (0, globals_1.it)('should return 404 if admin not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            mockReq.body = { name: 'Updated Admin' };
            prisma.admin.findUnique.mockResolvedValue(null);
            // Call the controller
            await adminController.updateAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
        });
    });
    (0, globals_1.describe)('deleteAdmin', () => {
        (0, globals_1.it)('should delete an admin', async () => {
            // Mock data
            const adminToDelete = {
                id: 2,
                name: 'Admin 2',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: false,
            };
            // Setup mocks
            mockReq.params = { id: '2' };
            prisma.admin.findUnique.mockResolvedValue(adminToDelete);
            prisma.admin.delete.mockResolvedValue(adminToDelete);
            // Call the controller
            await adminController.deleteAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin deleted successfully' });
        });
        (0, globals_1.it)('should return 404 if admin not found', async () => {
            // Setup mocks
            mockReq.params = { id: '999' };
            prisma.admin.findUnique.mockResolvedValue(null);
            // Call the controller
            await adminController.deleteAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
        });
        (0, globals_1.it)('should prevent deleting the last main admin', async () => {
            // Mock data
            const mainAdmin = {
                id: 1,
                name: 'Main Admin',
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: true,
            };
            // Setup mocks
            mockReq.params = { id: '1' };
            prisma.admin.findUnique.mockResolvedValue(mainAdmin);
            prisma.admin.count.mockResolvedValue(1);
            // Call the controller
            await adminController.deleteAdmin(mockReq, mockRes);
            // Assertions
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({ message: 'Cannot delete the last main admin' });
        });
    });
});
