"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const express_validator_1 = require("express-validator");
const validation_1 = require("../../middleware/validation");
// Mock express-validator
globals_1.jest.mock('express-validator', () => ({
    validationResult: globals_1.jest.fn(),
    body: globals_1.jest.fn().mockReturnThis(),
    check: globals_1.jest.fn().mockReturnThis(),
    isEmail: globals_1.jest.fn().mockReturnThis(),
    isLength: globals_1.jest.fn().mockReturnThis(),
    isDate: globals_1.jest.fn().mockReturnThis(),
    isURL: globals_1.jest.fn().mockReturnThis(),
    isBoolean: globals_1.jest.fn().mockReturnThis(),
    isIn: globals_1.jest.fn().mockReturnThis(),
    optional: globals_1.jest.fn().mockReturnThis(),
    withMessage: globals_1.jest.fn().mockReturnThis(),
}));
(0, globals_1.describe)('Scholarship Validation Rules', () => {
    let mockReq;
    let mockRes;
    let mockNext;
    (0, globals_1.beforeEach)(() => {
        mockReq = {
            body: {},
        };
        mockRes = {
            status: globals_1.jest.fn().mockReturnThis(),
            json: globals_1.jest.fn(),
        };
        mockNext = globals_1.jest.fn();
        express_validator_1.validationResult.mockImplementation(() => ({
            isEmpty: globals_1.jest.fn().mockReturnValue(true),
            array: globals_1.jest.fn().mockReturnValue([]),
        }));
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('createScholarshipValidation', () => {
        (0, globals_1.it)('should pass validation with valid data', async () => {
            mockReq.body = {
                title: 'Test Scholarship',
                description: 'This is a test scholarship',
                deadline: new Date().toISOString(),
                level: 'Undergraduate',
                country: 'United States',
                isOpen: true,
                financial_benefits_summary: 'Financial benefits',
                eligibility_summary: 'Eligibility criteria',
                scholarship_link: 'https://example.com/scholarship',
            };
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.create) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
            (0, globals_1.expect)(mockRes.status).not.toHaveBeenCalled();
            (0, globals_1.expect)(mockRes.json).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should fail validation with missing required fields', async () => {
            mockReq.body = {
                // Missing title and description
                deadline: new Date().toISOString(),
            };
            // Mock validation errors
            express_validator_1.validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'title', msg: 'Title is required' },
                    { param: 'description', msg: 'Description is required' },
                ]),
            }));
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.create) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            // Validation should fail and return errors
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'title', msg: 'Title is required' },
                    { param: 'description', msg: 'Description is required' },
                ],
            });
        });
        (0, globals_1.it)('should fail validation with invalid date format', async () => {
            mockReq.body = {
                title: 'Test Scholarship',
                description: 'This is a test scholarship',
                deadline: 'invalid-date', // Invalid date format
                level: 'Undergraduate',
                country: 'United States',
            };
            // Mock validation errors
            express_validator_1.validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'deadline', msg: 'Deadline must be a valid date' },
                ]),
            }));
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.create) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            // Validation should fail and return errors
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'deadline', msg: 'Deadline must be a valid date' },
                ],
            });
        });
        (0, globals_1.it)('should fail validation with invalid URL', async () => {
            mockReq.body = {
                title: 'Test Scholarship',
                description: 'This is a test scholarship',
                deadline: new Date().toISOString(),
                level: 'Undergraduate',
                country: 'United States',
                scholarship_link: 'invalid-url', // Invalid URL
            };
            // Mock validation errors
            express_validator_1.validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'scholarship_link', msg: 'Scholarship link must be a valid URL' },
                ]),
            }));
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.create) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            // Validation should fail and return errors
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'scholarship_link', msg: 'Scholarship link must be a valid URL' },
                ],
            });
        });
        (0, globals_1.it)('should fail validation with invalid level value', async () => {
            mockReq.body = {
                title: 'Test Scholarship',
                description: 'This is a test scholarship',
                deadline: new Date().toISOString(),
                level: 'InvalidLevel', // Invalid level
                country: 'United States',
            };
            // Mock validation errors
            express_validator_1.validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'level', msg: 'Level must be one of: Undergraduate, Graduate, PhD' },
                ]),
            }));
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.create) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            // Validation should fail and return errors
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'level', msg: 'Level must be one of: Undergraduate, Graduate, PhD' },
                ],
            });
        });
    });
    (0, globals_1.describe)('updateScholarshipValidation', () => {
        (0, globals_1.it)('should pass validation with valid data', async () => {
            mockReq.body = {
                title: 'Updated Scholarship',
                description: 'This is an updated scholarship',
            };
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.update) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
            (0, globals_1.expect)(mockRes.status).not.toHaveBeenCalled();
            (0, globals_1.expect)(mockRes.json).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should pass validation with partial data', async () => {
            mockReq.body = {
                title: 'Updated Scholarship',
                // Other fields are optional for update
            };
            // Run all validation middleware
            for (const validationFn of validation_1.scholarshipValidationRules.update) {
                await validationFn(mockReq, mockRes, mockNext);
            }
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
            (0, globals_1.expect)(mockRes.status).not.toHaveBeenCalled();
            (0, globals_1.expect)(mockRes.json).not.toHaveBeenCalled();
        });
    });
});
