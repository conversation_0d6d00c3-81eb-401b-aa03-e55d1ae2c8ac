"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const supertest_1 = __importDefault(require("supertest"));
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const app_1 = __importDefault(require("../../app"));
// Mock PrismaClient
globals_1.jest.mock('@prisma/client', () => {
    const mockPrisma = {
        user: {
            findUnique: globals_1.jest.fn(),
            findMany: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
            delete: globals_1.jest.fn(),
        },
        admin: {
            findUnique: globals_1.jest.fn(),
            findMany: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
            delete: globals_1.jest.fn(),
        },
        scholarship: {
            findUnique: globals_1.jest.fn(),
            findMany: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
            delete: globals_1.jest.fn(),
            count: globals_1.jest.fn(),
        },
        message: {
            findUnique: globals_1.jest.fn(),
            findMany: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
            delete: globals_1.jest.fn(),
            count: globals_1.jest.fn(),
        },
        newsletter: {
            findUnique: globals_1.jest.fn(),
            findMany: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            delete: globals_1.jest.fn(),
            count: globals_1.jest.fn(),
        },
        $connect: globals_1.jest.fn(),
        $disconnect: globals_1.jest.fn(),
    };
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => mockPrisma),
    };
});
// Mock bcryptjs
globals_1.jest.mock('bcryptjs', () => ({
    hash: globals_1.jest.fn().mockResolvedValue('hashed_password'),
    compare: globals_1.jest.fn().mockResolvedValue(true),
}));
// Mock jsonwebtoken
globals_1.jest.mock('jsonwebtoken', () => ({
    sign: globals_1.jest.fn().mockReturnValue('mock_token'),
    verify: globals_1.jest.fn().mockReturnValue({ id: 1, email: '<EMAIL>', role: 'user' }),
}));
(0, globals_1.describe)('API Integration Tests', () => {
    let prisma;
    let userToken;
    let adminToken;
    (0, globals_1.beforeAll)(async () => {
        prisma = new client_1.PrismaClient();
        // Create mock tokens
        userToken = jsonwebtoken_1.default.sign({ id: 1, email: '<EMAIL>', role: 'user' }, process.env.JWT_SECRET || 'test_secret', { expiresIn: '1h' });
        adminToken = jsonwebtoken_1.default.sign({ id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true }, process.env.JWT_SECRET || 'test_secret', { expiresIn: '1h' });
    });
    (0, globals_1.afterAll)(async () => {
        await prisma.$disconnect();
    });
    (0, globals_1.beforeEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('Authentication Endpoints', () => {
        (0, globals_1.it)('should register a new user', async () => {
            // Mock user doesn't exist yet
            prisma.user.findUnique.mockResolvedValue(null);
            // Mock user creation
            prisma.user.create.mockResolvedValue({
                id: 1,
                name: 'Test User',
                email: '<EMAIL>',
                role: 'user',
                createdAt: new Date(),
            });
            const response = await (0, supertest_1.default)(app_1.default)
                .post('/api/auth/register')
                .send({
                name: 'Test User',
                email: '<EMAIL>',
                password: 'Password123!',
            });
            (0, globals_1.expect)(response.status).toBe(201);
            (0, globals_1.expect)(response.body).toHaveProperty('token');
            (0, globals_1.expect)(response.body.user).toHaveProperty('id', 1);
            (0, globals_1.expect)(response.body.user).toHaveProperty('email', '<EMAIL>');
            (0, globals_1.expect)(response.body.user).not.toHaveProperty('password');
        });
        (0, globals_1.it)('should login an existing user', async () => {
            // Mock user exists
            prisma.user.findUnique.mockResolvedValue({
                id: 1,
                name: 'Test User',
                email: '<EMAIL>',
                password: 'hashed_password',
                role: 'user',
                failedLoginAttempts: 0,
                lockUntil: null,
            });
            const response = await (0, supertest_1.default)(app_1.default)
                .post('/api/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'Password123!',
            });
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body).toHaveProperty('token');
            (0, globals_1.expect)(response.body.user).toHaveProperty('id', 1);
            (0, globals_1.expect)(response.body.user).toHaveProperty('email', '<EMAIL>');
            (0, globals_1.expect)(response.body.user).not.toHaveProperty('password');
        });
        (0, globals_1.it)('should not login with incorrect credentials', async () => {
            // Mock user exists
            prisma.user.findUnique.mockResolvedValue({
                id: 1,
                name: 'Test User',
                email: '<EMAIL>',
                password: 'hashed_password',
                role: 'user',
                failedLoginAttempts: 0,
                lockUntil: null,
            });
            // Mock password comparison fails
            bcryptjs_1.default.compare.mockResolvedValueOnce(false);
            const response = await (0, supertest_1.default)(app_1.default)
                .post('/api/auth/login')
                .send({
                email: '<EMAIL>',
                password: 'WrongPassword123!',
            });
            (0, globals_1.expect)(response.status).toBe(401);
            (0, globals_1.expect)(response.body).toHaveProperty('message', 'Invalid credentials');
        });
    });
    (0, globals_1.describe)('Scholarship Endpoints', () => {
        (0, globals_1.it)('should get all scholarships', async () => {
            // Mock scholarships
            const mockScholarships = [
                {
                    id: 1,
                    title: 'Scholarship 1',
                    description: 'Description 1',
                    deadline: new Date(),
                    createdBy: 1,
                    user: { name: 'Test User', email: '<EMAIL>' },
                },
                {
                    id: 2,
                    title: 'Scholarship 2',
                    description: 'Description 2',
                    deadline: new Date(),
                    createdBy: 1,
                    user: { name: 'Test User', email: '<EMAIL>' },
                },
            ];
            prisma.scholarship.findMany.mockResolvedValue(mockScholarships);
            prisma.scholarship.count.mockResolvedValue(2);
            const response = await (0, supertest_1.default)(app_1.default)
                .get('/api/scholarships')
                .set('Authorization', `Bearer ${userToken}`);
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body).toHaveProperty('scholarships');
            (0, globals_1.expect)(response.body.scholarships).toHaveLength(2);
            (0, globals_1.expect)(response.body).toHaveProperty('pagination');
            (0, globals_1.expect)(response.body.pagination).toHaveProperty('total', 2);
        });
        (0, globals_1.it)('should get a scholarship by ID', async () => {
            // Mock scholarship
            const mockScholarship = {
                id: 1,
                title: 'Scholarship 1',
                description: 'Description 1',
                deadline: new Date(),
                createdBy: 1,
                user: { name: 'Test User', email: '<EMAIL>' },
            };
            prisma.scholarship.findUnique.mockResolvedValue(mockScholarship);
            const response = await (0, supertest_1.default)(app_1.default)
                .get('/api/scholarships/1')
                .set('Authorization', `Bearer ${userToken}`);
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body).toHaveProperty('id', 1);
            (0, globals_1.expect)(response.body).toHaveProperty('title', 'Scholarship 1');
        });
        (0, globals_1.it)('should create a new scholarship', async () => {
            // Mock scholarship creation
            const newScholarship = {
                id: 3,
                title: 'New Scholarship',
                description: 'New Description',
                deadline: new Date(),
                createdBy: 1,
                user: { name: 'Test User', email: '<EMAIL>' },
            };
            prisma.scholarship.create.mockResolvedValue(newScholarship);
            const response = await (0, supertest_1.default)(app_1.default)
                .post('/api/scholarships')
                .set('Authorization', `Bearer ${userToken}`)
                .send({
                title: 'New Scholarship',
                description: 'New Description',
                deadline: new Date().toISOString(),
                level: 'Undergraduate',
                country: 'United States',
            });
            (0, globals_1.expect)(response.status).toBe(201);
            (0, globals_1.expect)(response.body).toHaveProperty('id', 3);
            (0, globals_1.expect)(response.body).toHaveProperty('title', 'New Scholarship');
        });
    });
    (0, globals_1.describe)('Admin Endpoints', () => {
        (0, globals_1.it)('should get all users (admin only)', async () => {
            // Mock users
            const mockUsers = [
                {
                    id: 1,
                    name: 'User 1',
                    email: '<EMAIL>',
                    role: 'user',
                },
                {
                    id: 2,
                    name: 'User 2',
                    email: '<EMAIL>',
                    role: 'user',
                },
            ];
            prisma.user.findMany.mockResolvedValue(mockUsers);
            const response = await (0, supertest_1.default)(app_1.default)
                .get('/api/admin/users')
                .set('Authorization', `Bearer ${adminToken}`);
            (0, globals_1.expect)(response.status).toBe(200);
            (0, globals_1.expect)(response.body).toHaveLength(2);
        });
        (0, globals_1.it)('should not allow non-admin to access admin endpoints', async () => {
            const response = await (0, supertest_1.default)(app_1.default)
                .get('/api/admin/users')
                .set('Authorization', `Bearer ${userToken}`);
            (0, globals_1.expect)(response.status).toBe(403);
            (0, globals_1.expect)(response.body).toHaveProperty('message', 'Access denied');
        });
    });
});
