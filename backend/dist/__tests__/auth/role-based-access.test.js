"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const client_1 = require("@prisma/client");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const auth_1 = require("../../middleware/auth");
// Mock PrismaClient
globals_1.jest.mock('@prisma/client', () => {
    const mockPrisma = {
        scholarship: {
            findUnique: globals_1.jest.fn(),
        },
        $connect: globals_1.jest.fn(),
        $disconnect: globals_1.jest.fn(),
    };
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => mockPrisma),
    };
});
// Mock jsonwebtoken
globals_1.jest.mock('jsonwebtoken', () => ({
    verify: globals_1.jest.fn(),
}));
(0, globals_1.describe)('Role-Based Access Control', () => {
    let mockReq;
    let mockRes;
    let mockNext;
    let prisma;
    (0, globals_1.beforeEach)(() => {
        mockReq = {
            headers: {},
            params: {},
        };
        mockRes = {
            status: globals_1.jest.fn().mockReturnThis(),
            json: globals_1.jest.fn(),
        };
        mockNext = globals_1.jest.fn();
        prisma = new client_1.PrismaClient();
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('authMiddleware', () => {
        (0, globals_1.it)('should allow access with valid token', () => {
            mockReq.headers = {
                authorization: 'Bearer valid_token',
            };
            // Mock token verification
            jsonwebtoken_1.default.verify.mockReturnValue({
                id: 1,
                email: '<EMAIL>',
                role: 'user',
            });
            (0, auth_1.authMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
            (0, globals_1.expect)(mockReq.user).toEqual({
                id: 1,
                email: '<EMAIL>',
                role: 'user',
            });
        });
        (0, globals_1.it)('should deny access with missing token', () => {
            mockReq.headers = {}; // No authorization header
            (0, auth_1.authMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(401);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'No token provided',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should deny access with invalid token format', () => {
            mockReq.headers = {
                authorization: 'InvalidFormat',
            };
            (0, auth_1.authMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(401);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Invalid token format',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should deny access with invalid token', () => {
            mockReq.headers = {
                authorization: 'Bearer invalid_token',
            };
            // Mock token verification failure
            jsonwebtoken_1.default.verify.mockImplementation(() => {
                throw new Error('Invalid token');
            });
            (0, auth_1.authMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(401);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Invalid token',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
    });
    (0, globals_1.describe)('adminMiddleware', () => {
        (0, globals_1.it)('should allow access for admin users', () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: true
            };
            (0, auth_1.adminMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
        });
        (0, globals_1.it)('should deny access for non-admin users', () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'user',
                isMainAdmin: false
            };
            (0, auth_1.adminMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(403);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Access denied',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
    });
    (0, globals_1.describe)('mainAdminMiddleware', () => {
        (0, globals_1.it)('should allow access for main admin users', () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: true,
            };
            (0, auth_1.mainAdminMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
        });
        (0, globals_1.it)('should deny access for regular admin users', () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: false,
            };
            (0, auth_1.mainAdminMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(403);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Main admin privileges required',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should deny access for non-admin users', () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'user',
                isMainAdmin: false
            };
            (0, auth_1.mainAdminMiddleware)(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(403);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Main admin privileges required',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
    });
    (0, globals_1.describe)('ownershipMiddleware', () => {
        (0, globals_1.it)('should allow access for resource owners', async () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'user',
                isMainAdmin: false
            };
            mockReq.params = {
                id: '1',
            };
            // Mock scholarship with matching owner
            prisma.scholarship.findUnique.mockResolvedValue({
                id: 1,
                title: 'Test Scholarship',
                createdBy: 1, // Same as user ID
            });
            await (0, auth_1.ownershipMiddleware)('scholarship')(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
        });
        (0, globals_1.it)('should allow access for admin users regardless of ownership', async () => {
            mockReq.user = {
                id: 2,
                email: '<EMAIL>',
                role: 'admin',
                isMainAdmin: true
            };
            mockReq.params = {
                id: '1',
            };
            // Mock scholarship with different owner
            prisma.scholarship.findUnique.mockResolvedValue({
                id: 1,
                title: 'Test Scholarship',
                createdBy: 1, // Different from admin ID
            });
            await (0, auth_1.ownershipMiddleware)('scholarship')(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockNext).toHaveBeenCalled();
        });
        (0, globals_1.it)('should deny access for non-owners who are not admins', async () => {
            mockReq.user = {
                id: 2,
                email: '<EMAIL>',
                role: 'user',
                isMainAdmin: false
            };
            mockReq.params = {
                id: '1',
            };
            // Mock scholarship with different owner
            prisma.scholarship.findUnique.mockResolvedValue({
                id: 1,
                title: 'Test Scholarship',
                createdBy: 1, // Different from user ID
            });
            await (0, auth_1.ownershipMiddleware)('scholarship')(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(403);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Not authorized',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
        (0, globals_1.it)('should handle resource not found', async () => {
            mockReq.user = {
                id: 1,
                email: '<EMAIL>',
                role: 'user',
                isMainAdmin: false
            };
            mockReq.params = {
                id: '999', // Non-existent ID
            };
            // Mock scholarship not found
            prisma.scholarship.findUnique.mockResolvedValue(null);
            await (0, auth_1.ownershipMiddleware)('scholarship')(mockReq, mockRes, mockNext);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(404);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Resource not found',
            });
            (0, globals_1.expect)(mockNext).not.toHaveBeenCalled();
        });
    });
});
