"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetMocks = exports.setupMocks = exports.mockValidationResult = exports.mockJwt = exports.mockBcrypt = exports.mockUserModel = exports.mockPrismaClient = exports.mockNext = exports.mockResponse = exports.mockRequest = void 0;
/**
 * Create a mock Express request object
 */
const mockRequest = (options = {}) => {
    return {
        params: options.params || {},
        query: options.query || {},
        body: options.body || {},
        user: options.user || {
            id: 1,
            email: '<EMAIL>',
            role: 'user',
            isMainAdmin: false
        },
        headers: options.headers || {},
    };
};
exports.mockRequest = mockRequest;
/**
 * Create a mock Express response object
 */
const mockResponse = () => {
    const res = {};
    // Use 'as any' to bypass TypeScript's type checking for these mocks
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    return res;
};
exports.mockResponse = mockResponse;
/**
 * Create a mock next function
 */
exports.mockNext = jest.fn();
/**
 * Create a mock Prisma client for testing
 */
const mockPrismaClient = () => {
    const mockPrisma = {
        user: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
        admin: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
        scholarship: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
        message: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
        newsletter: {
            findUnique: jest.fn(),
            findMany: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
        },
        $connect: jest.fn(),
        $disconnect: jest.fn(),
    };
    return mockPrisma;
};
exports.mockPrismaClient = mockPrismaClient;
/**
 * Mock for the User model (for backward compatibility with Mongoose tests)
 */
exports.mockUserModel = {
    create: jest.fn(),
    findById: jest.fn(),
    findOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
};
/**
 * Mock for bcrypt
 */
exports.mockBcrypt = {
    hash: jest.fn().mockResolvedValue('hashed_password'),
    compare: jest.fn().mockResolvedValue(true),
    genSalt: jest.fn().mockResolvedValue('salt'),
};
/**
 * Mock for jsonwebtoken
 */
exports.mockJwt = {
    sign: jest.fn().mockReturnValue('mock_token'),
    verify: jest.fn().mockReturnValue({
        id: 1,
        email: '<EMAIL>',
        role: 'user',
        isMainAdmin: false
    }),
};
/**
 * Mock for express-validator
 */
exports.mockValidationResult = {
    isEmpty: jest.fn().mockReturnValue(true),
    array: jest.fn().mockReturnValue([]),
};
/**
 * Setup all mocks for a test file
 */
const setupMocks = () => {
    // Mock PrismaClient
    jest.mock('@prisma/client', () => ({
        PrismaClient: jest.fn().mockImplementation(() => (0, exports.mockPrismaClient)()),
    }));
    // Mock bcryptjs
    jest.mock('bcryptjs', () => exports.mockBcrypt);
    // Mock jsonwebtoken
    jest.mock('jsonwebtoken', () => exports.mockJwt);
    // Mock express-validator
    jest.mock('express-validator', () => ({
        validationResult: jest.fn().mockImplementation(() => exports.mockValidationResult),
        body: jest.fn().mockReturnThis(),
        check: jest.fn().mockReturnThis(),
        param: jest.fn().mockReturnThis(),
        query: jest.fn().mockReturnThis(),
        isEmail: jest.fn().mockReturnThis(),
        isLength: jest.fn().mockReturnThis(),
        isDate: jest.fn().mockReturnThis(),
        isURL: jest.fn().mockReturnThis(),
        isBoolean: jest.fn().mockReturnThis(),
        isIn: jest.fn().mockReturnThis(),
        optional: jest.fn().mockReturnThis(),
        withMessage: jest.fn().mockReturnThis(),
    }));
    // Mock User model for backward compatibility
    jest.mock('../../models/user.model', () => exports.mockUserModel);
};
exports.setupMocks = setupMocks;
/**
 * Reset all mocks after tests
 */
const resetMocks = () => {
    jest.resetAllMocks();
};
exports.resetMocks = resetMocks;
exports.default = {
    mockRequest: exports.mockRequest,
    mockResponse: exports.mockResponse,
    mockNext: exports.mockNext,
    mockPrismaClient: exports.mockPrismaClient,
    mockUserModel: exports.mockUserModel,
    mockBcrypt: exports.mockBcrypt,
    mockJwt: exports.mockJwt,
    mockValidationResult: exports.mockValidationResult,
    setupMocks: exports.setupMocks,
    resetMocks: exports.resetMocks,
};
