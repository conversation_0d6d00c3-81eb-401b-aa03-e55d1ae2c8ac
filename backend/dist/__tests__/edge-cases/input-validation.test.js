"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const client_1 = require("@prisma/client");
const scholarshipController = __importStar(require("../../controllers/scholarship.controller"));
const userController = __importStar(require("../../controllers/user.controller"));
// Mock PrismaClient
globals_1.jest.mock('@prisma/client', () => {
    const mockPrisma = {
        scholarship: {
            findMany: globals_1.jest.fn(),
            findUnique: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
            delete: globals_1.jest.fn(),
            count: globals_1.jest.fn(),
        },
        user: {
            findUnique: globals_1.jest.fn(),
            create: globals_1.jest.fn(),
            update: globals_1.jest.fn(),
        },
        $connect: globals_1.jest.fn(),
        $disconnect: globals_1.jest.fn(),
    };
    return {
        PrismaClient: globals_1.jest.fn().mockImplementation(() => mockPrisma),
    };
});
// Mock express-validator
globals_1.jest.mock('express-validator', () => ({
    validationResult: globals_1.jest.fn().mockImplementation(() => ({
        isEmpty: globals_1.jest.fn().mockReturnValue(true),
        array: globals_1.jest.fn().mockReturnValue([]),
    })),
}));
(0, globals_1.describe)('Edge Case Input Validation', () => {
    let mockReq;
    let mockRes;
    let prisma;
    (0, globals_1.beforeEach)(() => {
        mockReq = {
            params: {},
            query: {},
            body: {},
            user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true },
        };
        mockRes = {
            status: globals_1.jest.fn().mockReturnThis(),
            json: globals_1.jest.fn(),
        };
        prisma = new client_1.PrismaClient();
    });
    (0, globals_1.afterEach)(() => {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('Scholarship Controller Edge Cases', () => {
        (0, globals_1.it)('should handle empty strings in scholarship creation', async () => {
            mockReq.body = {
                title: '',
                description: '',
                deadline: new Date().toISOString(),
                level: 'Undergraduate',
                country: '',
            };
            // Mock validation errors for empty strings
            require('express-validator').validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'title', msg: 'Title cannot be empty' },
                    { param: 'description', msg: 'Description cannot be empty' },
                ]),
            }));
            await scholarshipController.createScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'title', msg: 'Title cannot be empty' },
                    { param: 'description', msg: 'Description cannot be empty' },
                ],
            });
        });
        (0, globals_1.it)('should handle extremely long strings in scholarship creation', async () => {
            // Create a string that's 10,000 characters long
            const longString = 'a'.repeat(10000);
            mockReq.body = {
                title: longString,
                description: longString,
                deadline: new Date().toISOString(),
                level: 'Undergraduate',
                country: 'United States',
            };
            // Mock validation errors for long strings
            require('express-validator').validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'title', msg: 'Title cannot exceed 200 characters' },
                    { param: 'description', msg: 'Description cannot exceed 5000 characters' },
                ]),
            }));
            await scholarshipController.createScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'title', msg: 'Title cannot exceed 200 characters' },
                    { param: 'description', msg: 'Description cannot exceed 5000 characters' },
                ],
            });
        });
        (0, globals_1.it)('should handle invalid date formats in scholarship creation', async () => {
            mockReq.body = {
                title: 'Test Scholarship',
                description: 'This is a test scholarship',
                deadline: 'not-a-date',
                level: 'Undergraduate',
                country: 'United States',
            };
            // Mock validation errors for invalid date
            require('express-validator').validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'deadline', msg: 'Deadline must be a valid date' },
                ]),
            }));
            await scholarshipController.createScholarship(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'deadline', msg: 'Deadline must be a valid date' },
                ],
            });
        });
        (0, globals_1.it)('should handle non-numeric ID in getScholarshipById', async () => {
            mockReq.params = { id: 'abc' }; // Non-numeric ID
            await scholarshipController.getScholarshipById(mockReq, mockRes);
            // Should return 400 Bad Request
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Invalid scholarship ID',
            });
        });
        (0, globals_1.it)('should handle negative ID in getScholarshipById', async () => {
            mockReq.params = { id: '-1' }; // Negative ID
            await scholarshipController.getScholarshipById(mockReq, mockRes);
            // Should return 400 Bad Request
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'Invalid scholarship ID',
            });
        });
    });
    (0, globals_1.describe)('User Controller Edge Cases', () => {
        (0, globals_1.it)('should handle invalid email format in user creation', async () => {
            mockReq.body = {
                name: 'Test User',
                email: 'not-an-email',
                password: 'Password123!',
            };
            // Mock validation errors for invalid email
            require('express-validator').validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'email', msg: 'Email must be a valid email address' },
                ]),
            }));
            await userController.createUser(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'email', msg: 'Email must be a valid email address' },
                ],
            });
        });
        (0, globals_1.it)('should handle weak passwords in user creation', async () => {
            mockReq.body = {
                name: 'Test User',
                email: '<EMAIL>',
                password: '123456', // Weak password
            };
            // Mock validation errors for weak password
            require('express-validator').validationResult.mockImplementation(() => ({
                isEmpty: globals_1.jest.fn().mockReturnValue(false),
                array: globals_1.jest.fn().mockReturnValue([
                    { param: 'password', msg: 'Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character' },
                ]),
            }));
            await userController.createUser(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(400);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                errors: [
                    { param: 'password', msg: 'Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character' },
                ],
            });
        });
        (0, globals_1.it)('should handle duplicate email in user creation', async () => {
            mockReq.body = {
                name: 'Test User',
                email: '<EMAIL>',
                password: 'Password123!',
            };
            // Mock user already exists
            prisma.user.findUnique.mockResolvedValue({
                id: 2,
                email: '<EMAIL>',
            });
            await userController.createUser(mockReq, mockRes);
            (0, globals_1.expect)(mockRes.status).toHaveBeenCalledWith(409);
            (0, globals_1.expect)(mockRes.json).toHaveBeenCalledWith({
                message: 'User with this email already exists',
            });
        });
    });
});
