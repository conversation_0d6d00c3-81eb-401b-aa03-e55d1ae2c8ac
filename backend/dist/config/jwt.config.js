"use strict";
/**
 * Centralized JWT Configuration
 * Industry-standard JWT security configuration for MaBourse
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.JWTUtils = exports.getJWTVerifyOptions = exports.getJWTSignOptions = exports.COOKIE_CONFIG = exports.JWT_CONFIG = void 0;
const jwt = __importStar(require("jsonwebtoken"));
const envValidator_1 = require("../utils/envValidator");
// JWT Configuration Constants
exports.JWT_CONFIG = {
    // Core JWT settings
    SECRET: (0, envValidator_1.getEnv)('JWT_SECRET', 'mabourse-enterprise-grade-jwt-secret-key-2025-production-ready-security'),
    ALGORITHM: 'HS256',
    ISSUER: (0, envValidator_1.getEnv)('JWT_ISSUER', 'mabourse-admin-portal'),
    AUDIENCE: (0, envValidator_1.getEnv)('JWT_AUDIENCE', 'mabourse-admin-users'),
    // Token expiration settings
    ACCESS_TOKEN_EXPIRATION: (0, envValidator_1.getEnv)('JWT_EXPIRATION', '2h'),
    REFRESH_TOKEN_EXPIRATION: (0, envValidator_1.getEnv)('REFRESH_TOKEN_EXPIRATION', '7d'),
    // Security settings
    CLOCK_TOLERANCE: 30, // 30 seconds
    MAX_AGE: '2h',
    // Cookie settings
    COOKIE_NAME: 'auth_token',
    REFRESH_COOKIE_NAME: 'refresh_token',
};
// Cookie Configuration
exports.COOKIE_CONFIG = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? process.env.COOKIE_DOMAIN : undefined,
    priority: 'high',
    // Access token cookie (2 hours)
    accessToken: {
        maxAge: 2 * 60 * 60 * 1000, // 2 hours in milliseconds
    },
    // Refresh token cookie (7 days)
    refreshToken: {
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    }
};
// Standard JWT Sign Options
const getJWTSignOptions = () => ({
    algorithm: exports.JWT_CONFIG.ALGORITHM,
    expiresIn: exports.JWT_CONFIG.ACCESS_TOKEN_EXPIRATION,
    issuer: exports.JWT_CONFIG.ISSUER,
    audience: exports.JWT_CONFIG.AUDIENCE,
});
exports.getJWTSignOptions = getJWTSignOptions;
// Standard JWT Verify Options
const getJWTVerifyOptions = () => ({
    algorithms: [exports.JWT_CONFIG.ALGORITHM],
    issuer: exports.JWT_CONFIG.ISSUER,
    audience: exports.JWT_CONFIG.AUDIENCE,
    clockTolerance: exports.JWT_CONFIG.CLOCK_TOLERANCE,
    maxAge: exports.JWT_CONFIG.MAX_AGE,
});
exports.getJWTVerifyOptions = getJWTVerifyOptions;
// Utility Functions
class JWTUtils {
    /**
     * Generate a secure JWT token
     */
    static generateToken(payload) {
        // Add security metadata
        const enhancedPayload = {
            ...payload,
            jti: `${payload.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            iat: Math.floor(Date.now() / 1000),
        };
        return jwt.sign(enhancedPayload, exports.JWT_CONFIG.SECRET, (0, exports.getJWTSignOptions)());
    }
    /**
     * Verify and decode JWT token
     */
    static verifyToken(token) {
        return jwt.verify(token, exports.JWT_CONFIG.SECRET, (0, exports.getJWTVerifyOptions)());
    }
    /**
     * Generate refresh token
     */
    static generateRefreshToken(payload) {
        const refreshPayload = {
            ...payload,
            type: 'refresh',
            jti: `refresh-${payload.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        };
        return jwt.sign(refreshPayload, exports.JWT_CONFIG.SECRET, {
            algorithm: exports.JWT_CONFIG.ALGORITHM,
            expiresIn: exports.JWT_CONFIG.REFRESH_TOKEN_EXPIRATION,
            issuer: exports.JWT_CONFIG.ISSUER,
            audience: exports.JWT_CONFIG.AUDIENCE,
        });
    }
    /**
     * Decode token without verification (for debugging)
     */
    static decodeToken(token) {
        return jwt.decode(token);
    }
    /**
     * Check if token is expired
     */
    static isTokenExpired(token) {
        try {
            const decoded = jwt.decode(token);
            if (!decoded || !decoded.exp)
                return true;
            return Date.now() >= decoded.exp * 1000;
        }
        catch (_a) {
            return true;
        }
    }
    /**
     * Get token expiration time
     */
    static getTokenExpiration(token) {
        try {
            const decoded = jwt.decode(token);
            if (!decoded || !decoded.exp)
                return null;
            return new Date(decoded.exp * 1000);
        }
        catch (_a) {
            return null;
        }
    }
    /**
     * Generate secure cookie options for access token
     */
    static getAccessTokenCookieOptions() {
        return {
            ...exports.COOKIE_CONFIG,
            ...exports.COOKIE_CONFIG.accessToken,
        };
    }
    /**
     * Generate secure cookie options for refresh token
     */
    static getRefreshTokenCookieOptions() {
        return {
            ...exports.COOKIE_CONFIG,
            ...exports.COOKIE_CONFIG.refreshToken,
        };
    }
}
exports.JWTUtils = JWTUtils;
exports.default = {
    JWT_CONFIG: exports.JWT_CONFIG,
    COOKIE_CONFIG: exports.COOKIE_CONFIG,
    JWTUtils,
    getJWTSignOptions: exports.getJWTSignOptions,
    getJWTVerifyOptions: exports.getJWTVerifyOptions,
};
