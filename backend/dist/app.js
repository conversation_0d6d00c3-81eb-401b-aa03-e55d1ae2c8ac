"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const scholarships_1 = __importDefault(require("./routes/scholarships"));
const messages_1 = __importDefault(require("./routes/messages"));
const newsletter_1 = __importDefault(require("./routes/newsletter"));
const auth_new_1 = __importDefault(require("./routes/auth.new"));
const security_dashboard_routes_1 = __importDefault(require("./routes/security.dashboard.routes"));
const guide_routes_1 = __importDefault(require("./routes/guide.routes"));
const opportunity_routes_1 = __importDefault(require("./routes/opportunity.routes"));
const country_routes_1 = __importDefault(require("./routes/country.routes"));
const app = (0, express_1.default)();
app.use((0, cors_1.default)());
app.use(express_1.default.json());
// API Routes
app.use('/api/scholarships', scholarships_1.default);
app.use('/api/messages', messages_1.default);
app.use('/api/newsletter', newsletter_1.default);
app.use('/api/auth', auth_new_1.default);
app.use('/api/security', security_dashboard_routes_1.default);
app.use('/api/guides', guide_routes_1.default);
app.use('/api/opportunities', opportunity_routes_1.default);
app.use('/api/countries', country_routes_1.default);
// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});
exports.default = app;
