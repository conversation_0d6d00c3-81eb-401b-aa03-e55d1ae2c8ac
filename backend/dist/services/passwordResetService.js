"use strict";
/**
 * Professional Password Reset Service
 * Industry-standard secure password reset functionality
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordResetService = void 0;
const crypto_1 = __importDefault(require("crypto"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const database_1 = require("../config/database");
const Admin_1 = require("../models/Admin");
const passwordPolicy_1 = require("../utils/passwordPolicy");
class PasswordResetService {
    /**
     * Initialize password reset table
     */
    static async initializeTable() {
        await (0, database_1.query)(`
      CREATE TABLE IF NOT EXISTS admin_password_reset_tokens (
        id VARCHAR(255) PRIMARY KEY,
        admin_id INTEGER NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
        token VARCHAR(255) NOT NULL UNIQUE,
        expires_at TIMESTAMP NOT NULL,
        is_used BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        used_at TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT
      );

      CREATE INDEX IF NOT EXISTS idx_password_reset_admin_id ON admin_password_reset_tokens(admin_id);
      CREATE INDEX IF NOT EXISTS idx_password_reset_token ON admin_password_reset_tokens(token);
      CREATE INDEX IF NOT EXISTS idx_password_reset_expires ON admin_password_reset_tokens(expires_at);
    `);
    }
    /**
     * Generate secure password reset token
     */
    static async generateResetToken(email, ipAddress, userAgent) {
        try {
            // Find admin by email
            const admin = await Admin_1.Admin.findByEmail(email);
            if (!admin) {
                // Don't reveal if email exists for security
                return {
                    success: true,
                    message: 'If the email exists, a password reset link has been sent.'
                };
            }
            // Check rate limiting
            const recentAttempts = await this.getRecentResetAttempts(admin.id);
            if (recentAttempts >= this.MAX_RESET_ATTEMPTS) {
                return {
                    success: false,
                    message: 'Too many password reset attempts. Please try again later.'
                };
            }
            // Invalidate any existing tokens for this admin
            await this.invalidateExistingTokens(admin.id);
            // Generate secure token
            const tokenId = `reset-${admin.id}-${Date.now()}-${crypto_1.default.randomBytes(16).toString('hex')}`;
            const token = crypto_1.default.randomBytes(32).toString('hex');
            const hashedToken = await bcryptjs_1.default.hash(token, 12);
            const expiresAt = new Date(Date.now() + this.TOKEN_EXPIRY_HOURS * 60 * 60 * 1000);
            // Store token in database
            await (0, database_1.query)(`
        INSERT INTO admin_password_reset_tokens (
          id, admin_id, token, expires_at, ip_address, user_agent
        ) VALUES ($1, $2, $3, $4, $5, $6)
      `, [tokenId, admin.id, hashedToken, expiresAt, ipAddress, userAgent]);
            console.log(`✅ Password reset token generated for admin ${email}`);
            return {
                success: true,
                message: 'Password reset link has been sent to your email.',
                tokenId: `${tokenId}:${token}` // Return unhashed token for email
            };
        }
        catch (error) {
            console.error('Error generating password reset token:', error);
            return {
                success: false,
                message: 'Failed to generate password reset token.'
            };
        }
    }
    /**
     * Validate and use password reset token
     */
    static async validateResetToken(tokenId, token) {
        try {
            // Get token from database
            const result = await (0, database_1.query)(`
        SELECT * FROM admin_password_reset_tokens 
        WHERE id = $1 AND is_used = false AND expires_at > NOW()
      `, [tokenId]);
            if (result.rows.length === 0) {
                return {
                    valid: false,
                    message: 'Invalid or expired password reset token.'
                };
            }
            const tokenRecord = result.rows[0];
            // Verify token
            const isValidToken = await bcryptjs_1.default.compare(token, tokenRecord.token);
            if (!isValidToken) {
                return {
                    valid: false,
                    message: 'Invalid password reset token.'
                };
            }
            return {
                valid: true,
                adminId: tokenRecord.admin_id,
                message: 'Token is valid.'
            };
        }
        catch (error) {
            console.error('Error validating password reset token:', error);
            return {
                valid: false,
                message: 'Failed to validate password reset token.'
            };
        }
    }
    /**
     * Reset password using token
     */
    static async resetPassword(tokenId, token, newPassword) {
        try {
            // Validate token
            const tokenValidation = await this.validateResetToken(tokenId, token);
            if (!tokenValidation.valid || !tokenValidation.adminId) {
                return {
                    success: false,
                    message: tokenValidation.message
                };
            }
            // Validate new password
            const passwordValidation = await (0, passwordPolicy_1.validatePassword)(newPassword, tokenValidation.adminId);
            if (!passwordValidation.valid) {
                return {
                    success: false,
                    message: passwordValidation.message
                };
            }
            // Update admin password
            await Admin_1.Admin.update(tokenValidation.adminId, {
                password: newPassword // Admin.update will hash it
            });
            // Mark token as used
            await (0, database_1.query)(`
        UPDATE admin_password_reset_tokens 
        SET is_used = true, used_at = NOW()
        WHERE id = $1
      `, [tokenId]);
            // Invalidate all sessions for this admin (force re-login)
            // This would be implemented with SessionManager if available
            console.log(`✅ Password reset successful for admin ID ${tokenValidation.adminId}`);
            return {
                success: true,
                message: 'Password has been reset successfully. Please login with your new password.'
            };
        }
        catch (error) {
            console.error('Error resetting password:', error);
            return {
                success: false,
                message: 'Failed to reset password.'
            };
        }
    }
    /**
     * Get recent reset attempts for rate limiting
     */
    static async getRecentResetAttempts(adminId) {
        const result = await (0, database_1.query)(`
      SELECT COUNT(*) as count
      FROM admin_password_reset_tokens 
      WHERE admin_id = $1 AND created_at > NOW() - INTERVAL '1 hour'
    `, [adminId]);
        return parseInt(result.rows[0].count) || 0;
    }
    /**
     * Invalidate existing tokens for an admin
     */
    static async invalidateExistingTokens(adminId) {
        await (0, database_1.query)(`
      UPDATE admin_password_reset_tokens 
      SET is_used = true
      WHERE admin_id = $1 AND is_used = false
    `, [adminId]);
    }
    /**
     * Clean up expired tokens
     */
    static async cleanupExpiredTokens() {
        const result = await (0, database_1.query)(`
      DELETE FROM admin_password_reset_tokens 
      WHERE expires_at <= NOW() OR (is_used = true AND used_at <= NOW() - INTERVAL '24 hours')
      RETURNING id
    `);
        return result.rows.length;
    }
    /**
     * Get reset token statistics
     */
    static async getResetStats() {
        const result = await (0, database_1.query)(`
      SELECT 
        COUNT(*) FILTER (WHERE is_used = false AND expires_at > NOW()) as total_active,
        COUNT(*) FILTER (WHERE expires_at <= NOW()) as total_expired,
        COUNT(*) FILTER (WHERE is_used = true) as total_used
      FROM admin_password_reset_tokens
    `);
        return {
            totalActive: parseInt(result.rows[0].total_active) || 0,
            totalExpired: parseInt(result.rows[0].total_expired) || 0,
            totalUsed: parseInt(result.rows[0].total_used) || 0,
        };
    }
}
exports.PasswordResetService = PasswordResetService;
PasswordResetService.TOKEN_EXPIRY_HOURS = 1; // 1 hour expiry for security
PasswordResetService.MAX_RESET_ATTEMPTS = 3; // Max attempts per hour
exports.default = PasswordResetService;
