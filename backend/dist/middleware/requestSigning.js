"use strict";
/**
 * Request Signing Middleware
 * Implements cryptographic request verification for enhanced API security
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestSigningService = void 0;
exports.optionalSignatureVerification = optionalSignatureVerification;
const crypto_1 = __importDefault(require("crypto"));
const database_1 = require("../config/database");
const securityMonitor_1 = require("../utils/securityMonitor");
const authLogger_1 = require("../utils/authLogger");
class RequestSigningService {
    /**
     * Generate HMAC signature for request
     */
    static generateSignature(method, url, body, timestamp, nonce, secretKey) {
        const stringToSign = [
            method.toUpperCase(),
            url,
            body,
            timestamp,
            nonce
        ].join('\n');
        return crypto_1.default
            .createHmac(_a.ALGORITHM, secretKey)
            .update(stringToSign)
            .digest('hex');
    }
    /**
     * Get API key from database
     */
    static async getAPIKey(keyId) {
        try {
            const result = await (0, database_1.query)(`
        SELECT 
          id, admin_id, key_id, secret_key, name, permissions,
          is_active, expires_at, last_used, created_at
        FROM api_keys 
        WHERE key_id = $1
      `, [keyId]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            return {
                id: row.id,
                adminId: row.admin_id,
                keyId: row.key_id,
                secretKey: row.secret_key,
                name: row.name,
                permissions: row.permissions || [],
                isActive: row.is_active,
                expiresAt: row.expires_at,
                lastUsed: row.last_used,
                createdAt: row.created_at
            };
        }
        catch (error) {
            console.error('Error getting API key:', error);
            return null;
        }
    }
    /**
     * Update API key last used timestamp
     */
    static async updateAPIKeyUsage(keyId) {
        try {
            await (0, database_1.query)(`
        UPDATE api_keys 
        SET last_used = CURRENT_TIMESTAMP 
        WHERE key_id = $1
      `, [keyId]);
        }
        catch (error) {
            console.error('Error updating API key usage:', error);
        }
    }
    /**
     * Add nonce to used set with cleanup
     */
    static addUsedNonce(nonce) {
        _a.usedNonces.add(nonce);
        // Clean up old nonces if cache is too large
        if (_a.usedNonces.size > _a.NONCE_CACHE_SIZE) {
            const noncesToDelete = Array.from(_a.usedNonces).slice(0, 1000);
            noncesToDelete.forEach(n => _a.usedNonces.delete(n));
        }
    }
    /**
     * Handle signature verification errors
     */
    static async handleSignatureError(req, res, message, errorCode) {
        // Log security event
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.LOGIN_FAILED,
            message: `Request signature verification failed: ${message}`,
            email: '',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.WARNING,
            sessionId: req.sessionID || 'unknown',
            riskScore: 70,
            details: {
                errorCode,
                endpoint: req.originalUrl,
                method: req.method,
                headers: {
                    signature: req.get('X-Signature') ? 'present' : 'missing',
                    timestamp: req.get('X-Timestamp') ? 'present' : 'missing',
                    nonce: req.get('X-Nonce') ? 'present' : 'missing',
                    keyId: req.get('X-Key-ID') ? 'present' : 'missing'
                }
            }
        });
        res.status(401).json({
            success: false,
            message: 'Request signature verification failed',
            error: message,
            code: errorCode
        });
    }
    /**
     * Generate new API key pair
     */
    static async generateAPIKey(adminId, name, permissions = [], expiresAt) {
        const keyId = 'ak_' + crypto_1.default.randomBytes(16).toString('hex');
        const secretKey = crypto_1.default.randomBytes(32).toString('hex');
        await (0, database_1.query)(`
      INSERT INTO api_keys (admin_id, key_id, secret_key, name, permissions, expires_at)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [adminId, keyId, secretKey, name, JSON.stringify(permissions), expiresAt]);
        return { keyId, secretKey };
    }
    /**
     * Revoke API key
     */
    static async revokeAPIKey(keyId) {
        try {
            const result = await (0, database_1.query)(`
        UPDATE api_keys 
        SET is_active = false 
        WHERE key_id = $1
      `, [keyId]);
            return (result.rowCount || 0) > 0;
        }
        catch (error) {
            console.error('Error revoking API key:', error);
            return false;
        }
    }
    /**
     * List API keys for admin
     */
    static async listAPIKeys(adminId) {
        try {
            const result = await (0, database_1.query)(`
        SELECT 
          key_id, name, permissions, is_active, expires_at, last_used, created_at
        FROM api_keys 
        WHERE admin_id = $1
        ORDER BY created_at DESC
      `, [adminId]);
            return result.rows.map(row => ({
                keyId: row.key_id,
                name: row.name,
                permissions: row.permissions || [],
                isActive: row.is_active,
                expiresAt: row.expires_at,
                lastUsed: row.last_used,
                createdAt: row.created_at
            }));
        }
        catch (error) {
            console.error('Error listing API keys:', error);
            return [];
        }
    }
}
exports.RequestSigningService = RequestSigningService;
_a = RequestSigningService;
RequestSigningService.SIGNATURE_HEADER = 'X-Signature';
RequestSigningService.TIMESTAMP_HEADER = 'X-Timestamp';
RequestSigningService.NONCE_HEADER = 'X-Nonce';
RequestSigningService.KEY_ID_HEADER = 'X-Key-ID';
RequestSigningService.ALGORITHM = 'sha256';
RequestSigningService.MAX_TIMESTAMP_SKEW = 5 * 60 * 1000; // 5 minutes
RequestSigningService.NONCE_CACHE_SIZE = 10000;
RequestSigningService.usedNonces = new Set();
/**
 * Middleware to verify request signatures
 */
RequestSigningService.verifySignature = async (req, res, next) => {
    try {
        const signature = req.get(_a.SIGNATURE_HEADER);
        const timestamp = req.get(_a.TIMESTAMP_HEADER);
        const nonce = req.get(_a.NONCE_HEADER);
        const keyId = req.get(_a.KEY_ID_HEADER);
        // Check if all required headers are present
        if (!signature || !timestamp || !nonce || !keyId) {
            return _a.handleSignatureError(req, res, 'Missing required signature headers', 'MISSING_HEADERS');
        }
        // Verify timestamp (prevent replay attacks)
        const requestTime = parseInt(timestamp);
        const now = Date.now();
        if (Math.abs(now - requestTime) > _a.MAX_TIMESTAMP_SKEW) {
            return _a.handleSignatureError(req, res, 'Request timestamp is too old or too far in the future', 'INVALID_TIMESTAMP');
        }
        // Verify nonce (prevent replay attacks)
        if (_a.usedNonces.has(nonce)) {
            return _a.handleSignatureError(req, res, 'Nonce has already been used', 'DUPLICATE_NONCE');
        }
        // Get API key
        const apiKey = await _a.getAPIKey(keyId);
        if (!apiKey) {
            return _a.handleSignatureError(req, res, 'Invalid API key', 'INVALID_KEY');
        }
        if (!apiKey.isActive) {
            return _a.handleSignatureError(req, res, 'API key is disabled', 'DISABLED_KEY');
        }
        if (apiKey.expiresAt && new Date() > apiKey.expiresAt) {
            return _a.handleSignatureError(req, res, 'API key has expired', 'EXPIRED_KEY');
        }
        // Generate expected signature
        const expectedSignature = _a.generateSignature(req.method, req.originalUrl, JSON.stringify(req.body || {}), timestamp, nonce, apiKey.secretKey);
        // Verify signature
        if (!crypto_1.default.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
            return _a.handleSignatureError(req, res, 'Invalid signature', 'INVALID_SIGNATURE');
        }
        // Add nonce to used set (with cleanup)
        _a.addUsedNonce(nonce);
        // Update API key last used timestamp
        await _a.updateAPIKeyUsage(keyId);
        // Add signature info to request
        req.signature = {
            valid: true,
            keyId,
            timestamp: requestTime,
            nonce
        };
        // Log successful signature verification
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.LOGIN_SUCCESS,
            message: 'Request signature verified successfully',
            adminId: apiKey.adminId,
            email: '',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.INFO,
            sessionId: req.sessionID || 'unknown',
            details: {
                keyId,
                endpoint: req.originalUrl,
                method: req.method,
                signatureValid: true
            }
        });
        next();
    }
    catch (error) {
        console.error('Request signature verification error:', error);
        return _a.handleSignatureError(req, res, 'Signature verification failed', 'VERIFICATION_ERROR');
    }
};
/**
 * Middleware factory for optional signature verification
 */
function optionalSignatureVerification() {
    return async (req, res, next) => {
        const hasSignatureHeaders = req.get('X-Signature') ||
            req.get('X-Key-ID');
        if (hasSignatureHeaders) {
            // If signature headers are present, verify them
            return RequestSigningService.verifySignature(req, res, next);
        }
        else {
            // If no signature headers, continue without verification
            req.signature = { valid: false, keyId: '', timestamp: 0, nonce: '' };
            next();
        }
    };
}
exports.default = RequestSigningService.verifySignature;
