"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleScholarshipUpload = exports.processUploadedFile = exports.uploadScholarshipThumbnail = exports.handleMulterError = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const crypto_1 = __importDefault(require("crypto"));
// Ensure upload directories exist
const createUploadDirectories = () => {
    const uploadDir = path_1.default.join(__dirname, '../../uploads');
    const scholarshipsDir = path_1.default.join(uploadDir, 'scholarships');
    if (!fs_1.default.existsSync(uploadDir)) {
        fs_1.default.mkdirSync(uploadDir, { recursive: true });
    }
    if (!fs_1.default.existsSync(scholarshipsDir)) {
        fs_1.default.mkdirSync(scholarshipsDir, { recursive: true });
    }
};
// Create upload directories on module load
createUploadDirectories();
// Configure storage
const storage = multer_1.default.diskStorage({
    destination: (_req, _file, cb) => {
        cb(null, path_1.default.join(__dirname, '../../uploads/scholarships'));
    },
    filename: (_req, file, cb) => {
        // Create a secure unique filename with original extension
        // Use crypto for more secure random values than Math.random()
        const randomBytes = crypto_1.default.randomBytes(16).toString('hex');
        const timestamp = Date.now();
        const ext = path_1.default.extname(file.originalname).toLowerCase();
        // Sanitize the original filename to remove any potentially harmful characters
        const sanitizedName = path_1.default.basename(file.originalname, ext)
            .replace(/[^a-zA-Z0-9]/g, '_')
            .substring(0, 20); // Limit the length
        cb(null, `${sanitizedName}-${timestamp}-${randomBytes}${ext}`);
    }
});
// File filter function
const fileFilter = (_req, file, cb) => {
    // Accept only image files
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    const allowedExtensions = ['.jpg', '.jpeg', '.png'];
    // Check both mimetype and file extension for security
    const ext = path_1.default.extname(file.originalname).toLowerCase();
    console.log('File upload attempt:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        extension: ext
    });
    if (allowedMimeTypes.includes(file.mimetype) && allowedExtensions.includes(ext)) {
        cb(null, true);
    }
    else {
        cb(new Error(`Only .jpg, .jpeg, and .png files are allowed. Got mimetype: ${file.mimetype}, extension: ${ext}`), false);
    }
};
// Create multer upload instance
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB max file size
        files: 1 // Only allow 1 file per request
    },
    fileFilter
});
// Error handling middleware for multer errors
const handleMulterError = (err, _req, res, next) => {
    if (err instanceof multer_1.default.MulterError) {
        // A Multer error occurred when uploading
        console.error('Multer error:', err);
        if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(413).json({
                error: 'File too large',
                message: 'File size should not exceed 5MB'
            });
        }
        return res.status(400).json({
            error: err.code,
            message: err.message
        });
    }
    else if (err) {
        // An unknown error occurred
        console.error('Upload error:', err);
        return res.status(500).json({
            error: 'upload_error',
            message: err.message
        });
    }
    // No error occurred, continue
    next();
};
exports.handleMulterError = handleMulterError;
// Export middleware for different use cases
exports.uploadScholarshipThumbnail = upload.single('thumbnail');
// Helper function to process uploaded file and add path to request body
const processUploadedFile = (req, _res, next) => {
    console.log('Processing uploaded file. Request file:', req.file);
    if (req.file) {
        // Add the file path to the request body
        req.body.thumbnail = `/uploads/scholarships/${req.file.filename}`;
        // Log successful upload
        console.log(`File uploaded successfully: ${req.file.filename}`);
        console.log('Updated request body with thumbnail path:', req.body.thumbnail);
    }
    else {
        console.log('No file was uploaded with this request');
    }
    next();
};
exports.processUploadedFile = processUploadedFile;
// Export combined middleware
exports.handleScholarshipUpload = [
    exports.uploadScholarshipThumbnail,
    exports.handleMulterError,
    exports.processUploadedFile
];
