"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMetricsForDateRange = exports.getMetricsForDate = exports.metricsMiddleware = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_1 = __importDefault(require("../utils/logger"));
// In-memory storage for metrics (will be periodically written to disk)
let requestMetrics = [];
const METRICS_FLUSH_INTERVAL = 60 * 1000; // 1 minute
const MAX_METRICS_BEFORE_FLUSH = 100;
// Ensure metrics directory exists
const metricsDir = path_1.default.join(__dirname, '../../logs/metrics');
if (!fs_1.default.existsSync(metricsDir)) {
    fs_1.default.mkdirSync(metricsDir, { recursive: true });
}
// Function to write metrics to disk
const flushMetricsToDisk = () => {
    if (requestMetrics.length === 0)
        return;
    const date = new Date();
    const fileName = `metrics-${date.toISOString().split('T')[0]}.json`;
    const filePath = path_1.default.join(metricsDir, fileName);
    try {
        // Read existing metrics if file exists
        let existingMetrics = [];
        if (fs_1.default.existsSync(filePath)) {
            const fileContent = fs_1.default.readFileSync(filePath, 'utf8');
            existingMetrics = JSON.parse(fileContent);
        }
        // Combine existing and new metrics
        const allMetrics = [...existingMetrics, ...requestMetrics];
        // Write to file
        fs_1.default.writeFileSync(filePath, JSON.stringify(allMetrics, null, 2));
        // Clear in-memory metrics
        requestMetrics = [];
        logger_1.default.info(`Flushed ${requestMetrics.length} metrics to disk`, {
            category: 'metrics',
            file: fileName,
        });
    }
    catch (error) {
        logger_1.default.error(`Failed to flush metrics to disk: ${error.message}`, {
            category: 'metrics',
            error,
        });
    }
};
// Set up periodic flushing
setInterval(flushMetricsToDisk, METRICS_FLUSH_INTERVAL);
// Ensure metrics are flushed when the application exits
process.on('SIGINT', () => {
    flushMetricsToDisk();
    process.exit(0);
});
process.on('SIGTERM', () => {
    flushMetricsToDisk();
    process.exit(0);
});
// Middleware to collect request metrics
const metricsMiddleware = (req, res, next) => {
    // Record start time
    const startTime = process.hrtime();
    // Store original end method
    const originalEnd = res.end;
    // Override end method to capture response metrics
    res.end = function (chunk, encoding, callback) {
        var _a, _b;
        // Calculate response time
        const hrtime = process.hrtime(startTime);
        const responseTimeMs = hrtime[0] * 1000 + hrtime[1] / 1000000;
        // Create metric object
        const metric = {
            method: req.method,
            path: req.path,
            statusCode: res.statusCode,
            responseTime: parseFloat(responseTimeMs.toFixed(2)),
            timestamp: new Date(),
            userAgent: req.headers['user-agent'],
            ip: req.ip,
            userId: (_a = req.user) === null || _a === void 0 ? void 0 : _a.id,
            userRole: (_b = req.user) === null || _b === void 0 ? void 0 : _b.role,
        };
        // Add to in-memory storage
        requestMetrics.push(metric);
        // Flush if we've accumulated enough metrics
        if (requestMetrics.length >= MAX_METRICS_BEFORE_FLUSH) {
            flushMetricsToDisk();
        }
        // Log slow responses
        if (responseTimeMs > 1000) {
            logger_1.default.warn(`Slow response detected: ${req.method} ${req.path} took ${responseTimeMs.toFixed(2)}ms`, {
                category: 'performance',
                metric,
            });
        }
        // Call original end method
        return originalEnd.call(this, chunk, encoding, callback);
    };
    next();
};
exports.metricsMiddleware = metricsMiddleware;
// Utility function to get metrics for a specific date
const getMetricsForDate = (date) => {
    const dateString = date.toISOString().split('T')[0];
    const filePath = path_1.default.join(metricsDir, `metrics-${dateString}.json`);
    if (!fs_1.default.existsSync(filePath)) {
        return [];
    }
    try {
        const fileContent = fs_1.default.readFileSync(filePath, 'utf8');
        return JSON.parse(fileContent);
    }
    catch (error) {
        logger_1.default.error(`Failed to read metrics file: ${error.message}`, {
            category: 'metrics',
            error,
        });
        return [];
    }
};
exports.getMetricsForDate = getMetricsForDate;
// Utility function to get metrics for a date range
const getMetricsForDateRange = (startDate, endDate) => {
    const metrics = [];
    // Clone start date to avoid modifying the original
    const currentDate = new Date(startDate);
    // Iterate through each day in the range
    while (currentDate <= endDate) {
        const dateMetrics = (0, exports.getMetricsForDate)(currentDate);
        metrics.push(...dateMetrics);
        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
    }
    return metrics;
};
exports.getMetricsForDateRange = getMetricsForDateRange;
exports.default = exports.metricsMiddleware;
