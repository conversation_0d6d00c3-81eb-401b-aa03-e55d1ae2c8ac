"use strict";
/**
 * Advanced Content Security Policy Middleware
 * Implements enhanced CSP with nonce-based script execution and strict security rules
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedCSPService = void 0;
exports.getNonce = getNonce;
exports.createCSP = createCSP;
const crypto_1 = __importDefault(require("crypto"));
const securityMonitor_1 = require("../utils/securityMonitor");
const authLogger_1 = require("../utils/authLogger");
class AdvancedCSPService {
    /**
     * Generate cryptographically secure nonce
     */
    static generateNonce() {
        return crypto_1.default.randomBytes(16).toString('base64');
    }
    /**
     * Create CSP middleware with custom configuration
     */
    static createCSPMiddleware(config = {}) {
        const finalConfig = { ..._a.DEFAULT_CONFIG, ...config };
        return (req, res, next) => {
            try {
                // Generate nonce for this request
                if (finalConfig.enableNonce) {
                    req.nonce = _a.generateNonce();
                }
                // Build CSP header
                const cspHeader = _a.buildCSPHeader(finalConfig, req.nonce);
                // Set CSP header
                const headerName = finalConfig.reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';
                res.setHeader(headerName, cspHeader);
                // Add additional security headers
                _a.setAdditionalSecurityHeaders(res, finalConfig);
                // Log CSP policy application
                _a.logCSPApplication(req, finalConfig);
                next();
            }
            catch (error) {
                console.error('CSP middleware error:', error);
                // Continue without CSP on error to avoid breaking the application
                next();
            }
        };
    }
    /**
     * Build comprehensive CSP header
     */
    static buildCSPHeader(config, nonce) {
        const directives = [];
        // Default source directive
        directives.push(`default-src ${config.allowedDomains.map(d => `'${d}'`).join(' ')}`);
        // Script source directive with nonce support
        const scriptSources = [...config.trustedScriptSources.map(s => `'${s}'`)];
        if (config.enableNonce && nonce) {
            scriptSources.push(`'nonce-${nonce}'`);
        }
        if (config.allowInlineScripts) {
            scriptSources.push("'unsafe-inline'");
        }
        if (config.allowEval) {
            scriptSources.push("'unsafe-eval'");
        }
        directives.push(`script-src ${scriptSources.join(' ')}`);
        // Style source directive
        const styleSources = [...config.trustedStyleSources.map(s => `'${s}'`)];
        if (config.allowInlineStyles) {
            styleSources.push("'unsafe-inline'");
        }
        if (config.enableNonce && nonce) {
            styleSources.push(`'nonce-${nonce}'`);
        }
        directives.push(`style-src ${styleSources.join(' ')}`);
        // Object source directive (strict)
        directives.push("object-src 'none'");
        // Base URI directive
        directives.push("base-uri 'self'");
        // Form action directive
        directives.push("form-action 'self'");
        // Frame ancestors directive (prevent clickjacking)
        directives.push("frame-ancestors 'none'");
        // Image source directive
        directives.push("img-src 'self' data: https:");
        // Font source directive
        directives.push("font-src 'self' https:");
        // Connect source directive (for AJAX, WebSocket, etc.)
        directives.push("connect-src 'self' https:");
        // Media source directive
        directives.push("media-src 'self'");
        // Worker source directive
        directives.push("worker-src 'self'");
        // Manifest source directive
        directives.push("manifest-src 'self'");
        // Frame source directive
        directives.push("frame-src 'none'");
        // Upgrade insecure requests
        if (config.upgradeInsecureRequests) {
            directives.push("upgrade-insecure-requests");
        }
        // Block all mixed content
        directives.push("block-all-mixed-content");
        // Report URI for violations
        if (config.reportUri) {
            directives.push(`report-uri ${config.reportUri}`);
        }
        return directives.join('; ');
    }
    /**
     * Set additional security headers
     */
    static setAdditionalSecurityHeaders(res, config) {
        // X-Content-Type-Options
        res.setHeader('X-Content-Type-Options', 'nosniff');
        // X-Frame-Options (backup for older browsers)
        res.setHeader('X-Frame-Options', 'DENY');
        // X-XSS-Protection (for older browsers)
        res.setHeader('X-XSS-Protection', '1; mode=block');
        // Referrer Policy
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        // Permissions Policy (formerly Feature Policy)
        const permissionsPolicy = [
            'camera=()',
            'microphone=()',
            'geolocation=()',
            'payment=()',
            'usb=()',
            'magnetometer=()',
            'accelerometer=()',
            'gyroscope=()'
        ].join(', ');
        res.setHeader('Permissions-Policy', permissionsPolicy);
        // Cross-Origin Embedder Policy
        res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
        // Cross-Origin Opener Policy
        res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
        // Cross-Origin Resource Policy
        res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
        // Strict Transport Security (if HTTPS)
        if (config.upgradeInsecureRequests) {
            res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
    }
    /**
     * Log CSP policy application
     */
    static async logCSPApplication(req, config) {
        var _b;
        try {
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.CONFIGURATION_CHANGE,
                message: 'CSP policy applied',
                email: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.email) || '',
                ip: req.ip || 'unknown',
                userAgent: req.get('User-Agent'),
                severity: authLogger_1.SecurityLevel.INFO,
                sessionId: req.sessionID || 'unknown',
                details: {
                    endpoint: req.originalUrl,
                    method: req.method,
                    cspConfig: {
                        strictMode: config.strictMode,
                        enableNonce: config.enableNonce,
                        reportOnly: config.reportOnly
                    },
                    nonceGenerated: !!req.nonce
                }
            });
        }
        catch (error) {
            console.error('Error logging CSP application:', error);
        }
    }
}
exports.AdvancedCSPService = AdvancedCSPService;
_a = AdvancedCSPService;
AdvancedCSPService.DEFAULT_CONFIG = {
    enableNonce: true,
    strictMode: true,
    reportOnly: false,
    allowedDomains: ['self'],
    trustedScriptSources: ['self'],
    trustedStyleSources: ['self', 'unsafe-inline'],
    allowInlineStyles: false,
    allowInlineScripts: false,
    allowEval: false,
    upgradeInsecureRequests: true
};
/**
 * CSP violation report handler
 */
AdvancedCSPService.cspViolationHandler = async (req, res) => {
    var _b, _c;
    try {
        const violation = req.body;
        // Log CSP violation
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.SUSPICIOUS_ACTIVITY,
            message: 'CSP violation detected',
            email: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.email) || '',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.WARNING,
            sessionId: req.sessionID || 'unknown',
            riskScore: 60,
            details: {
                violation: {
                    documentUri: violation['document-uri'],
                    referrer: violation.referrer,
                    violatedDirective: violation['violated-directive'],
                    effectiveDirective: violation['effective-directive'],
                    originalPolicy: violation['original-policy'],
                    blockedUri: violation['blocked-uri'],
                    statusCode: violation['status-code'],
                    sourceFile: violation['source-file'],
                    lineNumber: violation['line-number'],
                    columnNumber: violation['column-number']
                }
            }
        });
        // Check for potential XSS attempts
        const suspiciousPatterns = [
            'javascript:',
            'data:text/html',
            'vbscript:',
            'onload=',
            'onerror=',
            'onclick=',
            '<script',
            'eval(',
            'setTimeout(',
            'setInterval('
        ];
        const blockedUri = violation['blocked-uri'] || '';
        const sourceFile = violation['source-file'] || '';
        const isSuspicious = suspiciousPatterns.some(pattern => blockedUri.toLowerCase().includes(pattern) ||
            sourceFile.toLowerCase().includes(pattern));
        if (isSuspicious) {
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.SUSPICIOUS_ACTIVITY,
                message: 'Potential XSS attempt detected via CSP violation',
                email: ((_c = req.user) === null || _c === void 0 ? void 0 : _c.email) || '',
                ip: req.ip || 'unknown',
                userAgent: req.get('User-Agent'),
                severity: authLogger_1.SecurityLevel.WARNING,
                sessionId: req.sessionID || 'unknown',
                riskScore: 85,
                details: {
                    suspiciousContent: {
                        blockedUri,
                        sourceFile,
                        violatedDirective: violation['violated-directive']
                    }
                }
            });
        }
        res.status(204).send(); // No content response
    }
    catch (error) {
        console.error('CSP violation handler error:', error);
        res.status(500).json({ error: 'Failed to process CSP violation report' });
    }
};
/**
 * Predefined CSP configurations for different environments
 */
AdvancedCSPService.configurations = {
    // Strict configuration for production
    production: {
        enableNonce: true,
        strictMode: true,
        reportOnly: false,
        allowedDomains: ['self'],
        trustedScriptSources: ['self'],
        trustedStyleSources: ['self'],
        allowInlineStyles: false,
        allowInlineScripts: false,
        allowEval: false,
        upgradeInsecureRequests: true,
        reportUri: '/api/security/csp-violation'
    },
    // Development configuration with relaxed rules
    development: {
        enableNonce: true,
        strictMode: false,
        reportOnly: true,
        allowedDomains: ['self', 'localhost:*'],
        trustedScriptSources: ['self', 'localhost:*'],
        trustedStyleSources: ['self', 'unsafe-inline', 'localhost:*'],
        allowInlineStyles: true,
        allowInlineScripts: false,
        allowEval: true,
        upgradeInsecureRequests: false,
        reportUri: '/api/security/csp-violation'
    },
    // Testing configuration
    testing: {
        enableNonce: false,
        strictMode: false,
        reportOnly: true,
        allowedDomains: ['self', '*'],
        trustedScriptSources: ['self', 'unsafe-inline', 'unsafe-eval'],
        trustedStyleSources: ['self', 'unsafe-inline'],
        allowInlineStyles: true,
        allowInlineScripts: true,
        allowEval: true,
        upgradeInsecureRequests: false
    }
};
/**
 * Helper function to get nonce from request
 */
function getNonce(req) {
    return req.nonce;
}
/**
 * Middleware factory for different environments
 */
function createCSP(environment = 'production') {
    const config = AdvancedCSPService.configurations[environment];
    return AdvancedCSPService.createCSPMiddleware(config);
}
exports.default = AdvancedCSPService.createCSPMiddleware;
