"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminValidationRules = exports.userValidationRules = exports.scholarshipValidationRules = exports.validateRequest = void 0;
const express_validator_1 = require("express-validator");
// Validation middleware to check for validation errors
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};
exports.validateRequest = validateRequest;
// Scholarship validation rules
exports.scholarshipValidationRules = {
    // Rules for creating a new scholarship
    create: [
        (0, express_validator_1.body)('title')
            .notEmpty()
            .withMessage('Title is required')
            .isLength({ max: 200 })
            .withMessage('Title cannot exceed 200 characters'),
        (0, express_validator_1.body)('description')
            .notEmpty()
            .withMessage('Description is required')
            .isLength({ max: 5000 })
            .withMessage('Description cannot exceed 5000 characters'),
        (0, express_validator_1.body)('deadline')
            .notEmpty()
            .withMessage('Deadline is required')
            .isISO8601()
            .withMessage('Deadline must be a valid date'),
        (0, express_validator_1.body)('level')
            .optional()
            .isIn(['Undergraduate', 'Graduate', 'PhD'])
            .withMessage('Level must be one of: Undergraduate, Graduate, PhD'),
        (0, express_validator_1.body)('country')
            .optional()
            .isString()
            .withMessage('Country must be a string'),
        (0, express_validator_1.body)('isOpen')
            .optional()
            .isBoolean()
            .withMessage('isOpen must be a boolean'),
        (0, express_validator_1.body)('financial_benefits_summary')
            .optional()
            .isString()
            .withMessage('Financial benefits summary must be a string')
            .isLength({ max: 1000 })
            .withMessage('Financial benefits summary cannot exceed 1000 characters'),
        (0, express_validator_1.body)('eligibility_summary')
            .optional()
            .isString()
            .withMessage('Eligibility summary must be a string')
            .isLength({ max: 1000 })
            .withMessage('Eligibility summary cannot exceed 1000 characters'),
        (0, express_validator_1.body)('scholarship_link')
            .optional()
            .isURL()
            .withMessage('Scholarship link must be a valid URL'),
        (0, express_validator_1.body)('youtube_link')
            .optional()
            .isURL()
            .withMessage('YouTube link must be a valid URL'),
        exports.validateRequest,
    ],
    // Rules for updating an existing scholarship
    update: [
        (0, express_validator_1.param)('id')
            .isInt()
            .withMessage('Scholarship ID must be an integer'),
        (0, express_validator_1.body)('title')
            .optional()
            .isLength({ max: 200 })
            .withMessage('Title cannot exceed 200 characters'),
        (0, express_validator_1.body)('description')
            .optional()
            .isLength({ max: 5000 })
            .withMessage('Description cannot exceed 5000 characters'),
        (0, express_validator_1.body)('deadline')
            .optional()
            .isISO8601()
            .withMessage('Deadline must be a valid date'),
        (0, express_validator_1.body)('level')
            .optional()
            .isIn(['Undergraduate', 'Graduate', 'PhD'])
            .withMessage('Level must be one of: Undergraduate, Graduate, PhD'),
        (0, express_validator_1.body)('country')
            .optional()
            .isString()
            .withMessage('Country must be a string'),
        (0, express_validator_1.body)('isOpen')
            .optional()
            .isBoolean()
            .withMessage('isOpen must be a boolean'),
        (0, express_validator_1.body)('financial_benefits_summary')
            .optional()
            .isString()
            .withMessage('Financial benefits summary must be a string')
            .isLength({ max: 1000 })
            .withMessage('Financial benefits summary cannot exceed 1000 characters'),
        (0, express_validator_1.body)('eligibility_summary')
            .optional()
            .isString()
            .withMessage('Eligibility summary must be a string')
            .isLength({ max: 1000 })
            .withMessage('Eligibility summary cannot exceed 1000 characters'),
        (0, express_validator_1.body)('scholarship_link')
            .optional()
            .isURL()
            .withMessage('Scholarship link must be a valid URL'),
        (0, express_validator_1.body)('youtube_link')
            .optional()
            .isURL()
            .withMessage('YouTube link must be a valid URL'),
        exports.validateRequest,
    ],
    // Rules for deleting a scholarship
    delete: [
        (0, express_validator_1.param)('id')
            .isInt()
            .withMessage('Scholarship ID must be an integer'),
        exports.validateRequest,
    ],
};
// User validation rules
exports.userValidationRules = {
    // Rules for creating a new user
    create: [
        (0, express_validator_1.body)('name')
            .notEmpty()
            .withMessage('Name is required')
            .isLength({ max: 100 })
            .withMessage('Name cannot exceed 100 characters'),
        (0, express_validator_1.body)('email')
            .notEmpty()
            .withMessage('Email is required')
            .isEmail()
            .withMessage('Email must be a valid email address'),
        (0, express_validator_1.body)('password')
            .notEmpty()
            .withMessage('Password is required')
            .isLength({ min: 8 })
            .withMessage('Password must be at least 8 characters long')
            .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
            .withMessage('Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'),
        exports.validateRequest,
    ],
    // Rules for updating a user
    update: [
        (0, express_validator_1.body)('name')
            .optional()
            .isLength({ max: 100 })
            .withMessage('Name cannot exceed 100 characters'),
        (0, express_validator_1.body)('email')
            .optional()
            .isEmail()
            .withMessage('Email must be a valid email address'),
        exports.validateRequest,
    ],
    // Rules for changing password
    changePassword: [
        (0, express_validator_1.body)('currentPassword')
            .notEmpty()
            .withMessage('Current password is required'),
        (0, express_validator_1.body)('newPassword')
            .notEmpty()
            .withMessage('New password is required')
            .isLength({ min: 8 })
            .withMessage('New password must be at least 8 characters long')
            .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
            .withMessage('New password must include at least one uppercase letter, one lowercase letter, one number, and one special character'),
        exports.validateRequest,
    ],
};
// Admin validation rules
exports.adminValidationRules = {
    // Rules for creating a new admin
    create: [
        (0, express_validator_1.body)('name')
            .notEmpty()
            .withMessage('Name is required')
            .isLength({ max: 100 })
            .withMessage('Name cannot exceed 100 characters'),
        (0, express_validator_1.body)('email')
            .notEmpty()
            .withMessage('Email is required')
            .isEmail()
            .withMessage('Email must be a valid email address'),
        (0, express_validator_1.body)('password')
            .notEmpty()
            .withMessage('Password is required')
            .isLength({ min: 8 })
            .withMessage('Password must be at least 8 characters long')
            .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
            .withMessage('Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'),
        (0, express_validator_1.body)('privileges')
            .notEmpty()
            .withMessage('Privileges are required')
            .isArray()
            .withMessage('Privileges must be an array'),
        (0, express_validator_1.body)('isMainAdmin')
            .optional()
            .isBoolean()
            .withMessage('isMainAdmin must be a boolean'),
        exports.validateRequest,
    ],
    // Rules for updating an admin
    update: [
        (0, express_validator_1.body)('name')
            .optional()
            .isLength({ max: 100 })
            .withMessage('Name cannot exceed 100 characters'),
        (0, express_validator_1.body)('email')
            .optional()
            .isEmail()
            .withMessage('Email must be a valid email address'),
        (0, express_validator_1.body)('privileges')
            .optional()
            .isArray()
            .withMessage('Privileges must be an array'),
        (0, express_validator_1.body)('isMainAdmin')
            .optional()
            .isBoolean()
            .withMessage('isMainAdmin must be a boolean'),
        exports.validateRequest,
    ],
};
