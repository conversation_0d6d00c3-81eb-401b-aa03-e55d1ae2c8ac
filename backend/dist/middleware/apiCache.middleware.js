"use strict";
/**
 * API Response Caching Middleware
 *
 * This middleware adds HTTP caching headers to API responses
 * and implements server-side caching for frequently accessed endpoints.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheApiResponse = exports.addCacheHeaders = exports.invalidateCache = exports.clearCache = exports.getCacheStats = void 0;
const node_cache_1 = __importDefault(require("node-cache"));
const logger_1 = require("../utils/logger");
// Cache configuration
const CACHE_TTL = 300; // Default cache TTL in seconds (5 minutes)
const CACHE_CHECK_PERIOD = 600; // Check for expired cache entries every 10 minutes
// Create a cache instance
const apiCache = new node_cache_1.default({
    stdTTL: CACHE_TTL,
    checkperiod: CACHE_CHECK_PERIOD,
    useClones: false, // For better performance
});
// Cache statistics
const cacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    invalidations: 0,
};
/**
 * Generate a cache key from request
 * @param req Express request
 * @returns Cache key
 */
const generateCacheKey = (req) => {
    return `${req.method}:${req.originalUrl}`;
};
/**
 * Check if a request should be cached
 * @param req Express request
 * @returns Whether the request should be cached
 */
const shouldCacheRequest = (req) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
        return false;
    }
    // Don't cache authenticated requests
    if (req.user) {
        return false;
    }
    // Don't cache requests with query parameters that should invalidate cache
    if (req.query.nocache === 'true') {
        return false;
    }
    // Cache specific endpoints
    const cachableEndpoints = [
        '/api/scholarships',
        '/api/scholarships/search',
        '/api/scholarships/',
        '/api/health',
    ];
    return cachableEndpoints.some(endpoint => req.originalUrl.startsWith(endpoint));
};
/**
 * Get cache statistics
 * @returns Cache statistics
 */
const getCacheStats = () => {
    return { ...cacheStats };
};
exports.getCacheStats = getCacheStats;
/**
 * Clear the entire cache
 */
const clearCache = () => {
    apiCache.flushAll();
    logger_1.cacheLogger.clear({ type: 'api' });
};
exports.clearCache = clearCache;
/**
 * Invalidate cache for a specific path
 * @param path Path to invalidate
 */
const invalidateCache = (path) => {
    // Get all cache keys
    const keys = apiCache.keys();
    // Filter keys for the specified path
    const pathKeys = keys.filter(key => key.includes(path));
    // Delete matching keys
    if (pathKeys.length > 0) {
        apiCache.del(pathKeys);
        cacheStats.invalidations += pathKeys.length;
        logger_1.cacheLogger.invalidate(path, { count: pathKeys.length, keys: pathKeys });
    }
};
exports.invalidateCache = invalidateCache;
/**
 * Middleware to add cache headers to responses
 * @param ttl Cache TTL in seconds
 */
const addCacheHeaders = (ttl = CACHE_TTL) => {
    return (req, res, next) => {
        // Skip for non-GET requests or authenticated requests
        if (req.method !== 'GET' || req.user) {
            return next();
        }
        // Set cache headers
        res.set('Cache-Control', `public, max-age=${ttl}`);
        res.set('Expires', new Date(Date.now() + ttl * 1000).toUTCString());
        next();
    };
};
exports.addCacheHeaders = addCacheHeaders;
/**
 * Middleware to cache API responses
 * @param ttl Cache TTL in seconds
 */
const cacheApiResponse = (ttl = CACHE_TTL) => {
    return (req, res, next) => {
        // Skip if request should not be cached
        if (!shouldCacheRequest(req)) {
            return next();
        }
        // Generate cache key
        const cacheKey = generateCacheKey(req);
        // Try to get from cache
        const cachedResponse = apiCache.get(cacheKey);
        if (cachedResponse) {
            cacheStats.hits++;
            logger_1.cacheLogger.hit(cacheKey, { url: req.originalUrl, method: req.method });
            // Set cache headers
            res.set('Cache-Control', `public, max-age=${ttl}`);
            res.set('Expires', new Date(Date.now() + ttl * 1000).toUTCString());
            res.set('X-Cache', 'HIT');
            // Send cached response
            return res.status(200).json(cachedResponse);
        }
        // Cache miss
        cacheStats.misses++;
        logger_1.cacheLogger.miss(cacheKey, { url: req.originalUrl, method: req.method });
        // Store original send method
        const originalSend = res.json;
        // Override send method to cache response
        res.json = function (body) {
            // Set cache headers
            res.set('Cache-Control', `public, max-age=${ttl}`);
            res.set('Expires', new Date(Date.now() + ttl * 1000).toUTCString());
            res.set('X-Cache', 'MISS');
            // Cache the response if it's successful
            if (res.statusCode >= 200 && res.statusCode < 300) {
                apiCache.set(cacheKey, body, ttl);
                cacheStats.sets++;
                logger_1.cacheLogger.set(cacheKey, ttl, { url: req.originalUrl, method: req.method });
            }
            // Call original send method
            return originalSend.call(this, body);
        };
        next();
    };
};
exports.cacheApiResponse = cacheApiResponse;
exports.default = {
    addCacheHeaders: exports.addCacheHeaders,
    cacheApiResponse: exports.cacheApiResponse,
    invalidateCache: exports.invalidateCache,
    getCacheStats: exports.getCacheStats,
    clearCache: exports.clearCache,
};
