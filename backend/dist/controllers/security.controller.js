"use strict";
/**
 * Security Controller
 *
 * Handles security-related operations such as fetching security events,
 * managing security settings, and detecting anomalies.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateSecuritySettings = exports.getSecuritySettings = exports.getSecurityEvents = void 0;
const express_validator_1 = require("express-validator");
const client_1 = require("@prisma/client");
const apiResponse_1 = require("../utils/apiResponse");
const authLogger_1 = __importStar(require("../utils/authLogger"));
const anomalyDetection_1 = require("../utils/anomalyDetection");
const prisma = new client_1.PrismaClient();
/**
 * Get security events with optional filtering
 */
const getSecurityEvents = async (req, res) => {
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors);
        }
        // Parse query parameters
        const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
        const endDate = req.query.endDate ? new Date(req.query.endDate) : undefined;
        const eventType = req.query.eventType;
        const severity = req.query.severity;
        const userId = req.query.userId ? parseInt(req.query.userId) : undefined;
        const email = req.query.email;
        const ip = req.query.ip;
        // Build where clause
        const where = {};
        // Date range filter
        if (startDate && endDate) {
            where.timestamp = {
                gte: startDate,
                lte: endDate,
            };
        }
        else if (startDate) {
            where.timestamp = {
                gte: startDate,
            };
        }
        else if (endDate) {
            where.timestamp = {
                lte: endDate,
            };
        }
        // Other filters
        if (eventType)
            where.eventType = eventType;
        if (severity)
            where.severity = severity;
        if (userId)
            where.userId = userId;
        if (email)
            where.email = { contains: email };
        if (ip)
            where.ip = ip;
        // Fetch events from database
        const events = await prisma.securityEvent.findMany({
            where,
            orderBy: {
                timestamp: 'desc',
            },
            take: 1000, // Limit to 1000 events for performance
        });
        // Calculate statistics
        const stats = {
            totalEvents: events.length,
            successfulLogins: events.filter(e => e.eventType === authLogger_1.AuthEventType.LOGIN_SUCCESS).length,
            failedLogins: events.filter(e => e.eventType === authLogger_1.AuthEventType.LOGIN_FAILURE).length,
            suspiciousActivities: events.filter(e => e.eventType === authLogger_1.AuthEventType.SUSPICIOUS_ACTIVITY).length,
            accountLockouts: events.filter(e => e.eventType === authLogger_1.AuthEventType.ACCOUNT_LOCKED).length,
            twoFactorSuccesses: events.filter(e => e.eventType === authLogger_1.AuthEventType.TWO_FACTOR_SUCCESS).length,
            twoFactorFailures: events.filter(e => e.eventType === authLogger_1.AuthEventType.TWO_FACTOR_FAILURE).length,
        };
        // Check for anomalies
        const anomalies = (0, anomalyDetection_1.detectAnomalies)(events);
        // Return events and stats
        return (0, apiResponse_1.sendSuccess)(res, {
            events,
            stats,
            anomalies,
        }, 'Security events retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching security events:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch security events', error);
    }
};
exports.getSecurityEvents = getSecurityEvents;
/**
 * Get security settings
 */
const getSecuritySettings = async (req, res) => {
    try {
        // Fetch security settings from database
        const settings = await prisma.securitySettings.findFirst();
        // Return settings
        return (0, apiResponse_1.sendSuccess)(res, { settings }, 'Security settings retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching security settings:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch security settings', error);
    }
};
exports.getSecuritySettings = getSecuritySettings;
/**
 * Update security settings
 */
const updateSecuritySettings = async (req, res) => {
    var _a, _b;
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors);
        }
        const { maxLoginAttempts, lockoutDuration, passwordExpiryDays, requireStrongPasswords, twoFactorEnabled, minPasswordLength, } = req.body;
        // Update settings in database
        const settings = await prisma.securitySettings.upsert({
            where: { id: 1 }, // Assuming there's only one settings record
            update: {
                maxLoginAttempts,
                lockoutDuration,
                passwordExpiryDays,
                requireStrongPasswords,
                twoFactorEnabled,
                minPasswordLength,
            },
            create: {
                maxLoginAttempts,
                lockoutDuration,
                passwordExpiryDays,
                requireStrongPasswords,
                twoFactorEnabled,
                minPasswordLength,
            },
        });
        // Log the settings update
        authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.SECURITY_SETTINGS_UPDATED, 'Security settings updated', {
            adminId: (_a = req.user) === null || _a === void 0 ? void 0 : _a.id,
            email: (_b = req.user) === null || _b === void 0 ? void 0 : _b.email,
            settings,
        }, authLogger_1.SecurityLevel.INFO);
        // Return updated settings
        return (0, apiResponse_1.sendSuccess)(res, { settings }, 'Security settings updated successfully');
    }
    catch (error) {
        console.error('Error updating security settings:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to update security settings', error);
    }
};
exports.updateSecuritySettings = updateSecuritySettings;
exports.default = {
    getSecurityEvents: exports.getSecurityEvents,
    getSecuritySettings: exports.getSecuritySettings,
    updateSecuritySettings: exports.updateSecuritySettings,
};
