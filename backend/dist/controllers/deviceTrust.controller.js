"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivateDevice = exports.untrustDevice = exports.trustDevice = exports.getDeviceById = exports.getAdminDevices = void 0;
const database_1 = require("../config/database");
const apiResponse_1 = require("../utils/apiResponse");
const securityMonitor_1 = require("../utils/securityMonitor");
const authLogger_1 = require("../utils/authLogger");
// Get all devices for an admin
const getAdminDevices = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        const { page = '1', limit = '10', trusted, active } = req.query;
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const offset = (pageNumber - 1) * limitNumber;
        // Build WHERE clause
        let whereClause = 'WHERE admin_id = $1';
        const params = [adminId];
        let paramCount = 2;
        if (trusted !== undefined) {
            whereClause += ` AND trusted = $${paramCount}`;
            params.push(trusted === 'true');
            paramCount++;
        }
        if (active !== undefined) {
            whereClause += ` AND is_active = $${paramCount}`;
            params.push(active === 'true');
            paramCount++;
        }
        // Get total count
        const countResult = await (0, database_1.query)(`
      SELECT COUNT(*) as total FROM trusted_devices ${whereClause}
    `, params);
        // Get devices with pagination
        const devicesResult = await (0, database_1.query)(`
      SELECT 
        id,
        device_fingerprint,
        device_name,
        device_type,
        browser,
        os,
        first_seen,
        last_seen,
        trusted,
        trusted_at,
        ip_addresses,
        is_active
      FROM trusted_devices 
      ${whereClause}
      ORDER BY last_seen DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...params, limitNumber, offset]);
        const total = parseInt(countResult.rows[0].total);
        const totalPages = Math.ceil(total / limitNumber);
        // Process devices to add computed fields
        const devices = devicesResult.rows.map(device => ({
            ...device,
            ipAddresses: device.ip_addresses || [],
            riskScore: calculateDeviceRiskScore(device),
            lastSeenFormatted: new Date(device.last_seen).toLocaleString(),
            firstSeenFormatted: new Date(device.first_seen).toLocaleString(),
            trustedAtFormatted: device.trusted_at ? new Date(device.trusted_at).toLocaleString() : null
        }));
        return (0, apiResponse_1.sendPaginatedSuccess)(res, devices, {
            page: pageNumber,
            limit: limitNumber,
            total,
            totalPages
        }, 'Devices retrieved successfully');
    }
    catch (error) {
        console.error('Get admin devices error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve devices', error);
    }
};
exports.getAdminDevices = getAdminDevices;
// Get device details by ID
const getDeviceById = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const deviceId = parseInt(req.params.id);
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        if (isNaN(deviceId)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid device ID', null, 400);
        }
        const deviceResult = await (0, database_1.query)(`
      SELECT 
        td.*,
        COUNT(se.id) as security_events_count,
        MAX(se.timestamp) as last_security_event
      FROM trusted_devices td
      LEFT JOIN security_events se ON se.device_fingerprint = td.device_fingerprint
      WHERE td.id = $1 AND td.admin_id = $2
      GROUP BY td.id
    `, [deviceId, adminId]);
        if (deviceResult.rows.length === 0) {
            return (0, apiResponse_1.sendNotFound)(res, 'Device not found');
        }
        const device = deviceResult.rows[0];
        // Get recent security events for this device
        const eventsResult = await (0, database_1.query)(`
      SELECT event_type, message, timestamp, severity, risk_score
      FROM security_events 
      WHERE device_fingerprint = $1 
      ORDER BY timestamp DESC 
      LIMIT 10
    `, [device.device_fingerprint]);
        // Get login history for this device
        const loginResult = await (0, database_1.query)(`
      SELECT timestamp, success, ip, geolocation
      FROM login_attempts 
      WHERE device_fingerprint = $1 
      ORDER BY timestamp DESC 
      LIMIT 20
    `, [device.device_fingerprint]);
        const deviceDetails = {
            ...device,
            ipAddresses: device.ip_addresses || [],
            riskScore: calculateDeviceRiskScore(device),
            securityEventsCount: parseInt(device.security_events_count),
            lastSecurityEvent: device.last_security_event,
            recentEvents: eventsResult.rows,
            loginHistory: loginResult.rows.map(login => ({
                ...login,
                geolocation: login.geolocation || {}
            }))
        };
        return (0, apiResponse_1.sendSuccess)(res, deviceDetails, 'Device details retrieved successfully');
    }
    catch (error) {
        console.error('Get device by ID error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve device details', error);
    }
};
exports.getDeviceById = getDeviceById;
// Trust a device
const trustDevice = async (req, res) => {
    var _a, _b;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const deviceId = parseInt(req.params.id);
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        if (isNaN(deviceId)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid device ID', null, 400);
        }
        // Check if device exists and belongs to admin
        const deviceResult = await (0, database_1.query)(`
      SELECT * FROM trusted_devices 
      WHERE id = $1 AND admin_id = $2
    `, [deviceId, adminId]);
        if (deviceResult.rows.length === 0) {
            return (0, apiResponse_1.sendNotFound)(res, 'Device not found');
        }
        const device = deviceResult.rows[0];
        if (device.trusted) {
            return (0, apiResponse_1.sendError)(res, 'Device is already trusted', null, 400);
        }
        // Trust the device
        await (0, database_1.query)(`
      UPDATE trusted_devices 
      SET trusted = true, trusted_at = CURRENT_TIMESTAMP 
      WHERE id = $1
    `, [deviceId]);
        // Log security event
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.CONFIGURATION_CHANGE,
            message: `Device trusted: ${device.device_name}`,
            adminId,
            email: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.email) || '',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.INFO,
            sessionId: req.sessionID,
            deviceFingerprint: device.device_fingerprint,
            details: {
                action: 'TRUST_DEVICE',
                deviceId,
                deviceName: device.device_name,
                deviceType: device.device_type
            }
        });
        return (0, apiResponse_1.sendSuccess)(res, { deviceId, trusted: true }, 'Device trusted successfully');
    }
    catch (error) {
        console.error('Trust device error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to trust device', error);
    }
};
exports.trustDevice = trustDevice;
// Untrust a device
const untrustDevice = async (req, res) => {
    var _a, _b;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const deviceId = parseInt(req.params.id);
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        if (isNaN(deviceId)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid device ID', null, 400);
        }
        // Check if device exists and belongs to admin
        const deviceResult = await (0, database_1.query)(`
      SELECT * FROM trusted_devices 
      WHERE id = $1 AND admin_id = $2
    `, [deviceId, adminId]);
        if (deviceResult.rows.length === 0) {
            return (0, apiResponse_1.sendNotFound)(res, 'Device not found');
        }
        const device = deviceResult.rows[0];
        // Untrust the device
        await (0, database_1.query)(`
      UPDATE trusted_devices 
      SET trusted = false, trusted_at = NULL 
      WHERE id = $1
    `, [deviceId]);
        // Log security event
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.CONFIGURATION_CHANGE,
            message: `Device untrusted: ${device.device_name}`,
            adminId,
            email: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.email) || '',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.WARNING,
            sessionId: req.sessionID,
            deviceFingerprint: device.device_fingerprint,
            details: {
                action: 'UNTRUST_DEVICE',
                deviceId,
                deviceName: device.device_name,
                deviceType: device.device_type
            }
        });
        return (0, apiResponse_1.sendSuccess)(res, { deviceId, trusted: false }, 'Device untrusted successfully');
    }
    catch (error) {
        console.error('Untrust device error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to untrust device', error);
    }
};
exports.untrustDevice = untrustDevice;
// Deactivate a device
const deactivateDevice = async (req, res) => {
    var _a, _b;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const deviceId = parseInt(req.params.id);
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        if (isNaN(deviceId)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid device ID', null, 400);
        }
        // Check if device exists and belongs to admin
        const deviceResult = await (0, database_1.query)(`
      SELECT * FROM trusted_devices 
      WHERE id = $1 AND admin_id = $2
    `, [deviceId, adminId]);
        if (deviceResult.rows.length === 0) {
            return (0, apiResponse_1.sendNotFound)(res, 'Device not found');
        }
        const device = deviceResult.rows[0];
        // Deactivate the device
        await (0, database_1.query)(`
      UPDATE trusted_devices 
      SET is_active = false, trusted = false, trusted_at = NULL 
      WHERE id = $1
    `, [deviceId]);
        // Log security event
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.CONFIGURATION_CHANGE,
            message: `Device deactivated: ${device.device_name}`,
            adminId,
            email: ((_b = req.user) === null || _b === void 0 ? void 0 : _b.email) || '',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.WARNING,
            sessionId: req.sessionID,
            deviceFingerprint: device.device_fingerprint,
            details: {
                action: 'DEACTIVATE_DEVICE',
                deviceId,
                deviceName: device.device_name,
                deviceType: device.device_type
            }
        });
        return (0, apiResponse_1.sendSuccess)(res, { deviceId, active: false }, 'Device deactivated successfully');
    }
    catch (error) {
        console.error('Deactivate device error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to deactivate device', error);
    }
};
exports.deactivateDevice = deactivateDevice;
// Calculate device risk score
function calculateDeviceRiskScore(device) {
    let riskScore = 0;
    // Base risk for untrusted devices
    if (!device.trusted) {
        riskScore += 30;
    }
    // Risk based on number of IP addresses used
    const ipCount = device.ip_addresses ? device.ip_addresses.length : 0;
    if (ipCount > 5) {
        riskScore += 20;
    }
    else if (ipCount > 2) {
        riskScore += 10;
    }
    // Risk based on last seen (inactive devices are riskier)
    const daysSinceLastSeen = Math.floor((Date.now() - new Date(device.last_seen).getTime()) / (1000 * 60 * 60 * 24));
    if (daysSinceLastSeen > 30) {
        riskScore += 25;
    }
    else if (daysSinceLastSeen > 7) {
        riskScore += 10;
    }
    // Risk based on device type (mobile devices might be riskier)
    if (device.device_type === 'mobile') {
        riskScore += 5;
    }
    return Math.min(100, riskScore);
}
