"use strict";
/**
 * Account Recovery Controller
 *
 * Handles advanced account recovery operations beyond simple password reset,
 * including multi-factor recovery options and account unlock.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.unlockAccount = exports.resetPassword = exports.verifyRecoveryToken = exports.requestRecovery = void 0;
const express_validator_1 = require("express-validator");
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
const email_1 = require("../utils/email");
const apiResponse_1 = require("../utils/apiResponse");
const authLogger_1 = __importStar(require("../utils/authLogger"));
const passwordPolicy_1 = __importDefault(require("../utils/passwordPolicy"));
const prisma = new client_1.PrismaClient();
/**
 * Request account recovery
 * Initiates the account recovery process by sending a recovery token
 */
const requestRecovery = async (req, res) => {
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors);
        }
        const { email, accountType } = req.body;
        // Determine which model to use based on account type
        const model = accountType === 'admin' ? prisma.admin : prisma.user;
        // Find the account
        const account = await model.findUnique({
            where: { email },
        });
        // Always return success even if account doesn't exist (security best practice)
        if (!account) {
            // Log the attempt but don't reveal that the account doesn't exist
            authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.RECOVERY_REQUEST, `Recovery requested for non-existent account: ${email}`, { email, accountType, ip: req.ip }, authLogger_1.SecurityLevel.INFO);
            return (0, apiResponse_1.sendSuccess)(res, null, 'If your account exists, recovery instructions have been sent to your email');
        }
        // Generate recovery token
        const recoveryToken = crypto_1.default.randomBytes(32).toString('hex');
        const recoveryTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
        // Update account with recovery token
        await model.update({
            where: { id: account.id },
            data: {
                resetPasswordToken: recoveryToken,
                resetPasswordExpires: recoveryTokenExpiry,
            },
        });
        // Send recovery email
        const recoveryLink = `${process.env.FRONTEND_URL}/account-recovery/${accountType}/${recoveryToken}`;
        await (0, email_1.sendEmail)({
            to: email,
            subject: 'Account Recovery - MaBourse',
            html: `
        <h1>Account Recovery</h1>
        <p>You requested to recover your account. Click the link below to continue:</p>
        <a href="${recoveryLink}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Recover Account</a>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this, please ignore this email or contact support if you're concerned.</p>
      `,
        });
        // Log the recovery request
        authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.RECOVERY_REQUEST, `Recovery requested for account: ${email}`, { accountId: account.id, email, accountType, ip: req.ip }, authLogger_1.SecurityLevel.INFO);
        return (0, apiResponse_1.sendSuccess)(res, null, 'Recovery instructions have been sent to your email');
    }
    catch (error) {
        console.error('Error requesting account recovery:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to request account recovery', error);
    }
};
exports.requestRecovery = requestRecovery;
/**
 * Verify recovery token
 * Checks if the recovery token is valid and returns account info
 */
const verifyRecoveryToken = async (req, res) => {
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors);
        }
        const { token, accountType } = req.params;
        // Determine which model to use based on account type
        const model = accountType === 'admin' ? prisma.admin : prisma.user;
        // Find account with the token
        const account = await model.findFirst({
            where: {
                resetPasswordToken: token,
                resetPasswordExpires: {
                    gt: new Date(),
                },
            },
        });
        if (!account) {
            return (0, apiResponse_1.sendError)(res, 'Recovery token is invalid or has expired', null, 401);
        }
        // Log the token verification
        authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.RECOVERY_TOKEN_VERIFIED, `Recovery token verified for account: ${account.email}`, { accountId: account.id, email: account.email, accountType, ip: req.ip }, authLogger_1.SecurityLevel.INFO);
        return (0, apiResponse_1.sendSuccess)(res, {
            email: account.email,
            name: account.name,
            hasTwoFactor: account.twoFactorEnabled,
        }, 'Recovery token is valid');
    }
    catch (error) {
        console.error('Error verifying recovery token:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to verify recovery token', error);
    }
};
exports.verifyRecoveryToken = verifyRecoveryToken;
/**
 * Reset password with recovery token
 * Resets the password using a valid recovery token
 */
const resetPassword = async (req, res) => {
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors);
        }
        const { token, accountType } = req.params;
        const { password } = req.body;
        // Determine which model to use based on account type
        const model = accountType === 'admin' ? prisma.admin : prisma.user;
        // Find account with the token
        const account = await model.findFirst({
            where: {
                resetPasswordToken: token,
                resetPasswordExpires: {
                    gt: new Date(),
                },
            },
        });
        if (!account) {
            return (0, apiResponse_1.sendError)(res, 'Recovery token is invalid or has expired', null, 401);
        }
        // Validate password strength
        const passwordValidation = passwordPolicy_1.default.validatePasswordStrength(password);
        if (!passwordValidation.valid) {
            return (0, apiResponse_1.sendError)(res, passwordValidation.message || 'Password does not meet security requirements', null, 400);
        }
        // Check if password is in history
        const isInHistory = await passwordPolicy_1.default.isPasswordInHistory(account.id, password);
        if (isInHistory) {
            return (0, apiResponse_1.sendError)(res, 'Password has been used recently. Please choose a different password.', null, 400);
        }
        // Hash the new password
        const salt = await bcryptjs_1.default.genSalt(10);
        const hashedPassword = await bcryptjs_1.default.hash(password, salt);
        // Calculate password expiry date
        const now = new Date();
        const expiryDays = 90; // Default to 90 days
        const expiryDate = new Date(now);
        expiryDate.setDate(expiryDate.getDate() + expiryDays);
        // Update account with new password
        await model.update({
            where: { id: account.id },
            data: {
                password: hashedPassword,
                resetPasswordToken: null,
                resetPasswordExpires: null,
                failedLoginAttempts: 0,
                lockUntil: null,
                passwordUpdatedAt: now,
                passwordExpiresAt: expiryDate,
                mustChangePassword: false,
            },
        });
        // Add password to history
        await passwordPolicy_1.default.addPasswordToHistory(account.id, hashedPassword);
        // Log the password reset
        authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.PASSWORD_RESET_SUCCESS, `Password reset successful for account: ${account.email}`, { accountId: account.id, email: account.email, accountType, ip: req.ip }, authLogger_1.SecurityLevel.INFO);
        // Send confirmation email
        await (0, email_1.sendEmail)({
            to: account.email,
            subject: 'Password Reset Confirmation - MaBourse',
            html: `
        <h1>Password Reset Confirmation</h1>
        <p>Your password has been successfully reset.</p>
        <p>If you didn't make this change, please contact support immediately.</p>
      `,
        });
        return (0, apiResponse_1.sendSuccess)(res, null, 'Password has been reset successfully');
    }
    catch (error) {
        console.error('Error resetting password:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to reset password', error);
    }
};
exports.resetPassword = resetPassword;
/**
 * Unlock account
 * Unlocks a locked account using a recovery token
 */
const unlockAccount = async (req, res) => {
    try {
        // Validate request
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors);
        }
        const { token, accountType } = req.params;
        // Determine which model to use based on account type
        const model = accountType === 'admin' ? prisma.admin : prisma.user;
        // Find account with the token
        const account = await model.findFirst({
            where: {
                resetPasswordToken: token,
                resetPasswordExpires: {
                    gt: new Date(),
                },
            },
        });
        if (!account) {
            return (0, apiResponse_1.sendError)(res, 'Recovery token is invalid or has expired', null, 401);
        }
        // Update account to unlock it
        await model.update({
            where: { id: account.id },
            data: {
                failedLoginAttempts: 0,
                lockUntil: null,
                resetPasswordToken: null,
                resetPasswordExpires: null,
            },
        });
        // Log the account unlock
        authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.ACCOUNT_UNLOCKED, `Account unlocked: ${account.email}`, { accountId: account.id, email: account.email, accountType, ip: req.ip }, authLogger_1.SecurityLevel.INFO);
        // Send confirmation email
        await (0, email_1.sendEmail)({
            to: account.email,
            subject: 'Account Unlocked - MaBourse',
            html: `
        <h1>Account Unlocked</h1>
        <p>Your account has been successfully unlocked.</p>
        <p>You can now log in with your credentials.</p>
        <p>If you didn't request this, please contact support immediately.</p>
      `,
        });
        return (0, apiResponse_1.sendSuccess)(res, null, 'Account has been unlocked successfully');
    }
    catch (error) {
        console.error('Error unlocking account:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to unlock account', error);
    }
};
exports.unlockAccount = unlockAccount;
exports.default = {
    requestRecovery: exports.requestRecovery,
    verifyRecoveryToken: exports.verifyRecoveryToken,
    resetPassword: exports.resetPassword,
    unlockAccount: exports.unlockAccount,
};
