"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTwoFactorStatus = exports.disableTwoFactor = exports.verifyTwoFactorToken = exports.verifyAndEnableTwoFactor = exports.initializeTwoFactor = void 0;
const express_validator_1 = require("express-validator");
const Admin_1 = require("../models/Admin");
const twoFactor_1 = require("../utils/twoFactor");
const apiResponse_1 = require("../utils/apiResponse");
const securityMonitor_1 = require("../utils/securityMonitor");
const authLogger_1 = require("../utils/authLogger");
// Initialize 2FA setup
const initializeTwoFactor = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        // Get admin
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return (0, apiResponse_1.sendNotFound)(res, 'Admin not found');
        }
        // Check if 2FA is already enabled
        if (admin.twoFactorEnabled) {
            return (0, apiResponse_1.sendError)(res, '2FA is already enabled', null, 400);
        }
        // Generate a new secret
        const secret = (0, twoFactor_1.generateSecret)(admin.email);
        // Generate QR code
        const qrCode = await (0, twoFactor_1.generateQRCode)(secret, admin.email);
        // Save temporary secret
        await Admin_1.Admin.update(adminId, { twoFactorTempSecret: secret });
        // Log security event
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.TWO_FACTOR_SETUP_INITIATED,
            message: '2FA setup initiated',
            adminId,
            email: admin.email,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.INFO,
            sessionId: req.sessionID,
            details: { action: 'INITIALIZE_2FA' }
        });
        return (0, apiResponse_1.sendSuccess)(res, {
            qrCode,
            secret // Include secret for manual entry if QR code doesn't work
        }, '2FA initialization successful');
    }
    catch (error) {
        console.error('Initialize 2FA error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to initialize 2FA', error);
    }
};
exports.initializeTwoFactor = initializeTwoFactor;
// Verify and enable 2FA
const verifyAndEnableTwoFactor = async (req, res) => {
    var _a;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { token } = req.body;
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        // Get admin
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return (0, apiResponse_1.sendNotFound)(res, 'Admin not found');
        }
        // Check if 2FA is already enabled
        if (admin.twoFactorEnabled) {
            return (0, apiResponse_1.sendError)(res, '2FA is already enabled', null, 400);
        }
        // Check if temporary secret exists
        if (!admin.twoFactorTempSecret) {
            return (0, apiResponse_1.sendError)(res, '2FA setup not initialized', null, 400);
        }
        // Verify token
        const isValid = (0, twoFactor_1.verifyToken)(token, admin.twoFactorTempSecret);
        if (!isValid) {
            // Log failed verification
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.TWO_FACTOR_VERIFICATION_FAILED,
                message: '2FA verification failed during setup',
                adminId,
                email: admin.email,
                ip: req.ip || 'unknown',
                userAgent: req.get('User-Agent'),
                severity: authLogger_1.SecurityLevel.WARNING,
                sessionId: req.sessionID,
                details: { action: 'VERIFY_2FA_SETUP', reason: 'INVALID_TOKEN' }
            });
            return (0, apiResponse_1.sendError)(res, 'Invalid token', null, 400);
        }
        // Generate backup codes
        const backupCodes = (0, twoFactor_1.generateBackupCodes)();
        // Enable 2FA
        await Admin_1.Admin.update(adminId, {
            twoFactorSecret: admin.twoFactorTempSecret,
            twoFactorEnabled: true,
            twoFactorTempSecret: undefined,
            twoFactorBackupCodes: backupCodes
        });
        // Log successful 2FA enablement
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.TWO_FACTOR_ENABLED,
            message: '2FA successfully enabled',
            adminId,
            email: admin.email,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.INFO,
            sessionId: req.sessionID,
            details: { action: 'ENABLE_2FA', backupCodesGenerated: backupCodes.length }
        });
        return (0, apiResponse_1.sendSuccess)(res, {
            backupCodes
        }, '2FA enabled successfully');
    }
    catch (error) {
        console.error('Verify and enable 2FA error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to enable 2FA', error);
    }
};
exports.verifyAndEnableTwoFactor = verifyAndEnableTwoFactor;
// Verify 2FA token during login
const verifyTwoFactorToken = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { adminId, token, isBackupCode = false } = req.body;
        // Get admin
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return (0, apiResponse_1.sendNotFound)(res, 'Admin not found');
        }
        // Check if 2FA is enabled
        if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
            return (0, apiResponse_1.sendError)(res, '2FA is not enabled for this account', null, 400);
        }
        let isValid = false;
        let updatedBackupCodes = null;
        if (isBackupCode) {
            // Verify backup code
            if (!admin.twoFactorBackupCodes || admin.twoFactorBackupCodes.length === 0) {
                return (0, apiResponse_1.sendError)(res, 'No backup codes available', null, 400);
            }
            const backupCodes = admin.twoFactorBackupCodes;
            const result = (0, twoFactor_1.verifyBackupCode)(token, backupCodes);
            isValid = result.valid;
            updatedBackupCodes = result.remainingCodes;
            // Update backup codes if valid
            if (isValid) {
                await Admin_1.Admin.update(adminId, {
                    twoFactorBackupCodes: updatedBackupCodes
                });
            }
        }
        else {
            // Verify TOTP token
            isValid = (0, twoFactor_1.verifyToken)(token, admin.twoFactorSecret);
        }
        if (!isValid) {
            // Log failed verification
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.TWO_FACTOR_VERIFICATION_FAILED,
                message: `2FA verification failed during login (${isBackupCode ? 'backup code' : 'TOTP'})`,
                adminId,
                email: admin.email,
                ip: req.ip || 'unknown',
                userAgent: req.get('User-Agent'),
                severity: authLogger_1.SecurityLevel.WARNING,
                sessionId: req.sessionID,
                details: {
                    action: 'VERIFY_2FA_LOGIN',
                    reason: 'INVALID_TOKEN',
                    tokenType: isBackupCode ? 'BACKUP_CODE' : 'TOTP'
                }
            });
            return (0, apiResponse_1.sendError)(res, 'Invalid token', null, 400);
        }
        // Log successful verification
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.TWO_FACTOR_VERIFIED,
            message: `2FA verification successful (${isBackupCode ? 'backup code' : 'TOTP'})`,
            adminId,
            email: admin.email,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.INFO,
            sessionId: req.sessionID,
            details: {
                action: 'VERIFY_2FA_LOGIN',
                tokenType: isBackupCode ? 'BACKUP_CODE' : 'TOTP',
                remainingBackupCodes: (updatedBackupCodes === null || updatedBackupCodes === void 0 ? void 0 : updatedBackupCodes.length) || null
            }
        });
        return (0, apiResponse_1.sendSuccess)(res, {
            verified: true,
            remainingBackupCodes: (updatedBackupCodes === null || updatedBackupCodes === void 0 ? void 0 : updatedBackupCodes.length) || null
        }, '2FA verification successful');
    }
    catch (error) {
        console.error('Verify 2FA token error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to verify 2FA token', error);
    }
};
exports.verifyTwoFactorToken = verifyTwoFactorToken;
// Disable 2FA
const disableTwoFactor = async (req, res) => {
    var _a;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { token } = req.body;
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        // Get admin
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return (0, apiResponse_1.sendNotFound)(res, 'Admin not found');
        }
        // Check if 2FA is enabled
        if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
            return (0, apiResponse_1.sendError)(res, '2FA is not enabled for this account', null, 400);
        }
        // Verify token
        const isValid = (0, twoFactor_1.verifyToken)(token, admin.twoFactorSecret);
        if (!isValid) {
            // Log failed verification
            await securityMonitor_1.SecurityMonitor.logSecurityEvent({
                eventType: authLogger_1.AuthEventType.TWO_FACTOR_VERIFICATION_FAILED,
                message: '2FA verification failed during disable attempt',
                adminId,
                email: admin.email,
                ip: req.ip || 'unknown',
                userAgent: req.get('User-Agent'),
                severity: authLogger_1.SecurityLevel.WARNING,
                sessionId: req.sessionID,
                details: { action: 'DISABLE_2FA', reason: 'INVALID_TOKEN' }
            });
            return (0, apiResponse_1.sendError)(res, 'Invalid token', null, 400);
        }
        // Disable 2FA
        await Admin_1.Admin.update(adminId, {
            twoFactorSecret: undefined,
            twoFactorEnabled: false,
            twoFactorTempSecret: undefined,
            twoFactorBackupCodes: []
        });
        // Log 2FA disabled
        await securityMonitor_1.SecurityMonitor.logSecurityEvent({
            eventType: authLogger_1.AuthEventType.TWO_FACTOR_DISABLED,
            message: '2FA disabled',
            adminId,
            email: admin.email,
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            severity: authLogger_1.SecurityLevel.WARNING,
            sessionId: req.sessionID,
            details: { action: 'DISABLE_2FA' }
        });
        return (0, apiResponse_1.sendSuccess)(res, null, '2FA disabled successfully');
    }
    catch (error) {
        console.error('Disable 2FA error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to disable 2FA', error);
    }
};
exports.disableTwoFactor = disableTwoFactor;
// Get 2FA status
const getTwoFactorStatus = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Authentication required');
        }
        // Get admin
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return (0, apiResponse_1.sendNotFound)(res, 'Admin not found');
        }
        const backupCodesCount = admin.twoFactorBackupCodes
            ? admin.twoFactorBackupCodes.length
            : 0;
        return (0, apiResponse_1.sendSuccess)(res, {
            enabled: admin.twoFactorEnabled,
            backupCodesCount
        }, '2FA status retrieved successfully');
    }
    catch (error) {
        console.error('Get 2FA status error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to get 2FA status', error);
    }
};
exports.getTwoFactorStatus = getTwoFactorStatus;
