"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUser = exports.deleteUser = exports.getUsers = exports.changePassword = exports.updateProfile = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = require("../models/User");
const apiResponse_1 = require("../utils/apiResponse");
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
// Update user profile
const updateProfile = async (req, res) => {
    var _a;
    try {
        const { name, email } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            return res.status(401).json({ message: 'User not authenticated' });
        }
        const user = await User_1.User.findById(Number(userId));
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        const updateData = {};
        if (name)
            updateData.name = name;
        if (email)
            updateData.email = email;
        const updatedUser = await User_1.User.update(Number(userId), updateData);
        if (!updatedUser) {
            return res.status(500).json({ message: 'Failed to update profile' });
        }
        res.json({
            message: 'Profile updated successfully',
            user: {
                id: updatedUser.id,
                name: updatedUser.name,
                email: updatedUser.email,
                role: updatedUser.role,
            },
        });
    }
    catch (error) {
        console.error('Error updating profile:', error);
        res.status(500).json({ message: 'Error updating profile' });
    }
};
exports.updateProfile = updateProfile;
// Change user password
const changePassword = async (req, res) => {
    var _a;
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'User not authenticated');
        }
        const user = await User_1.User.findById(Number(userId));
        if (!user) {
            return (0, apiResponse_1.sendNotFound)(res, 'User not found');
        }
        // Compare passwords
        if (!user.password) {
            return (0, apiResponse_1.sendError)(res, 'User password not set', null, 400);
        }
        const isMatch = await bcryptjs_1.default.compare(currentPassword, user.password);
        if (!isMatch) {
            return (0, apiResponse_1.sendError)(res, 'Current password is incorrect', null, 400);
        }
        // Hash the new password
        const hashedPassword = await bcryptjs_1.default.hash(newPassword, 10);
        // Update the user's password
        await User_1.User.update(Number(userId), { password: hashedPassword });
        return (0, apiResponse_1.sendSuccess)(res, null, 'Password changed successfully');
    }
    catch (error) {
        console.error('Error changing password:', error);
        return (0, apiResponse_1.sendError)(res, 'Error changing password', error, 500);
    }
};
exports.changePassword = changePassword;
// Get all users (admin only)
const getUsers = async (req, res) => {
    try {
        const users = await User_1.User.findAll();
        res.json(users);
    }
    catch (error) {
        console.error('Error getting users:', error);
        res.status(500).json({ message: 'Error getting users' });
    }
};
exports.getUsers = getUsers;
// Delete user (admin only)
const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User_1.User.findById(Number(id));
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        await User_1.User.delete(Number(id));
        res.json({ message: 'User deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ message: 'Error deleting user' });
    }
};
exports.deleteUser = deleteUser;
// Create a new user
const createUser = async (req, res) => {
    try {
        const { name, email, password, role = 'user' } = req.body;
        // Check if user with this email already exists
        const existingUser = await User_1.User.findByEmail(email);
        if (existingUser) {
            return res.status(409).json({ message: 'User with this email already exists' });
        }
        // Hash the password
        const hashedPassword = await bcryptjs_1.default.hash(password, 10);
        // Create the user
        const user = await User_1.User.create({
            name,
            email,
            password: hashedPassword,
            role,
            failedLoginAttempts: 0,
            mustChangePassword: false
        });
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({
            id: user.id,
            email: user.email,
            role: user.role,
            isMainAdmin: false
        }, JWT_SECRET, { expiresIn: '1h' });
        // Return user data (excluding password) and token
        res.status(201).json({
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            },
            token
        });
    }
    catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ message: 'Error creating user' });
    }
};
exports.createUser = createUser;
