"use strict";
/**
 * Country Controller
 *
 * Handles country-related operations and scholarship filtering by country
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllCountriesForSidebar = exports.searchCountries = exports.getCountryStatistics = exports.getScholarshipsByCountry = exports.getAllCountries = void 0;
const Scholarship_1 = require("../models/Scholarship");
const apiResponse_1 = require("../utils/apiResponse");
const database_1 = require("../config/database");
const dateUtils_1 = __importDefault(require("../utils/dateUtils"));
/**
 * Get all countries with scholarship counts
 */
const getAllCountries = async (req, res) => {
    try {
        // Get distinct countries from scholarships with counts
        const result = await Scholarship_1.Scholarship.getCountriesWithCounts();
        (0, apiResponse_1.sendSuccess)(res, result, 'Countries retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching countries:', error);
        (0, apiResponse_1.sendError)(res, 'Failed to fetch countries', error);
    }
};
exports.getAllCountries = getAllCountries;
/**
 * Get scholarships by country
 */
const getScholarshipsByCountry = async (req, res) => {
    try {
        const { country } = req.params;
        const { page = '1', limit = '12', level, isOpen, sortBy = 'created_at', sortOrder = 'desc' } = req.query;
        if (!country) {
            return (0, apiResponse_1.sendError)(res, 'Country parameter is required', null, 400);
        }
        const decodedCountry = decodeURIComponent(country);
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const offset = (pageNumber - 1) * limitNumber;
        // Build WHERE clause
        let whereClause = 'WHERE country = $1';
        const params = [decodedCountry];
        let paramIndex = 2;
        if (level) {
            whereClause += ` AND level = $${paramIndex}`;
            params.push(level);
            paramIndex++;
        }
        if (isOpen !== undefined) {
            whereClause += ` AND is_open = $${paramIndex}`;
            params.push(isOpen === 'true');
            paramIndex++;
        }
        // Validate sortBy field
        const validSortFields = ['created_at', 'title', 'deadline', 'level'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
        const order = (sortOrder === null || sortOrder === void 0 ? void 0 : sortOrder.toLowerCase()) === 'asc' ? 'ASC' : 'DESC';
        // Get total count
        const countResult = await (0, database_1.query)(`
      SELECT COUNT(*) as total
      FROM scholarships
      ${whereClause}
    `, params);
        const totalCount = parseInt(countResult.rows[0].total);
        // Get scholarships
        const result = await (0, database_1.query)(`
      SELECT
        id, title, description, country, level, deadline, is_open,
        thumbnail, funding_source, created_at, updated_at
      FROM scholarships
      ${whereClause}
      ORDER BY ${sortField} ${order}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...params, limitNumber, offset]);
        // Process scholarships with computed fields
        const scholarships = result.rows.map(row => {
            var _a, _b;
            return ({
                id: row.id,
                title: row.title,
                description: row.description,
                country: row.country,
                level: row.level,
                deadline: row.deadline,
                isOpen: row.is_open,
                thumbnail: row.thumbnail,
                fundingSource: row.funding_source,
                createdAt: (_a = row.created_at) === null || _a === void 0 ? void 0 : _a.toISOString(),
                updatedAt: (_b = row.updated_at) === null || _b === void 0 ? void 0 : _b.toISOString(),
                // Add computed fields for frontend
                isExpired: dateUtils_1.default.isDatePast(row.deadline),
                daysRemaining: dateUtils_1.default.getDaysRemaining(row.deadline),
                formattedDeadline: dateUtils_1.default.formatDate(row.deadline)
            });
        });
        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limitNumber);
        return (0, apiResponse_1.sendPaginatedSuccess)(res, scholarships, {
            page: pageNumber,
            limit: limitNumber,
            total: totalCount,
            totalPages
        }, 'Scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching scholarships by country:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch scholarships', error);
    }
};
exports.getScholarshipsByCountry = getScholarshipsByCountry;
/**
 * Get country statistics
 */
const getCountryStatistics = async (req, res) => {
    try {
        const { country } = req.params;
        if (!country) {
            return (0, apiResponse_1.sendError)(res, 'Country parameter is required', null, 400);
        }
        const decodedCountry = decodeURIComponent(country);
        // Use direct SQL queries for better performance
        const statsResult = await (0, database_1.query)(`
      SELECT
        COUNT(*) as total_scholarships,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_scholarships,
        COUNT(CASE WHEN is_open = false THEN 1 END) as closed_scholarships
      FROM scholarships
      WHERE country = $1
    `, [decodedCountry]);
        const levelStatsResult = await (0, database_1.query)(`
      SELECT
        level,
        COUNT(*) as count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count
      FROM scholarships
      WHERE country = $1 AND level IS NOT NULL AND level != ''
      GROUP BY level
      ORDER BY count DESC
    `, [decodedCountry]);
        const stats = statsResult.rows[0];
        const levelStats = levelStatsResult.rows.map(row => ({
            level: row.level,
            count: parseInt(row.count),
            openCount: parseInt(row.open_count)
        }));
        const statistics = {
            country: decodedCountry,
            totalScholarships: parseInt(stats.total_scholarships),
            openScholarships: parseInt(stats.open_scholarships),
            closedScholarships: parseInt(stats.closed_scholarships),
            scholarshipsByLevel: levelStats
        };
        return (0, apiResponse_1.sendSuccess)(res, statistics, 'Country statistics retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching country statistics:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch country statistics', error);
    }
};
exports.getCountryStatistics = getCountryStatistics;
/**
 * Search countries
 */
const searchCountries = async (req, res) => {
    try {
        const { q } = req.query;
        if (!q || typeof q !== 'string') {
            return (0, apiResponse_1.sendError)(res, 'Search query is required', null, 400);
        }
        const result = await (0, database_1.query)(`
      SELECT
        country,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count
      FROM scholarships
      WHERE country ILIKE $1 AND country IS NOT NULL AND country != ''
      GROUP BY country
      ORDER BY total_count DESC
      LIMIT 20
    `, [`%${q}%`]);
        const countries = result.rows.map(row => ({
            name: row.country,
            totalCount: parseInt(row.total_count),
            openCount: parseInt(row.open_count),
            slug: row.country.toLowerCase().replace(/\s+/g, '-')
        }));
        return (0, apiResponse_1.sendSuccess)(res, countries, 'Country search completed successfully');
    }
    catch (error) {
        console.error('Error searching countries:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to search countries', error);
    }
};
exports.searchCountries = searchCountries;
/**
 * Get all countries with enhanced data for sidebar - Industry Standard Implementation
 */
const getAllCountriesForSidebar = async (req, res) => {
    try {
        const { limit = '20', excludeCountry } = req.query;
        const limitNumber = Math.min(parseInt(limit, 10), 50);
        let whereClause = 'WHERE country IS NOT NULL AND country != \'\'';
        const params = [];
        if (excludeCountry) {
            whereClause += ' AND country != $1';
            params.push(decodeURIComponent(excludeCountry));
        }
        const result = await (0, database_1.query)(`
      SELECT
        country,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count,
        MAX(created_at) as latest_scholarship
      FROM scholarships
      ${whereClause}
      GROUP BY country
      ORDER BY total_count DESC, latest_scholarship DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);
        const countries = result.rows.map(row => ({
            name: row.country,
            totalCount: parseInt(row.total_count),
            openCount: parseInt(row.open_count),
            latestScholarship: row.latest_scholarship,
            slug: row.country.toLowerCase().replace(/\s+/g, '-')
        }));
        return (0, apiResponse_1.sendSuccess)(res, countries, 'Countries for sidebar retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching countries for sidebar:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch countries for sidebar', error);
    }
};
exports.getAllCountriesForSidebar = getAllCountriesForSidebar;
