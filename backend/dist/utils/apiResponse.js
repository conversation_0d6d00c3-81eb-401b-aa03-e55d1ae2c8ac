"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendValidationError = exports.sendForbidden = exports.sendUnauthorized = exports.sendNotFound = exports.sendError = exports.sendPaginatedSuccess = exports.sendSuccess = void 0;
/**
 * Send a success response
 * @param res Express response object
 * @param data Response data
 * @param message Success message
 * @param statusCode HTTP status code (default: 200)
 */
const sendSuccess = (res, data, message = 'Success', statusCode = 200) => {
    const response = {
        success: true,
        message,
        data,
        timestamp: new Date().toISOString(),
        statusCode
    };
    return res.status(statusCode).json(response);
};
exports.sendSuccess = sendSuccess;
/**
 * Send a paginated success response
 * @param res Express response object
 * @param data Array of items
 * @param pagination Pagination metadata
 * @param message Success message
 * @param statusCode HTTP status code (default: 200)
 */
const sendPaginatedSuccess = (res, data, pagination, message = 'Success', statusCode = 200) => {
    const response = {
        success: true,
        message,
        data,
        pagination,
        timestamp: new Date().toISOString(),
        statusCode
    };
    return res.status(statusCode).json(response);
};
exports.sendPaginatedSuccess = sendPaginatedSuccess;
/**
 * Send an error response
 * @param res Express response object
 * @param message Error message
 * @param error Error details (only included in development)
 * @param statusCode HTTP status code (default: 400)
 */
const sendError = (res, message = 'An error occurred', error = null, statusCode = 400) => {
    const response = {
        success: false,
        message,
        error: process.env.NODE_ENV === 'development' ? error : undefined,
        timestamp: new Date().toISOString(),
        statusCode
    };
    return res.status(statusCode).json(response);
};
exports.sendError = sendError;
/**
 * Send a not found response
 * @param res Express response object
 * @param message Not found message
 */
const sendNotFound = (res, message = 'Resource not found') => {
    return (0, exports.sendError)(res, message, null, 404);
};
exports.sendNotFound = sendNotFound;
/**
 * Send an unauthorized response
 * @param res Express response object
 * @param message Unauthorized message
 */
const sendUnauthorized = (res, message = 'Unauthorized') => {
    return (0, exports.sendError)(res, message, null, 401);
};
exports.sendUnauthorized = sendUnauthorized;
/**
 * Send a forbidden response
 * @param res Express response object
 * @param message Forbidden message
 */
const sendForbidden = (res, message = 'Forbidden') => {
    return (0, exports.sendError)(res, message, null, 403);
};
exports.sendForbidden = sendForbidden;
/**
 * Send a validation error response
 * @param res Express response object
 * @param errors Validation errors
 * @param message Validation error message
 */
const sendValidationError = (res, errors, message = 'Validation error') => {
    return (0, exports.sendError)(res, message, errors, 422);
};
exports.sendValidationError = sendValidationError;
exports.default = {
    sendSuccess: exports.sendSuccess,
    sendPaginatedSuccess: exports.sendPaginatedSuccess,
    sendError: exports.sendError,
    sendNotFound: exports.sendNotFound,
    sendUnauthorized: exports.sendUnauthorized,
    sendForbidden: exports.sendForbidden,
    sendValidationError: exports.sendValidationError
};
