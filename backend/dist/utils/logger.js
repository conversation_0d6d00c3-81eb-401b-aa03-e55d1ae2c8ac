"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailLogger = exports.cacheLogger = exports.authLogger = exports.apiLogger = exports.dbLogger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Ensure logs directory exists
const logDir = path_1.default.join(__dirname, '../../logs');
if (!fs_1.default.existsSync(logDir)) {
    fs_1.default.mkdirSync(logDir, { recursive: true });
}
// Define log formats
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}${info.data ? ' ' + JSON.stringify(info.data) : ''}`));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.json());
// Create the logger
const logger = winston_1.default.createLogger({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    defaultMeta: { service: 'mabourse-api' },
    transports: [
        // Console transport
        new winston_1.default.transports.Console({
            format: consoleFormat,
        }),
        // Error log file transport
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'error.log'),
            level: 'error',
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // Combined log file transport
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'combined.log'),
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // Database query log file transport (for debugging and performance monitoring)
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'database.log'),
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
    exceptionHandlers: [
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'exceptions.log'),
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
    rejectionHandlers: [
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'rejections.log'),
            format: fileFormat,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
});
// Create specialized loggers
exports.dbLogger = {
    query: (message, data) => {
        logger.info(`[DB:QUERY] ${message}`, { data, category: 'database' });
    },
    error: (message, data) => {
        logger.error(`[DB:ERROR] ${message}`, { data, category: 'database' });
    },
    warn: (message, data) => {
        logger.warn(`[DB:WARN] ${message}`, { data, category: 'database' });
    },
    info: (message, data) => {
        logger.info(`[DB:INFO] ${message}`, { data, category: 'database' });
    },
    debug: (message, data) => {
        logger.debug(`[DB:DEBUG] ${message}`, { data, category: 'database' });
    },
};
exports.apiLogger = {
    error: (message, data) => {
        logger.error(`[API:ERROR] ${message}`, { data, category: 'api' });
    },
    warn: (message, data) => {
        logger.warn(`[API:WARN] ${message}`, { data, category: 'api' });
    },
    info: (message, data) => {
        logger.info(`[API:INFO] ${message}`, { data, category: 'api' });
    },
    debug: (message, data) => {
        logger.debug(`[API:DEBUG] ${message}`, { data, category: 'api' });
    },
    request: (req, res) => {
        const { method, originalUrl, ip, body, query, params } = req;
        const statusCode = res.statusCode;
        // Don't log sensitive information
        const sanitizedBody = { ...body };
        if (sanitizedBody.password)
            sanitizedBody.password = '[REDACTED]';
        if (sanitizedBody.resetPasswordToken)
            sanitizedBody.resetPasswordToken = '[REDACTED]';
        if (sanitizedBody.twoFactorSecret)
            sanitizedBody.twoFactorSecret = '[REDACTED]';
        logger.info(`[API:REQUEST] ${method} ${originalUrl} ${statusCode}`, {
            data: {
                method,
                url: originalUrl,
                statusCode,
                ip,
                body: sanitizedBody,
                query,
                params,
            },
            category: 'api',
        });
    },
};
exports.authLogger = {
    error: (message, data) => {
        logger.error(`[AUTH:ERROR] ${message}`, { data, category: 'auth' });
    },
    warn: (message, data) => {
        logger.warn(`[AUTH:WARN] ${message}`, { data, category: 'auth' });
    },
    info: (message, data) => {
        logger.info(`[AUTH:INFO] ${message}`, { data, category: 'auth' });
    },
    debug: (message, data) => {
        logger.debug(`[AUTH:DEBUG] ${message}`, { data, category: 'auth' });
    },
    login: (user, success, reason) => {
        const message = success
            ? `User ${user.email} logged in successfully`
            : `Failed login attempt for ${user.email}: ${reason || 'Unknown reason'}`;
        if (success) {
            logger.info(`[AUTH:LOGIN] ${message}`, {
                data: { userId: user.id, email: user.email, success },
                category: 'auth',
            });
        }
        else {
            logger.warn(`[AUTH:LOGIN] ${message}`, {
                data: { email: user.email, success, reason },
                category: 'auth',
            });
        }
    },
};
exports.cacheLogger = {
    error: (message, data) => {
        logger.error(`[CACHE:ERROR] ${message}`, { data, category: 'cache' });
    },
    warn: (message, data) => {
        logger.warn(`[CACHE:WARN] ${message}`, { data, category: 'cache' });
    },
    info: (message, data) => {
        logger.info(`[CACHE:INFO] ${message}`, { data, category: 'cache' });
    },
    debug: (message, data) => {
        logger.debug(`[CACHE:DEBUG] ${message}`, { data, category: 'cache' });
    },
    hit: (key, data) => {
        logger.debug(`[CACHE:HIT] Cache hit for key: ${key}`, { data, category: 'cache' });
    },
    miss: (key, data) => {
        logger.debug(`[CACHE:MISS] Cache miss for key: ${key}`, { data, category: 'cache' });
    },
    set: (key, ttl, data) => {
        logger.debug(`[CACHE:SET] Cache set for key: ${key} with TTL: ${ttl}s`, { data, category: 'cache' });
    },
    invalidate: (key, data) => {
        logger.info(`[CACHE:INVALIDATE] Cache invalidated for key: ${key}`, { data, category: 'cache' });
    },
    clear: (data) => {
        logger.info(`[CACHE:CLEAR] Cache cleared`, { data, category: 'cache' });
    },
};
exports.emailLogger = {
    error: (message, data) => {
        logger.error(`[EMAIL:ERROR] ${message}`, { data, category: 'email' });
    },
    warn: (message, data) => {
        logger.warn(`[EMAIL:WARN] ${message}`, { data, category: 'email' });
    },
    info: (message, data) => {
        logger.info(`[EMAIL:INFO] ${message}`, { data, category: 'email' });
    },
    debug: (message, data) => {
        logger.debug(`[EMAIL:DEBUG] ${message}`, { data, category: 'email' });
    },
    send: (to, subject, data) => {
        logger.info(`[EMAIL:SEND] Email sent to ${to} with subject: ${subject}`, { data, category: 'email' });
    },
};
exports.default = logger;
