"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityMonitor = exports.DeviceType = exports.AlertType = exports.RiskLevel = void 0;
const database_1 = require("../config/database");
const authLogger_1 = require("./authLogger");
// Security risk levels
var RiskLevel;
(function (RiskLevel) {
    RiskLevel[RiskLevel["LOW"] = 0] = "LOW";
    RiskLevel[RiskLevel["MEDIUM"] = 25] = "MEDIUM";
    RiskLevel[RiskLevel["HIGH"] = 50] = "HIGH";
    RiskLevel[RiskLevel["CRITICAL"] = 75] = "CRITICAL";
    RiskLevel[RiskLevel["EXTREME"] = 100] = "EXTREME";
})(RiskLevel || (exports.RiskLevel = RiskLevel = {}));
// Alert types
var AlertType;
(function (AlertType) {
    AlertType["BRUTE_FORCE_ATTACK"] = "BRUTE_FORCE_ATTACK";
    AlertType["SUSPICIOUS_LOGIN_PATTERN"] = "SUSPICIOUS_LOGIN_PATTERN";
    AlertType["IMPOSSIBLE_TRAVEL"] = "IMPOSSIBLE_TRAVEL";
    AlertType["NEW_DEVICE_LOGIN"] = "NEW_DEVICE_LOGIN";
    AlertType["MULTIPLE_FAILED_LOGINS"] = "MULTIPLE_FAILED_LOGINS";
    AlertType["ACCOUNT_ENUMERATION"] = "ACCOUNT_ENUMERATION";
    AlertType["SUSPICIOUS_IP"] = "SUSPICIOUS_IP";
    AlertType["PASSWORD_SPRAY_ATTACK"] = "PASSWORD_SPRAY_ATTACK";
    AlertType["CREDENTIAL_STUFFING"] = "CREDENTIAL_STUFFING";
    AlertType["ANOMALOUS_BEHAVIOR"] = "ANOMALOUS_BEHAVIOR";
})(AlertType || (exports.AlertType = AlertType = {}));
// Device types
var DeviceType;
(function (DeviceType) {
    DeviceType["DESKTOP"] = "desktop";
    DeviceType["MOBILE"] = "mobile";
    DeviceType["TABLET"] = "tablet";
    DeviceType["UNKNOWN"] = "unknown";
})(DeviceType || (exports.DeviceType = DeviceType = {}));
/**
 * ENTERPRISE-GRADE SECURITY MONITORING SYSTEM
 */
class SecurityMonitor {
    /**
     * Log a comprehensive security event
     */
    static async logSecurityEvent(event) {
        try {
            const result = await (0, database_1.query)(`
        INSERT INTO security_events (
          event_type, message, user_id, admin_id, email, ip, user_agent,
          details, severity, session_id, request_id, geolocation,
          device_fingerprint, risk_score, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
                event.eventType,
                event.message,
                event.userId,
                event.adminId,
                event.email,
                event.ip,
                event.userAgent,
                JSON.stringify(event.details || {}),
                event.severity,
                event.sessionId,
                event.requestId,
                JSON.stringify(event.geolocation || {}),
                event.deviceFingerprint,
                event.riskScore || 0
            ]);
            const eventId = result.rows[0].id;
            // Check if this event should trigger an alert
            await this.checkForSecurityAlerts(eventId, event);
            return eventId;
        }
        catch (error) {
            console.error('Error logging security event:', error);
            throw error;
        }
    }
    /**
     * Log a login attempt
     */
    static async logLoginAttempt(attempt) {
        try {
            await (0, database_1.query)(`
        INSERT INTO login_attempts (
          email, ip, user_agent, success, failure_reason,
          geolocation, device_fingerprint, session_id, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
      `, [
                attempt.email,
                attempt.ip,
                attempt.userAgent,
                attempt.success,
                attempt.failureReason,
                JSON.stringify(attempt.geolocation || {}),
                attempt.deviceFingerprint,
                attempt.sessionId
            ]);
            // Analyze login patterns for suspicious activity
            if (!attempt.success) {
                await this.analyzeFailedLoginPatterns(attempt);
            }
        }
        catch (error) {
            console.error('Error logging login attempt:', error);
        }
    }
    /**
     * Generate device fingerprint from request
     */
    static generateDeviceFingerprint(req) {
        const userAgent = req.get('User-Agent') || '';
        const acceptLanguage = req.get('Accept-Language') || '';
        const acceptEncoding = req.get('Accept-Encoding') || '';
        const ip = req.ip || '';
        // Create a simple fingerprint (in production, use more sophisticated methods)
        const fingerprint = Buffer.from(`${userAgent}|${acceptLanguage}|${acceptEncoding}|${ip}`).toString('base64').substring(0, 32);
        return fingerprint;
    }
    /**
     * Parse device information from user agent
     */
    static parseDeviceInfo(userAgent) {
        const ua = userAgent.toLowerCase();
        let deviceType = DeviceType.UNKNOWN;
        let browser = 'Unknown';
        let os = 'Unknown';
        // Detect device type
        if (ua.includes('mobile') || ua.includes('android')) {
            deviceType = DeviceType.MOBILE;
        }
        else if (ua.includes('tablet') || ua.includes('ipad')) {
            deviceType = DeviceType.TABLET;
        }
        else {
            deviceType = DeviceType.DESKTOP;
        }
        // Detect browser
        if (ua.includes('chrome'))
            browser = 'Chrome';
        else if (ua.includes('firefox'))
            browser = 'Firefox';
        else if (ua.includes('safari'))
            browser = 'Safari';
        else if (ua.includes('edge'))
            browser = 'Edge';
        // Detect OS
        if (ua.includes('windows'))
            os = 'Windows';
        else if (ua.includes('mac'))
            os = 'macOS';
        else if (ua.includes('linux'))
            os = 'Linux';
        else if (ua.includes('android'))
            os = 'Android';
        else if (ua.includes('ios'))
            os = 'iOS';
        return {
            fingerprint: this.generateDeviceFingerprint({ get: () => userAgent }),
            type: deviceType,
            browser,
            os,
            userAgent
        };
    }
    /**
     * Track device for user/admin
     */
    static async trackDevice(userId, adminId, deviceInfo, ip, trusted = false) {
        try {
            // Check if device already exists
            const existingDevice = await (0, database_1.query)(`
        SELECT id, ip_addresses FROM trusted_devices 
        WHERE device_fingerprint = $1 AND (
          ($2::integer IS NOT NULL AND user_id = $2) OR 
          ($3::integer IS NOT NULL AND admin_id = $3)
        )
      `, [deviceInfo.fingerprint, userId, adminId]);
            if (existingDevice.rows.length > 0) {
                // Update existing device
                const device = existingDevice.rows[0];
                const ipAddresses = device.ip_addresses || [];
                if (!ipAddresses.includes(ip)) {
                    ipAddresses.push(ip);
                }
                await (0, database_1.query)(`
          UPDATE trusted_devices 
          SET last_seen = CURRENT_TIMESTAMP,
              ip_addresses = $1,
              device_name = COALESCE(device_name, $2),
              browser = COALESCE(browser, $3),
              os = COALESCE(os, $4)
          WHERE id = $5
        `, [
                    JSON.stringify(ipAddresses),
                    `${deviceInfo.browser} on ${deviceInfo.os}`,
                    deviceInfo.browser,
                    deviceInfo.os,
                    device.id
                ]);
            }
            else {
                // Create new device record
                await (0, database_1.query)(`
          INSERT INTO trusted_devices (
            user_id, admin_id, device_fingerprint, device_name,
            device_type, browser, os, trusted, ip_addresses
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `, [
                    userId,
                    adminId,
                    deviceInfo.fingerprint,
                    `${deviceInfo.browser} on ${deviceInfo.os}`,
                    deviceInfo.type,
                    deviceInfo.browser,
                    deviceInfo.os,
                    trusted,
                    JSON.stringify([ip])
                ]);
                // If this is a new device, create an alert
                if (!trusted) {
                    await this.createSecurityAlert({
                        alertType: AlertType.NEW_DEVICE_LOGIN,
                        title: 'New Device Login Detected',
                        description: `Login from new ${deviceInfo.type} device: ${deviceInfo.browser} on ${deviceInfo.os}`,
                        severity: 'medium',
                        userId,
                        adminId,
                        ip,
                        metadata: { deviceInfo }
                    });
                }
            }
        }
        catch (error) {
            console.error('Error tracking device:', error);
        }
    }
    /**
     * Create security alert
     */
    static async createSecurityAlert(alert) {
        try {
            const result = await (0, database_1.query)(`
        INSERT INTO security_alerts (
          alert_type, title, description, severity, user_id, admin_id,
          ip, triggered_by_event_id, metadata, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
                alert.alertType,
                alert.title,
                alert.description,
                alert.severity,
                alert.userId,
                alert.adminId,
                alert.ip,
                alert.triggeredByEventId,
                JSON.stringify(alert.metadata || {})
            ]);
            const alertId = result.rows[0].id;
            // Log the alert creation
            await (0, authLogger_1.logAuthEvent)(authLogger_1.AuthEventType.SUSPICIOUS_ACTIVITY, `Security alert created: ${alert.title}`, {
                alertId,
                alertType: alert.alertType,
                severity: alert.severity,
                userId: alert.userId,
                adminId: alert.adminId,
                ip: alert.ip
            }, alert.severity === 'critical' ? authLogger_1.SecurityLevel.ALERT : authLogger_1.SecurityLevel.WARN);
            return alertId;
        }
        catch (error) {
            console.error('Error creating security alert:', error);
            throw error;
        }
    }
    /**
     * Analyze failed login patterns for suspicious activity
     */
    static async analyzeFailedLoginPatterns(attempt) {
        try {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            // Check for brute force attacks (same email, multiple IPs)
            const recentFailures = await (0, database_1.query)(`
        SELECT COUNT(*) as count, COUNT(DISTINCT ip) as unique_ips
        FROM login_attempts 
        WHERE email = $1 AND success = false AND timestamp > $2
      `, [attempt.email, oneHourAgo]);
            const failures = recentFailures.rows[0];
            if (failures.count >= 10) {
                await this.createSecurityAlert({
                    alertType: AlertType.BRUTE_FORCE_ATTACK,
                    title: 'Brute Force Attack Detected',
                    description: `${failures.count} failed login attempts for ${attempt.email} in the last hour`,
                    severity: 'high',
                    ip: attempt.ip,
                    metadata: { email: attempt.email, attempts: failures.count, uniqueIps: failures.unique_ips }
                });
            }
            // Check for password spray attacks (same IP, multiple emails)
            const ipFailures = await (0, database_1.query)(`
        SELECT COUNT(*) as count, COUNT(DISTINCT email) as unique_emails
        FROM login_attempts 
        WHERE ip = $1 AND success = false AND timestamp > $2
      `, [attempt.ip, oneHourAgo]);
            const ipAttempts = ipFailures.rows[0];
            if (ipAttempts.unique_emails >= 5) {
                await this.createSecurityAlert({
                    alertType: AlertType.PASSWORD_SPRAY_ATTACK,
                    title: 'Password Spray Attack Detected',
                    description: `${ipAttempts.count} failed login attempts from IP ${attempt.ip} targeting ${ipAttempts.unique_emails} different accounts`,
                    severity: 'high',
                    ip: attempt.ip,
                    metadata: { attempts: ipAttempts.count, uniqueEmails: ipAttempts.unique_emails }
                });
            }
        }
        catch (error) {
            console.error('Error analyzing failed login patterns:', error);
        }
    }
    /**
     * Check if security event should trigger alerts
     */
    static async checkForSecurityAlerts(eventId, event) {
        try {
            // High risk score events
            if (event.riskScore && event.riskScore >= RiskLevel.HIGH) {
                await this.createSecurityAlert({
                    alertType: AlertType.ANOMALOUS_BEHAVIOR,
                    title: 'High Risk Security Event',
                    description: `Security event with risk score ${event.riskScore}: ${event.message}`,
                    severity: event.riskScore >= RiskLevel.CRITICAL ? 'critical' : 'high',
                    userId: event.userId,
                    adminId: event.adminId,
                    ip: event.ip,
                    triggeredByEventId: eventId,
                    metadata: event.details
                });
            }
        }
        catch (error) {
            console.error('Error checking for security alerts:', error);
        }
    }
    /**
     * Get security metrics for dashboard
     */
    static async getSecurityMetrics(timeframe = 'day') {
        try {
            let timeCondition = '';
            const now = new Date();
            switch (timeframe) {
                case 'hour':
                    timeCondition = `timestamp > '${new Date(now.getTime() - 60 * 60 * 1000).toISOString()}'`;
                    break;
                case 'day':
                    timeCondition = `timestamp > '${new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()}'`;
                    break;
                case 'week':
                    timeCondition = `timestamp > '${new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()}'`;
                    break;
            }
            const [events, attempts, alerts] = await Promise.all([
                (0, database_1.query)(`SELECT COUNT(*) as total, severity FROM security_events WHERE ${timeCondition} GROUP BY severity`),
                (0, database_1.query)(`SELECT COUNT(*) as total, success FROM login_attempts WHERE ${timeCondition} GROUP BY success`),
                (0, database_1.query)(`SELECT COUNT(*) as total, severity FROM security_alerts WHERE ${timeCondition} GROUP BY severity`)
            ]);
            return {
                timeframe,
                securityEvents: events.rows,
                loginAttempts: attempts.rows,
                securityAlerts: alerts.rows,
                timestamp: now.toISOString()
            };
        }
        catch (error) {
            console.error('Error getting security metrics:', error);
            throw error;
        }
    }
}
exports.SecurityMonitor = SecurityMonitor;
