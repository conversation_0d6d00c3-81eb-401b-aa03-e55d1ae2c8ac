"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNumEnv = exports.getBoolEnv = exports.getEnv = exports.validateEnv = void 0;
/**
 * Environment variable validation utility
 *
 * This utility validates that all required environment variables are set
 * and logs warnings for missing optional variables.
 */
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Load environment variables based on NODE_ENV
const loadEnv = () => {
    const NODE_ENV = process.env.NODE_ENV || 'development';
    const envPath = path_1.default.resolve(process.cwd(), `.env.${NODE_ENV}`);
    // Check if environment-specific file exists
    if (fs_1.default.existsSync(envPath)) {
        console.log(`Loading environment variables from ${envPath}`);
        dotenv_1.default.config({ path: envPath });
    }
    else {
        // Fall back to default .env file
        console.log('Loading environment variables from .env');
        dotenv_1.default.config();
    }
};
// Required environment variables
const requiredVariables = [
    'PORT',
    'JWT_SECRET',
    'DATABASE_URL',
];
// Optional environment variables with default values
const optionalVariables = {
    'NODE_ENV': 'development',
    'JWT_EXPIRATION': '1d',
    'REFRESH_TOKEN_SECRET': 'mabourse-refresh-token-secret',
    'REFRESH_TOKEN_EXPIRATION': '7d',
    'CORS_ORIGIN': 'http://localhost:3000',
    'FRONTEND_URL': 'http://localhost:3000',
    'LOG_LEVEL': 'info',
    'RATE_LIMIT_WINDOW_MS': '60000',
    'RATE_LIMIT_MAX_REQUESTS': '100',
};
/**
 * Validates environment variables and logs warnings/errors
 * @returns {boolean} True if all required variables are set, false otherwise
 */
const validateEnv = () => {
    // Load environment variables first
    loadEnv();
    let isValid = true;
    const missingVars = [];
    // Check required variables
    requiredVariables.forEach(varName => {
        if (!process.env[varName]) {
            console.error(`❌ Required environment variable ${varName} is not set`);
            missingVars.push(varName);
            isValid = false;
        }
    });
    // Check optional variables and set defaults if needed
    Object.entries(optionalVariables).forEach(([varName, defaultValue]) => {
        if (!process.env[varName]) {
            console.warn(`⚠️ Optional environment variable ${varName} is not set, using default: ${defaultValue}`);
            process.env[varName] = defaultValue;
        }
    });
    // Log environment mode
    console.info(`🌍 Environment: ${process.env.NODE_ENV}`);
    if (!isValid) {
        console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
        console.error('Please check your .env file or environment configuration');
    }
    else {
        console.info('✅ Environment validation passed');
    }
    return isValid;
};
exports.validateEnv = validateEnv;
/**
 * Gets an environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {string} fallback - The fallback value if the variable is not set
 * @returns {string} The environment variable value or fallback
 */
const getEnv = (name, fallback) => {
    return process.env[name] || fallback;
};
exports.getEnv = getEnv;
/**
 * Gets a boolean environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {boolean} fallback - The fallback value if the variable is not set
 * @returns {boolean} The environment variable value as boolean or fallback
 */
const getBoolEnv = (name, fallback) => {
    const value = process.env[name];
    if (value === undefined)
        return fallback;
    return value.toLowerCase() === 'true';
};
exports.getBoolEnv = getBoolEnv;
/**
 * Gets a number environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {number} fallback - The fallback value if the variable is not set
 * @returns {number} The environment variable value as number or fallback
 */
const getNumEnv = (name, fallback) => {
    const value = process.env[name];
    if (value === undefined)
        return fallback;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? fallback : parsed;
};
exports.getNumEnv = getNumEnv;
exports.default = exports.validateEnv;
