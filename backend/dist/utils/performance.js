"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceTracker = void 0;
exports.trackPerformance = trackPerformance;
const logger_1 = __importDefault(require("./logger"));
/**
 * Utility class for measuring and tracking performance of operations
 */
class PerformanceTracker {
    /**
     * Create a new performance tracker
     * @param name Name of the operation being tracked
     */
    constructor(name) {
        this.checkpoints = [];
        this.name = name;
        this.startTime = process.hrtime();
    }
    /**
     * Record a checkpoint in the operation
     * @param name Name of the checkpoint
     * @returns Time in milliseconds since the start
     */
    checkpoint(name) {
        const hrtime = process.hrtime(this.startTime);
        const milliseconds = hrtime[0] * 1000 + hrtime[1] / 1000000;
        this.checkpoints.push({
            name,
            time: milliseconds,
        });
        return milliseconds;
    }
    /**
     * End the performance tracking and log the results
     * @param logLevel Log level to use (default: 'debug')
     * @returns Total time in milliseconds
     */
    end(logLevel = 'debug') {
        const hrtime = process.hrtime(this.startTime);
        const totalTime = hrtime[0] * 1000 + hrtime[1] / 1000000;
        // Add end checkpoint
        this.checkpoints.push({
            name: 'end',
            time: totalTime,
        });
        // Calculate time between checkpoints
        const checkpointDurations = [];
        for (let i = 1; i < this.checkpoints.length; i++) {
            const current = this.checkpoints[i];
            const previous = this.checkpoints[i - 1];
            checkpointDurations.push({
                from: previous.name,
                to: current.name,
                duration: current.time - previous.time,
            });
        }
        // Log performance data
        const message = `Performance: ${this.name} completed in ${totalTime.toFixed(2)}ms`;
        const data = {
            operation: this.name,
            totalTime: parseFloat(totalTime.toFixed(2)),
            checkpoints: this.checkpoints.map(cp => ({
                name: cp.name,
                time: parseFloat(cp.time.toFixed(2)),
            })),
            checkpointDurations: checkpointDurations.map(cpd => ({
                from: cpd.from,
                to: cpd.to,
                duration: parseFloat(cpd.duration.toFixed(2)),
            })),
        };
        // Log at appropriate level
        switch (logLevel) {
            case 'info':
                logger_1.default.info(message, { data, category: 'performance' });
                break;
            case 'warn':
                logger_1.default.warn(message, { data, category: 'performance' });
                break;
            default:
                logger_1.default.debug(message, { data, category: 'performance' });
        }
        return totalTime;
    }
    /**
     * Create a performance tracker and track a function execution
     * @param name Name of the operation
     * @param fn Function to track
     * @param logLevel Log level to use
     * @returns Result of the function
     */
    static track(name, fn, logLevel = 'debug') {
        const tracker = new PerformanceTracker(name);
        try {
            const result = fn();
            tracker.end(logLevel);
            return result;
        }
        catch (error) {
            tracker.checkpoint('error');
            tracker.end('warn');
            throw error;
        }
    }
    /**
     * Create a performance tracker and track an async function execution
     * @param name Name of the operation
     * @param fn Async function to track
     * @param logLevel Log level to use
     * @returns Promise resolving to the function result
     */
    static async trackAsync(name, fn, logLevel = 'debug') {
        const tracker = new PerformanceTracker(name);
        try {
            const result = await fn();
            tracker.end(logLevel);
            return result;
        }
        catch (error) {
            tracker.checkpoint('error');
            tracker.end('warn');
            throw error;
        }
    }
}
exports.PerformanceTracker = PerformanceTracker;
/**
 * Decorator for tracking method performance (for class methods)
 * @param name Optional name for the operation (defaults to method name)
 * @param logLevel Log level to use
 */
function trackPerformance(name, logLevel = 'debug') {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        const operationName = name || `${target.constructor.name}.${propertyKey}`;
        descriptor.value = function (...args) {
            if (originalMethod.constructor.name === 'AsyncFunction') {
                return PerformanceTracker.trackAsync(operationName, () => originalMethod.apply(this, args), logLevel);
            }
            else {
                return PerformanceTracker.track(operationName, () => originalMethod.apply(this, args), logLevel);
            }
        };
        return descriptor;
    };
}
exports.default = PerformanceTracker;
