"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendNewScholarshipNotification = exports.generateNewScholarshipEmail = exports.generatePasswordResetEmail = exports.sendEmail = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Create a transporter object
const createTransporter = () => {
    // Use configured SMTP settings if available
    if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
        return nodemailer_1.default.createTransport({
            host: process.env.SMTP_HOST,
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
            },
        });
    }
    // Fallback to ethereal.email for development
    console.log('SMTP settings not found, using ethereal.email for development');
    return nodemailer_1.default.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
            user: process.env.ETHEREAL_EMAIL || 'your-ethereal-email',
            pass: process.env.ETHEREAL_PASSWORD || 'your-ethereal-password',
        },
    });
};
const sendEmail = async (options) => {
    const transporter = createTransporter();
    const mailOptions = {
        from: process.env.SMTP_FROM || 'MaBourse Admin <<EMAIL>>',
        ...options,
    };
    try {
        const info = await transporter.sendMail(mailOptions);
        // Log email URL for development (ethereal.email)
        if (!process.env.SMTP_HOST && info.messageId) {
            console.log('Preview URL: %s', nodemailer_1.default.getTestMessageUrl(info));
        }
        console.log('Email sent successfully');
    }
    catch (error) {
        console.error('Error sending email:', error);
        throw new Error('Failed to send email');
    }
};
exports.sendEmail = sendEmail;
// Generate a password reset email
const generatePasswordResetEmail = (name, resetUrl) => {
    const text = `
    Hello ${name},

    You are receiving this email because you (or someone else) has requested to reset your password.

    Please click on the following link to reset your password:
    ${resetUrl}

    This link will expire in 1 hour.

    If you did not request this, please ignore this email and your password will remain unchanged.

    Regards,
    MaBourse Team
  `;
    const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4a6ee0;">Password Reset Request</h2>
      <p>Hello ${name},</p>
      <p>You are receiving this email because you (or someone else) has requested to reset your password.</p>
      <p>Please click on the following button to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" style="background-color: #4a6ee0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
      </div>
      <p>This link will expire in 1 hour.</p>
      <p>If you did not request this, please ignore this email and your password will remain unchanged.</p>
      <p>Regards,<br>MaBourse Team</p>
    </div>
  `;
    return { text, html };
};
exports.generatePasswordResetEmail = generatePasswordResetEmail;
// Generate a new scholarship notification email
const generateNewScholarshipEmail = (scholarshipTitle, scholarshipDescription, scholarshipLink, applicationLink) => {
    const text = `
    New Scholarship Alert: ${scholarshipTitle}

    We're excited to inform you about a new scholarship opportunity that has been added to MaBourse:

    ${scholarshipTitle}

    ${scholarshipDescription}

    View the full scholarship details here:
    ${scholarshipLink}

    Apply now:
    ${applicationLink}

    If you wish to unsubscribe from these notifications, please visit your profile settings.

    Regards,
    MaBourse Team
  `;
    const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4a6ee0;">New Scholarship Alert</h2>
      <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #333; margin-top: 0;">${scholarshipTitle}</h3>
        <p style="color: #555;">${scholarshipDescription}</p>
      </div>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${scholarshipLink}" style="background-color: #4a6ee0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 10px;">View Details</a>
        <a href="${applicationLink}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Apply Now</a>
      </div>
      <p style="font-size: 12px; color: #777; text-align: center; margin-top: 40px;">
        If you wish to unsubscribe from these notifications, please visit your profile settings.
      </p>
      <p>Regards,<br>MaBourse Team</p>
    </div>
  `;
    return { text, html };
};
exports.generateNewScholarshipEmail = generateNewScholarshipEmail;
// Send notification to all subscribers about a new scholarship
const sendNewScholarshipNotification = async (subscribers, scholarshipTitle, scholarshipDescription, scholarshipId) => {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const scholarshipLink = `${baseUrl}/scholarships/${scholarshipId}`;
    const applicationLink = `${baseUrl}/scholarships/${scholarshipId}/apply`;
    // Truncate description if it's too long
    const truncatedDescription = scholarshipDescription.length > 200
        ? `${scholarshipDescription.substring(0, 200)}...`
        : scholarshipDescription;
    const { text, html } = (0, exports.generateNewScholarshipEmail)(scholarshipTitle, truncatedDescription, scholarshipLink, applicationLink);
    console.log(`Sending notification about new scholarship "${scholarshipTitle}" to ${subscribers.length} subscribers`);
    // Send emails in batches to avoid overwhelming the email server
    const batchSize = 10;
    const batches = Math.ceil(subscribers.length / batchSize);
    for (let i = 0; i < batches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, subscribers.length);
        const batch = subscribers.slice(start, end);
        console.log(`Processing batch ${i + 1}/${batches} (${batch.length} subscribers)`);
        // Process each subscriber in the batch
        for (const subscriber of batch) {
            try {
                await (0, exports.sendEmail)({
                    to: subscriber.email,
                    subject: `New Scholarship Alert: ${scholarshipTitle}`,
                    text,
                    html
                });
            }
            catch (error) {
                console.error(`Failed to send notification to ${subscriber.email}:`, error);
                // Continue with next subscriber even if one fails
            }
        }
    }
    console.log('Finished sending scholarship notifications');
};
exports.sendNewScholarshipNotification = sendNewScholarshipNotification;
