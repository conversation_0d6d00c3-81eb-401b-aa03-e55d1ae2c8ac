"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyBackupCode = exports.generateBackupCodes = exports.generateQRCode = exports.verifyToken = exports.generateToken = exports.generateSecret = void 0;
const otplib_1 = require("otplib");
const crypto_1 = __importDefault(require("crypto"));
const qrcode_1 = __importDefault(require("qrcode"));
// Configure authenticator
otplib_1.authenticator.options = {
    window: 1, // Allow 1 step before and after current step (30 seconds window)
};
// Generate a new secret key
const generateSecret = (email) => {
    return otplib_1.authenticator.generateSecret();
};
exports.generateSecret = generateSecret;
// Generate a TOTP token
const generateToken = (secret) => {
    return otplib_1.authenticator.generate(secret);
};
exports.generateToken = generateToken;
// Verify a TOTP token
const verifyToken = (token, secret) => {
    try {
        return otplib_1.authenticator.verify({ token, secret });
    }
    catch (error) {
        console.error('Error verifying token:', error);
        return false;
    }
};
exports.verifyToken = verifyToken;
// Generate a QR code for the secret
const generateQRCode = async (secret, email, issuer = 'MaBourse Admin') => {
    try {
        const otpauth = otplib_1.authenticator.keyuri(email, issuer, secret);
        return await qrcode_1.default.toDataURL(otpauth);
    }
    catch (error) {
        console.error('Error generating QR code:', error);
        throw new Error('Failed to generate QR code');
    }
};
exports.generateQRCode = generateQRCode;
// Generate backup codes
const generateBackupCodes = (count = 10) => {
    const codes = [];
    for (let i = 0; i < count; i++) {
        // Generate a random 8-character code
        const code = crypto_1.default.randomBytes(4).toString('hex').toUpperCase();
        codes.push(code);
    }
    return codes;
};
exports.generateBackupCodes = generateBackupCodes;
// Verify a backup code
const verifyBackupCode = (code, backupCodes) => {
    const normalizedCode = code.trim().toUpperCase();
    const codeIndex = backupCodes.indexOf(normalizedCode);
    if (codeIndex === -1) {
        return { valid: false, remainingCodes: backupCodes };
    }
    // Remove the used code
    const remainingCodes = [...backupCodes];
    remainingCodes.splice(codeIndex, 1);
    return { valid: true, remainingCodes };
};
exports.verifyBackupCode = verifyBackupCode;
