"use strict";
/**
 * Date utility functions for consistent date handling across the application
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentDateISO = exports.getDaysRemaining = exports.isDateToday = exports.isDatePast = exports.isValidDate = exports.parseDate = exports.formatDate = exports.DEFAULT_LOCALE = exports.DateFormat = void 0;
/**
 * Format options for different date display formats
 */
var DateFormat;
(function (DateFormat) {
    DateFormat["SHORT"] = "short";
    DateFormat["MEDIUM"] = "medium";
    DateFormat["LONG"] = "long";
    DateFormat["FULL"] = "full";
    DateFormat["ISO"] = "iso";
})(DateFormat || (exports.DateFormat = DateFormat = {}));
/**
 * Default locale for date formatting
 */
exports.DEFAULT_LOCALE = 'fr-FR';
/**
 * Format a date string or Date object to a localized string
 * @param date Date string or Date object
 * @param format Format type
 * @param locale Locale for formatting (default: fr-FR)
 * @returns Formatted date string
 */
const formatDate = (date, format = DateFormat.MEDIUM, locale = exports.DEFAULT_LOCALE) => {
    if (!date)
        return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
        console.error(`Invalid date: ${date}`);
        return '';
    }
    switch (format) {
        case DateFormat.SHORT:
            return dateObj.toLocaleDateString(locale, {
                day: 'numeric',
                month: 'numeric',
                year: 'numeric'
            });
        case DateFormat.MEDIUM:
            return dateObj.toLocaleDateString(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
        case DateFormat.LONG:
            return dateObj.toLocaleDateString(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
                weekday: 'long'
            });
        case DateFormat.FULL:
            return dateObj.toLocaleString(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
                weekday: 'long',
                hour: '2-digit',
                minute: '2-digit'
            });
        case DateFormat.ISO:
            return dateObj.toISOString();
        default:
            return dateObj.toLocaleDateString(locale);
    }
};
exports.formatDate = formatDate;
/**
 * Parse a date string to a Date object
 * @param dateString Date string to parse
 * @returns Date object or null if invalid
 */
const parseDate = (dateString) => {
    if (!dateString)
        return null;
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
};
exports.parseDate = parseDate;
/**
 * Check if a date is valid
 * @param date Date to check
 * @returns True if date is valid
 */
const isValidDate = (date) => {
    if (!date)
        return false;
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return !isNaN(dateObj.getTime());
};
exports.isValidDate = isValidDate;
/**
 * Check if a date is in the past
 * @param date Date to check
 * @returns True if date is in the past
 */
const isDatePast = (date) => {
    if (!date)
        return false;
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    return dateObj < now;
};
exports.isDatePast = isDatePast;
/**
 * Check if a date is today
 * @param date Date to check
 * @returns True if date is today
 */
const isDateToday = (date) => {
    if (!date)
        return false;
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    return (dateObj.getDate() === now.getDate() &&
        dateObj.getMonth() === now.getMonth() &&
        dateObj.getFullYear() === now.getFullYear());
};
exports.isDateToday = isDateToday;
/**
 * Get the number of days remaining until a date
 * @param date Target date
 * @returns Number of days remaining (negative if date is in the past)
 */
const getDaysRemaining = (date) => {
    if (!date)
        return 0;
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    // Reset time to midnight for accurate day calculation
    const targetDate = new Date(dateObj);
    targetDate.setHours(0, 0, 0, 0);
    const nowDate = new Date(now);
    nowDate.setHours(0, 0, 0, 0);
    const diffInMs = targetDate.getTime() - nowDate.getTime();
    return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
};
exports.getDaysRemaining = getDaysRemaining;
/**
 * Get the current date in ISO format
 * @returns Current date in ISO format
 */
const getCurrentDateISO = () => {
    return new Date().toISOString();
};
exports.getCurrentDateISO = getCurrentDateISO;
exports.default = {
    formatDate: exports.formatDate,
    parseDate: exports.parseDate,
    isValidDate: exports.isValidDate,
    isDatePast: exports.isDatePast,
    isDateToday: exports.isDateToday,
    getDaysRemaining: exports.getDaysRemaining,
    getCurrentDateISO: exports.getCurrentDateISO,
    DateFormat
};
