"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeolocationService = void 0;
const axios_1 = __importDefault(require("axios"));
/**
 * ENTERPRISE-GRADE GEOLOCATION & TRAVEL ANALYSIS SERVICE
 */
class GeolocationService {
    /**
     * Get geolocation data for an IP address
     */
    static async getGeolocation(ip) {
        var _a, _b, _c, _d;
        try {
            // Skip localhost and private IPs
            if (this.isPrivateIP(ip)) {
                return {
                    ip,
                    country: 'Local',
                    countryCode: 'LOCAL',
                    city: 'Localhost',
                    lat: 0,
                    lon: 0
                };
            }
            // Check cache first
            const cached = this.cache.get(ip);
            if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
                return cached.data;
            }
            // Get basic geolocation data
            const geoResponse = await axios_1.default.get(`${this.IP_API_URL}/${ip}`, {
                timeout: 5000,
                params: {
                    fields: 'status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query'
                }
            });
            if (geoResponse.data.status === 'fail') {
                console.warn(`Geolocation failed for IP ${ip}:`, geoResponse.data.message);
                return null;
            }
            const geoData = {
                ip: geoResponse.data.query || ip,
                country: geoResponse.data.country,
                countryCode: geoResponse.data.countryCode,
                region: geoResponse.data.region,
                regionName: geoResponse.data.regionName,
                city: geoResponse.data.city,
                zip: geoResponse.data.zip,
                lat: geoResponse.data.lat,
                lon: geoResponse.data.lon,
                timezone: geoResponse.data.timezone,
                isp: geoResponse.data.isp,
                org: geoResponse.data.org,
                as: geoResponse.data.as,
                status: geoResponse.data.status
            };
            // Try to get VPN/Proxy detection (optional, may fail)
            try {
                const vpnResponse = await axios_1.default.get(`${this.VPNAPI_URL}/${ip}`, {
                    timeout: 3000
                });
                if (vpnResponse.data) {
                    geoData.isVpn = ((_a = vpnResponse.data.security) === null || _a === void 0 ? void 0 : _a.vpn) || false;
                    geoData.isTor = ((_b = vpnResponse.data.security) === null || _b === void 0 ? void 0 : _b.tor) || false;
                    geoData.isProxy = ((_c = vpnResponse.data.security) === null || _c === void 0 ? void 0 : _c.proxy) || false;
                    geoData.threat = ((_d = vpnResponse.data.security) === null || _d === void 0 ? void 0 : _d.threat) || 'none';
                }
            }
            catch (vpnError) {
                // VPN detection is optional, continue without it
                console.debug('VPN detection failed:', vpnError.message);
            }
            // Cache the result
            this.cache.set(ip, { data: geoData, timestamp: Date.now() });
            return geoData;
        }
        catch (error) {
            console.error(`Error getting geolocation for IP ${ip}:`, error.message);
            return null;
        }
    }
    /**
     * Analyze travel between two locations
     */
    static analyzeTravelPattern(previousLocation, currentLocation, timeDifferenceMinutes) {
        const analysis = {
            previousLocation: previousLocation || undefined,
            currentLocation,
            timeDifference: timeDifferenceMinutes,
            isImpossibleTravel: false,
            riskScore: 0
        };
        // If no previous location, no travel analysis possible
        if (!previousLocation || !previousLocation.lat || !previousLocation.lon ||
            !currentLocation.lat || !currentLocation.lon) {
            return analysis;
        }
        // Calculate distance between locations
        const distance = this.calculateDistance(previousLocation.lat, previousLocation.lon, currentLocation.lat, currentLocation.lon);
        analysis.distance = distance;
        // Calculate required speed
        const timeHours = timeDifferenceMinutes / 60;
        const requiredSpeed = timeHours > 0 ? distance / timeHours : Infinity;
        analysis.maxPossibleSpeed = requiredSpeed;
        // Determine if travel is impossible
        if (requiredSpeed > this.MAX_HUMAN_SPEED && distance > 100) { // Allow some tolerance for nearby locations
            analysis.isImpossibleTravel = true;
            analysis.riskScore = Math.min(100, Math.floor(requiredSpeed / 10)); // Higher speed = higher risk
        }
        // Additional risk factors
        let riskScore = analysis.riskScore;
        // VPN/Proxy usage increases risk
        if (currentLocation.isVpn || currentLocation.isProxy || currentLocation.isTor) {
            riskScore += 25;
        }
        // Different countries increase risk
        if (previousLocation.countryCode !== currentLocation.countryCode) {
            riskScore += 15;
        }
        // Very fast travel (but not impossible) is suspicious
        if (requiredSpeed > 500 && requiredSpeed <= this.MAX_HUMAN_SPEED) {
            riskScore += 20;
        }
        // Threat indicators
        if (currentLocation.threat && currentLocation.threat !== 'none') {
            riskScore += 30;
        }
        analysis.riskScore = Math.min(100, riskScore);
        return analysis;
    }
    /**
     * Calculate distance between two coordinates using Haversine formula
     */
    static calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Earth's radius in kilometers
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    /**
     * Convert degrees to radians
     */
    static toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    /**
     * Check if IP is private/local
     */
    static isPrivateIP(ip) {
        if (!ip || ip === '::1' || ip === '127.0.0.1' || ip.startsWith('::ffff:127.')) {
            return true;
        }
        // IPv4 private ranges
        const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
        const match = ip.match(ipv4Regex);
        if (match) {
            const [, a, b, c, d] = match.map(Number);
            // 10.0.0.0/8
            if (a === 10)
                return true;
            // **********/12
            if (a === 172 && b >= 16 && b <= 31)
                return true;
            // ***********/16
            if (a === 192 && b === 168)
                return true;
            // ***********/16 (link-local)
            if (a === 169 && b === 254)
                return true;
        }
        return false;
    }
    /**
     * Get risk assessment for an IP address
     */
    static async assessIPRisk(ip) {
        try {
            const geoData = await this.getGeolocation(ip);
            if (!geoData)
                return 0;
            let riskScore = 0;
            // VPN/Proxy/Tor usage
            if (geoData.isVpn)
                riskScore += 30;
            if (geoData.isProxy)
                riskScore += 25;
            if (geoData.isTor)
                riskScore += 50;
            // Threat indicators
            if (geoData.threat && geoData.threat !== 'none') {
                riskScore += 40;
            }
            // High-risk countries (this would be configurable in production)
            const highRiskCountries = ['CN', 'RU', 'KP', 'IR']; // Example list
            if (geoData.countryCode && highRiskCountries.includes(geoData.countryCode)) {
                riskScore += 20;
            }
            return Math.min(100, riskScore);
        }
        catch (error) {
            console.error('Error assessing IP risk:', error);
            return 0;
        }
    }
    /**
     * Clear geolocation cache
     */
    static clearCache() {
        this.cache.clear();
    }
    /**
     * Get cache statistics
     */
    static getCacheStats() {
        return {
            size: this.cache.size,
            entries: Array.from(this.cache.keys())
        };
    }
}
exports.GeolocationService = GeolocationService;
GeolocationService.IP_API_URL = 'http://ip-api.com/json';
GeolocationService.VPNAPI_URL = 'https://vpnapi.io/api';
GeolocationService.MAX_HUMAN_SPEED = 1000; // km/h (commercial flight speed)
GeolocationService.CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
GeolocationService.cache = new Map();
// GeolocationService is already exported above
