"use strict";
/**
 * Database Migration Utility - Convert Base64 Thumbnails to Files
 *
 * This utility ensures compliance with industry production standards by:
 * 1. Converting base64 images stored in database to proper file storage
 * 2. Updating database references to point to file URLs
 * 3. Maintaining data integrity during migration
 *
 * Industry Standards Compliance:
 * - Separates binary data from relational data
 * - Reduces database size and improves performance
 * - Enables proper CDN integration and caching
 * - Follows best practices for file storage
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThumbnailMigrationService = void 0;
const database_1 = require("../config/database");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const crypto_1 = __importDefault(require("crypto"));
class ThumbnailMigrationService {
    /**
     * Migrate all base64 thumbnails to file storage
     */
    static async migrateAllThumbnails() {
        console.log('🔄 Starting thumbnail migration to comply with industry standards...');
        const stats = {
            migrated: 0,
            errors: 0,
            skipped: 0
        };
        try {
            // Ensure uploads directory exists
            this.ensureUploadsDirectory();
            // Find all scholarships with base64 thumbnails
            const result = await (0, database_1.query)(`
        SELECT id, title, thumbnail 
        FROM scholarships 
        WHERE thumbnail LIKE 'data:image/%'
      `);
            const scholarshipsWithBase64 = result.rows;
            console.log(`📊 Found ${scholarshipsWithBase64.length} scholarships with base64 thumbnails`);
            if (scholarshipsWithBase64.length === 0) {
                console.log('✅ No base64 thumbnails found. Database already complies with industry standards.');
                return stats;
            }
            // Process each scholarship
            for (const scholarship of scholarshipsWithBase64) {
                try {
                    const newThumbnailPath = await this.convertBase64ToFile(scholarship.thumbnail, scholarship.id, scholarship.title);
                    if (newThumbnailPath) {
                        // Update database with new file path
                        await (0, database_1.query)('UPDATE scholarships SET thumbnail = $1 WHERE id = $2', [newThumbnailPath, scholarship.id]);
                        console.log(`✅ Migrated scholarship ${scholarship.id}: ${scholarship.title}`);
                        stats.migrated++;
                    }
                    else {
                        console.warn(`⚠️  Skipped scholarship ${scholarship.id}: Invalid base64 data`);
                        stats.skipped++;
                    }
                }
                catch (error) {
                    console.error(`❌ Error migrating scholarship ${scholarship.id}:`, error);
                    stats.errors++;
                }
            }
            console.log('\n📈 Migration Summary:');
            console.log(`✅ Successfully migrated: ${stats.migrated}`);
            console.log(`⚠️  Skipped (invalid data): ${stats.skipped}`);
            console.log(`❌ Errors: ${stats.errors}`);
            console.log('\n🎉 Thumbnail migration completed!');
        }
        catch (error) {
            console.error('💥 Fatal error during migration:', error);
            throw error;
        }
        return stats;
    }
    /**
     * Convert a single base64 image to file
     */
    static async convertBase64ToFile(base64Data, scholarshipId, scholarshipTitle) {
        try {
            // Validate base64 format
            if (!base64Data.startsWith('data:image/')) {
                return null;
            }
            // Extract MIME type and base64 content
            const [mimeInfo, base64Content] = base64Data.split(',');
            if (!base64Content) {
                return null;
            }
            const mimeType = mimeInfo.split(';')[0].split(':')[1];
            const extension = mimeType.split('/')[1];
            // Validate supported image types
            const supportedTypes = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
            if (!supportedTypes.includes(extension.toLowerCase())) {
                console.warn(`Unsupported image type: ${extension}`);
                return null;
            }
            // Generate secure filename
            const timestamp = Date.now();
            const randomBytes = crypto_1.default.randomBytes(16).toString('hex');
            const sanitizedTitle = scholarshipTitle
                .replace(/[^a-zA-Z0-9]/g, '_')
                .substring(0, 20);
            const filename = `migrated_${sanitizedTitle}_${scholarshipId}_${timestamp}_${randomBytes}.${extension}`;
            const filePath = path_1.default.join(this.UPLOADS_DIR, filename);
            // Convert base64 to buffer and save file
            const buffer = Buffer.from(base64Content, 'base64');
            // Validate file size (max 10MB for migration)
            if (buffer.length > 10 * 1024 * 1024) {
                console.warn(`File too large for scholarship ${scholarshipId}: ${buffer.length} bytes`);
                return null;
            }
            // Write file to disk
            fs_1.default.writeFileSync(filePath, buffer);
            // Return URL path for database
            return `/uploads/scholarships/${filename}`;
        }
        catch (error) {
            console.error('Error converting base64 to file:', error);
            return null;
        }
    }
    /**
     * Ensure uploads directory exists with proper permissions
     */
    static ensureUploadsDirectory() {
        const uploadsDir = path_1.default.join(__dirname, '../../uploads');
        const scholarshipsDir = this.UPLOADS_DIR;
        if (!fs_1.default.existsSync(uploadsDir)) {
            fs_1.default.mkdirSync(uploadsDir, { recursive: true, mode: 0o755 });
        }
        if (!fs_1.default.existsSync(scholarshipsDir)) {
            fs_1.default.mkdirSync(scholarshipsDir, { recursive: true, mode: 0o755 });
        }
    }
    /**
     * Validate migration results
     */
    static async validateMigration() {
        try {
            const result = await (0, database_1.query)(`
        SELECT COUNT(*) as count 
        FROM scholarships 
        WHERE thumbnail LIKE 'data:image/%'
      `);
            const remainingBase64Count = parseInt(result.rows[0].count);
            if (remainingBase64Count === 0) {
                console.log('✅ Migration validation passed: No base64 thumbnails remaining');
                return true;
            }
            else {
                console.warn(`⚠️  Migration validation failed: ${remainingBase64Count} base64 thumbnails still exist`);
                return false;
            }
        }
        catch (error) {
            console.error('Error validating migration:', error);
            return false;
        }
    }
}
exports.ThumbnailMigrationService = ThumbnailMigrationService;
ThumbnailMigrationService.UPLOADS_DIR = path_1.default.join(__dirname, '../../uploads/scholarships');
// Export for CLI usage
exports.default = ThumbnailMigrationService;
