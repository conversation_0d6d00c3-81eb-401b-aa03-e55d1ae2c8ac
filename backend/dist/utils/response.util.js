"use strict";
/**
 * Response utility functions for standardized API responses
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendForbidden = exports.sendUnauthorized = exports.sendNotFound = exports.sendValidationError = exports.sendError = exports.sendSuccess = void 0;
/**
 * Send success response
 */
const sendSuccess = (res, message, data, statusCode = 200) => {
    const response = {
        success: true,
        message,
        data,
        timestamp: new Date().toISOString()
    };
    res.status(statusCode).json(response);
};
exports.sendSuccess = sendSuccess;
/**
 * Send error response
 */
const sendError = (res, message, error, statusCode = 500) => {
    const response = {
        success: false,
        message,
        error: process.env.NODE_ENV === 'development' ? error : undefined,
        timestamp: new Date().toISOString()
    };
    res.status(statusCode).json(response);
};
exports.sendError = sendError;
/**
 * Send validation error response
 */
const sendValidationError = (res, errors, message = 'Validation failed') => {
    const response = {
        success: false,
        message,
        error: { validationErrors: errors },
        timestamp: new Date().toISOString()
    };
    res.status(400).json(response);
};
exports.sendValidationError = sendValidationError;
/**
 * Send not found response
 */
const sendNotFound = (res, message = 'Resource not found') => {
    const response = {
        success: false,
        message,
        timestamp: new Date().toISOString()
    };
    res.status(404).json(response);
};
exports.sendNotFound = sendNotFound;
/**
 * Send unauthorized response
 */
const sendUnauthorized = (res, message = 'Unauthorized access') => {
    const response = {
        success: false,
        message,
        timestamp: new Date().toISOString()
    };
    res.status(401).json(response);
};
exports.sendUnauthorized = sendUnauthorized;
/**
 * Send forbidden response
 */
const sendForbidden = (res, message = 'Access forbidden') => {
    const response = {
        success: false,
        message,
        timestamp: new Date().toISOString()
    };
    res.status(403).json(response);
};
exports.sendForbidden = sendForbidden;
