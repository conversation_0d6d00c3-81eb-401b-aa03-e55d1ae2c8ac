"use strict";
/**
 * Country Routes
 *
 * API routes for country-related operations and scholarship filtering
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const country_controller_1 = require("../controllers/country.controller");
const router = (0, express_1.Router)();
// Public routes
router.get('/', country_controller_1.getAllCountries);
router.get('/search', country_controller_1.searchCountries);
router.get('/sidebar', country_controller_1.getAllCountriesForSidebar);
router.get('/:country/scholarships', country_controller_1.getScholarshipsByCountry);
router.get('/:country/statistics', country_controller_1.getCountryStatistics);
exports.default = router;
