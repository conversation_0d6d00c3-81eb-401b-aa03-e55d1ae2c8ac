"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const user_controller_1 = require("../controllers/user.controller");
const auth_new_1 = require("../middleware/auth.new");
const router = express_1.default.Router();
// Update profile route
router.put('/profile', auth_new_1.authenticate, [
    (0, express_validator_1.body)('name').optional().trim().notEmpty().withMessage('Name cannot be empty'),
    (0, express_validator_1.body)('email').optional().isEmail().withMessage('Please enter a valid email'),
], user_controller_1.updateProfile);
// Change password route
router.put('/change-password', auth_new_1.authenticate, [
    (0, express_validator_1.body)('currentPassword').notEmpty().withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 6 })
        .withMessage('New password must be at least 6 characters long'),
], user_controller_1.changePassword);
// Get all users route (admin only)
router.get('/', auth_new_1.requireAdmin, user_controller_1.getUsers);
// Delete user route (admin only)
router.delete('/:id', auth_new_1.requireAdmin, user_controller_1.deleteUser);
exports.default = router;
