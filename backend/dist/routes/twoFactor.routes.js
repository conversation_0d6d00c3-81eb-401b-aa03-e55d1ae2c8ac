"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_new_1 = require("../middleware/auth.new");
const twoFactor_controller_1 = require("../controllers/twoFactor.controller");
const router = express_1.default.Router();
// Public route for verifying 2FA token during login
router.post('/verify', [
    (0, express_validator_1.body)('adminId').isNumeric().withMessage('Admin ID is required'),
    (0, express_validator_1.body)('token').notEmpty().withMessage('Token is required'),
    (0, express_validator_1.body)('isBackupCode').optional().isBoolean()
], twoFactor_controller_1.verifyTwoFactorToken);
// Protected routes (require authentication)
router.use(auth_new_1.authenticate);
// Get 2FA status
router.get('/status', twoFactor_controller_1.getTwoFactorStatus);
// Initialize 2FA setup
router.post('/initialize', twoFactor_controller_1.initializeTwoFactor);
// Verify and enable 2FA
router.post('/enable', [
    (0, express_validator_1.body)('token').notEmpty().withMessage('Token is required')
], twoFactor_controller_1.verifyAndEnableTwoFactor);
// Disable 2FA
router.post('/disable', [
    (0, express_validator_1.body)('token').notEmpty().withMessage('Token is required')
], twoFactor_controller_1.disableTwoFactor);
exports.default = router;
