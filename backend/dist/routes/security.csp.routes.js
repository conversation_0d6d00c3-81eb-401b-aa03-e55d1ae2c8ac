"use strict";
/**
 * CSP Security Routes
 * Handles Content Security Policy violation reports and CSP management
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_new_1 = require("../middleware/auth.new");
const advancedCSP_1 = require("../middleware/advancedCSP");
const apiResponse_1 = require("../utils/apiResponse");
const database_1 = require("../config/database");
const express_validator_2 = require("express-validator");
const router = express_1.default.Router();
/**
 * @route POST /api/security/csp-violation
 * @desc Handle CSP violation reports
 * @access Public (CSP reports come from browsers)
 */
router.post('/csp-violation', advancedCSP_1.AdvancedCSPService.cspViolationHandler);
/**
 * @route GET /api/security/csp-violations
 * @desc Get CSP violation reports (admin only)
 * @access Private (Super Admin only)
 */
router.get('/csp-violations', [
    auth_new_1.authenticate,
    auth_new_1.requireAdmin,
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('severity')
        .optional()
        .isIn(['low', 'medium', 'high', 'critical'])
        .withMessage('Invalid severity level'),
    (0, express_validator_1.query)('from')
        .optional()
        .isISO8601()
        .withMessage('From date must be in ISO8601 format'),
    (0, express_validator_1.query)('to')
        .optional()
        .isISO8601()
        .withMessage('To date must be in ISO8601 format')
], async (req, res) => {
    try {
        const errors = (0, express_validator_2.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { page = '1', limit = '20', severity, from, to } = req.query;
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const offset = (pageNumber - 1) * limitNumber;
        // Build WHERE clause
        let whereClause = "WHERE event_type = 'SUSPICIOUS_ACTIVITY' AND message LIKE '%CSP violation%'";
        const params = [];
        let paramCount = 1;
        if (severity) {
            whereClause += ` AND severity = $${paramCount}`;
            params.push(severity);
            paramCount++;
        }
        if (from) {
            whereClause += ` AND timestamp >= $${paramCount}`;
            params.push(from);
            paramCount++;
        }
        if (to) {
            whereClause += ` AND timestamp <= $${paramCount}`;
            params.push(to);
            paramCount++;
        }
        // Get total count
        const countResult = await (0, database_1.query)(`
      SELECT COUNT(*) as total FROM security_events ${whereClause}
    `, params);
        // Get violations with pagination
        const violationsResult = await (0, database_1.query)(`
      SELECT 
        id,
        timestamp,
        message,
        email,
        ip,
        user_agent,
        severity,
        risk_score,
        details
      FROM security_events 
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...params, limitNumber, offset]);
        const total = parseInt(countResult.rows[0].total);
        const totalPages = Math.ceil(total / limitNumber);
        // Process violations to extract CSP-specific data
        const violations = violationsResult.rows.map(row => {
            var _a, _b;
            return ({
                id: row.id,
                timestamp: row.timestamp,
                message: row.message,
                email: row.email,
                ip: row.ip,
                userAgent: row.user_agent,
                severity: row.severity,
                riskScore: row.risk_score,
                violation: ((_a = row.details) === null || _a === void 0 ? void 0 : _a.violation) || {},
                suspiciousContent: ((_b = row.details) === null || _b === void 0 ? void 0 : _b.suspiciousContent) || null
            });
        });
        return (0, apiResponse_1.sendPaginatedSuccess)(res, violations, {
            page: pageNumber,
            limit: limitNumber,
            total,
            totalPages
        }, 'CSP violations retrieved successfully');
    }
    catch (error) {
        console.error('Get CSP violations error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve CSP violations', error);
    }
});
/**
 * @route GET /api/security/csp-analytics
 * @desc Get CSP violation analytics
 * @access Private (Super Admin only)
 */
router.get('/csp-analytics', [
    auth_new_1.authenticate,
    auth_new_1.requireAdmin,
    (0, express_validator_1.query)('days')
        .optional()
        .isInt({ min: 1, max: 90 })
        .withMessage('Days must be between 1 and 90')
], async (req, res) => {
    try {
        const errors = (0, express_validator_2.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { days = '7' } = req.query;
        const daysNumber = parseInt(days, 10);
        // Get violation trends
        const trendsResult = await (0, database_1.query)(`
      SELECT 
        DATE_TRUNC('day', timestamp) as date,
        COUNT(*) as violation_count,
        COUNT(DISTINCT ip) as unique_ips,
        AVG(risk_score) as avg_risk_score
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
      GROUP BY DATE_TRUNC('day', timestamp)
      ORDER BY date DESC
    `);
        // Get top violated directives
        const directivesResult = await (0, database_1.query)(`
      SELECT 
        details->'violation'->>'violated-directive' as directive,
        COUNT(*) as count
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
        AND details->'violation'->>'violated-directive' IS NOT NULL
      GROUP BY details->'violation'->>'violated-directive'
      ORDER BY count DESC
      LIMIT 10
    `);
        // Get top source IPs
        const ipsResult = await (0, database_1.query)(`
      SELECT 
        ip,
        COUNT(*) as violation_count,
        MAX(risk_score) as max_risk_score,
        COUNT(DISTINCT details->'violation'->>'violated-directive') as unique_directives
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
      GROUP BY ip
      ORDER BY violation_count DESC
      LIMIT 10
    `);
        // Get blocked URIs
        const blockedUrisResult = await (0, database_1.query)(`
      SELECT 
        details->'violation'->>'blocked-uri' as blocked_uri,
        COUNT(*) as count,
        MAX(risk_score) as max_risk_score
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
        AND details->'violation'->>'blocked-uri' IS NOT NULL
        AND details->'violation'->>'blocked-uri' != ''
      GROUP BY details->'violation'->>'blocked-uri'
      ORDER BY count DESC
      LIMIT 10
    `);
        const analytics = {
            trends: trendsResult.rows,
            topViolatedDirectives: directivesResult.rows,
            topSourceIPs: ipsResult.rows,
            topBlockedURIs: blockedUrisResult.rows,
            summary: {
                totalViolations: trendsResult.rows.reduce((sum, row) => sum + parseInt(row.violation_count), 0),
                uniqueIPs: ipsResult.rows.length,
                avgRiskScore: trendsResult.rows.length > 0
                    ? trendsResult.rows.reduce((sum, row) => sum + parseFloat(row.avg_risk_score || '0'), 0) / trendsResult.rows.length
                    : 0
            }
        };
        return (0, apiResponse_1.sendSuccess)(res, analytics, 'CSP analytics retrieved successfully');
    }
    catch (error) {
        console.error('Get CSP analytics error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve CSP analytics', error);
    }
});
/**
 * @route POST /api/security/csp-config
 * @desc Update CSP configuration (super admin only)
 * @access Private (Super Admin only)
 */
router.post('/csp-config', [
    auth_new_1.authenticate,
    auth_new_1.requireAdmin,
    (0, express_validator_1.body)('enableNonce')
        .optional()
        .isBoolean()
        .withMessage('enableNonce must be a boolean'),
    (0, express_validator_1.body)('strictMode')
        .optional()
        .isBoolean()
        .withMessage('strictMode must be a boolean'),
    (0, express_validator_1.body)('reportOnly')
        .optional()
        .isBoolean()
        .withMessage('reportOnly must be a boolean'),
    (0, express_validator_1.body)('allowedDomains')
        .optional()
        .isArray()
        .withMessage('allowedDomains must be an array'),
    (0, express_validator_1.body)('trustedScriptSources')
        .optional()
        .isArray()
        .withMessage('trustedScriptSources must be an array'),
    (0, express_validator_1.body)('allowInlineStyles')
        .optional()
        .isBoolean()
        .withMessage('allowInlineStyles must be a boolean'),
    (0, express_validator_1.body)('allowInlineScripts')
        .optional()
        .isBoolean()
        .withMessage('allowInlineScripts must be a boolean')
], async (req, res) => {
    var _a;
    try {
        const errors = (0, express_validator_2.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const config = req.body;
        // Store CSP configuration in database
        await (0, database_1.query)(`
      INSERT INTO system_config (key, value, updated_by, updated_at)
      VALUES ('csp_config', $1, $2, CURRENT_TIMESTAMP)
      ON CONFLICT (key) 
      DO UPDATE SET 
        value = EXCLUDED.value,
        updated_by = EXCLUDED.updated_by,
        updated_at = EXCLUDED.updated_at
    `, [JSON.stringify(config), (_a = req.user) === null || _a === void 0 ? void 0 : _a.id]);
        return (0, apiResponse_1.sendSuccess)(res, config, 'CSP configuration updated successfully');
    }
    catch (error) {
        console.error('Update CSP config error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to update CSP configuration', error);
    }
});
exports.default = router;
