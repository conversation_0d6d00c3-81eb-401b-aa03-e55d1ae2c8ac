"use strict";
/**
 * Enhanced Image Routes
 *
 * Production-grade image serving with optimization, caching, and security
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const imageService_1 = require("../services/imageService");
const apiResponse_1 = require("../utils/apiResponse");
const router = express_1.default.Router();
/**
 * Serve optimized images with proper caching
 * GET /api/images/:category/:filename
 */
router.get('/:category/:filename', async (req, res) => {
    await imageService_1.ImageService.serveImage(req, res);
});
/**
 * Get image metadata and thumbnails
 * GET /api/images/:category/:filename/info
 */
router.get('/:category/:filename/info', async (req, res) => {
    try {
        const { category, filename } = req.params;
        const filePath = `uploads/${category}/${filename}`;
        // This would typically get metadata from database
        // For now, return basic info
        return (0, apiResponse_1.sendSuccess)(res, {
            filename,
            category,
            url: `/uploads/${category}/${filename}`,
            thumbnails: {
                small: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_small.webp`,
                medium: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_medium.webp`,
                large: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_large.webp`,
                card: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_card.webp`
            }
        }, 'Image info retrieved successfully');
    }
    catch (error) {
        console.error('Error getting image info:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to get image info', error);
    }
});
/**
 * Health check for image service
 * GET /api/images/health
 */
router.get('/health', (req, res) => {
    return (0, apiResponse_1.sendSuccess)(res, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'image-service'
    }, 'Image service is healthy');
});
exports.default = router;
