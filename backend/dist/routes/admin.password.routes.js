"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const admin_password_controller_1 = require("../controllers/admin.password.controller");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const router = express_1.default.Router();
// Rate limiting for password reset requests
const passwordResetLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // limit each IP to 3 requests per windowMs
    message: 'Too many password reset attempts, please try again after 15 minutes'
});
// Public routes - no authentication required
// Request password reset
router.post('/forgot-password', passwordResetLimiter, [
    (0, express_validator_1.body)('email').isEmail().withMessage('Please enter a valid email')
], admin_password_controller_1.forgotPassword);
// Reset password with token
router.post('/reset-password', [
    (0, express_validator_1.body)('token').notEmpty().withMessage('Token is required'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long')
], admin_password_controller_1.resetPassword);
// Validate reset token
router.get('/validate-token/:token', admin_password_controller_1.validateResetToken);
exports.default = router;
