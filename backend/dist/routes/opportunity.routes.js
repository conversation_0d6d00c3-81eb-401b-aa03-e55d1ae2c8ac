"use strict";
/**
 * Opportunity Routes
 *
 * API routes for opportunity management with proper authentication and validation
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const opportunity_controller_1 = require("../controllers/opportunity.controller");
const auth_new_1 = require("../middleware/auth.new");
const router = (0, express_1.Router)();
// Public routes
router.get('/', opportunity_controller_1.getAllOpportunities);
router.get('/active', opportunity_controller_1.getActiveOpportunities);
router.get('/search', opportunity_controller_1.searchOpportunities);
router.get('/types', opportunity_controller_1.getOpportunityTypes);
router.get('/latest', opportunity_controller_1.getLatestOpportunities);
router.get('/types-sidebar', opportunity_controller_1.getOpportunityTypesForSidebar);
router.get('/type/:type', opportunity_controller_1.getOpportunitiesByType);
router.get('/:id', opportunity_controller_1.getOpportunityById);
// Admin-only routes
router.post('/', auth_new_1.requireAdmin, opportunity_controller_1.createOpportunity);
router.put('/:id', auth_new_1.requireAdmin, opportunity_controller_1.updateOpportunity);
router.delete('/:id', auth_new_1.requireAdmin, opportunity_controller_1.deleteOpportunity);
exports.default = router;
