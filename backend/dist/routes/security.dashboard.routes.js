"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const security_dashboard_controller_1 = require("../controllers/security.dashboard.controller");
const auth_new_1 = require("../middleware/auth.new");
const router = express_1.default.Router();
/**
 * @route GET /api/security/dashboard
 * @desc Get security dashboard overview
 * @access Private (Admin only)
 */
router.get('/dashboard', auth_new_1.authenticate, auth_new_1.requireAdmin, [
    (0, express_validator_1.query)('timeframe')
        .optional()
        .isIn(['hour', 'day', 'week'])
        .withMessage('Timeframe must be hour, day, or week')
], security_dashboard_controller_1.getSecurityDashboard);
/**
 * @route GET /api/security/events
 * @desc Get security events with filtering and pagination
 * @access Private (Admin only)
 */
router.get('/events', auth_new_1.authenticate, auth_new_1.requireAdmin, [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('severity')
        .optional()
        .isIn(['info', 'warn', 'error', 'alert'])
        .withMessage('Invalid severity level'),
    (0, express_validator_1.query)('eventType')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Event type must be 1-100 characters'),
    (0, express_validator_1.query)('ip')
        .optional()
        .isIP()
        .withMessage('Invalid IP address format'),
    (0, express_validator_1.query)('email')
        .optional()
        .isEmail()
        .withMessage('Invalid email format'),
    (0, express_validator_1.query)('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be in ISO8601 format'),
    (0, express_validator_1.query)('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be in ISO8601 format'),
    (0, express_validator_1.query)('resolved')
        .optional()
        .isBoolean()
        .withMessage('Resolved must be a boolean')
], security_dashboard_controller_1.getSecurityEvents);
/**
 * @route GET /api/security/alerts
 * @desc Get security alerts with filtering and pagination
 * @access Private (Admin only)
 */
router.get('/alerts', auth_new_1.authenticate, auth_new_1.requireAdmin, [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('severity')
        .optional()
        .isIn(['low', 'medium', 'high', 'critical'])
        .withMessage('Invalid severity level'),
    (0, express_validator_1.query)('alertType')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Alert type must be 1-100 characters'),
    (0, express_validator_1.query)('acknowledged')
        .optional()
        .isBoolean()
        .withMessage('Acknowledged must be a boolean'),
    (0, express_validator_1.query)('resolved')
        .optional()
        .isBoolean()
        .withMessage('Resolved must be a boolean')
], security_dashboard_controller_1.getSecurityAlerts);
/**
 * @route POST /api/security/alerts/:id/acknowledge
 * @desc Acknowledge a security alert
 * @access Private (Admin only)
 */
router.post('/alerts/:id/acknowledge', auth_new_1.authenticate, auth_new_1.requireAdmin, [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Alert ID must be a positive integer')
], security_dashboard_controller_1.acknowledgeAlert);
/**
 * @route POST /api/security/alerts/:id/resolve
 * @desc Resolve a security alert
 * @access Private (Admin only)
 */
router.post('/alerts/:id/resolve', auth_new_1.authenticate, auth_new_1.requireAdmin, [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Alert ID must be a positive integer'),
    (0, express_validator_1.body)('resolutionNotes')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Resolution notes cannot exceed 1000 characters')
], security_dashboard_controller_1.resolveAlert);
/**
 * @route GET /api/security/ip/:ip/analysis
 * @desc Get IP address analysis including geolocation and risk assessment
 * @access Private (Admin only)
 */
router.get('/ip/:ip/analysis', auth_new_1.authenticate, auth_new_1.requireAdmin, [
    (0, express_validator_1.param)('ip')
        .isIP()
        .withMessage('Invalid IP address format')
], security_dashboard_controller_1.getIPAnalysis);
/**
 * @route GET /api/security/metrics
 * @desc Get real-time security metrics
 * @access Private (Admin only)
 */
router.get('/metrics', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { SecurityMonitor } = await Promise.resolve().then(() => __importStar(require('../utils/securityMonitor')));
        const timeframe = req.query.timeframe || 'day';
        const metrics = await SecurityMonitor.getSecurityMetrics(timeframe);
        res.json({
            success: true,
            data: metrics,
            message: 'Security metrics retrieved successfully'
        });
    }
    catch (error) {
        console.error('Security metrics error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve security metrics',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
/**
 * @route GET /api/security/stats
 * @desc Get security statistics summary
 * @access Private (Admin only)
 */
router.get('/stats', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { query } = await Promise.resolve().then(() => __importStar(require('../config/database')));
        // Get various security statistics
        const [totalEvents, activeAlerts, recentLogins, trustedDevices, riskEvents] = await Promise.all([
            query('SELECT COUNT(*) as count FROM security_events WHERE timestamp > NOW() - INTERVAL \'24 hours\''),
            query('SELECT COUNT(*) as count FROM security_alerts WHERE resolved = false'),
            query('SELECT COUNT(*) as count FROM login_attempts WHERE timestamp > NOW() - INTERVAL \'24 hours\' AND success = true'),
            query('SELECT COUNT(*) as count FROM trusted_devices WHERE trusted = true AND is_active = true'),
            query('SELECT COUNT(*) as count FROM security_events WHERE risk_score > 50 AND timestamp > NOW() - INTERVAL \'24 hours\'')
        ]);
        res.json({
            success: true,
            data: {
                totalEvents24h: parseInt(totalEvents.rows[0].count),
                activeAlerts: parseInt(activeAlerts.rows[0].count),
                successfulLogins24h: parseInt(recentLogins.rows[0].count),
                trustedDevices: parseInt(trustedDevices.rows[0].count),
                highRiskEvents24h: parseInt(riskEvents.rows[0].count),
                timestamp: new Date().toISOString()
            },
            message: 'Security statistics retrieved successfully'
        });
    }
    catch (error) {
        console.error('Security stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve security statistics',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
/**
 * @route GET /api/security/health
 * @desc Get security system health status
 * @access Private (Admin only)
 */
router.get('/health', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { GeolocationService } = await Promise.resolve().then(() => __importStar(require('../utils/geolocation')));
        // Test various security components
        const healthChecks = {
            database: false,
            geolocation: false,
            monitoring: false,
            timestamp: new Date().toISOString()
        };
        try {
            const { query } = await Promise.resolve().then(() => __importStar(require('../config/database')));
            await query('SELECT 1');
            healthChecks.database = true;
        }
        catch (dbError) {
            console.error('Database health check failed:', dbError);
        }
        try {
            // Test geolocation service with a known IP
            await GeolocationService.getGeolocation('*******');
            healthChecks.geolocation = true;
        }
        catch (geoError) {
            console.error('Geolocation health check failed:', geoError);
        }
        try {
            const { SecurityMonitor } = await Promise.resolve().then(() => __importStar(require('../utils/securityMonitor')));
            await SecurityMonitor.getSecurityMetrics('hour');
            healthChecks.monitoring = true;
        }
        catch (monitorError) {
            console.error('Monitoring health check failed:', monitorError);
        }
        const allHealthy = Object.values(healthChecks).every(check => typeof check === 'boolean' ? check : true);
        res.status(allHealthy ? 200 : 503).json({
            success: allHealthy,
            data: healthChecks,
            message: allHealthy ? 'All security systems operational' : 'Some security systems have issues'
        });
    }
    catch (error) {
        console.error('Security health check error:', error);
        res.status(500).json({
            success: false,
            message: 'Security health check failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});
exports.default = router;
