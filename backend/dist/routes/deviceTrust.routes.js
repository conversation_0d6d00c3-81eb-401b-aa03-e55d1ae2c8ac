"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_new_1 = require("../middleware/auth.new");
const deviceTrust_controller_1 = require("../controllers/deviceTrust.controller");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_new_1.authenticate);
/**
 * @route GET /api/devices
 * @desc Get all devices for the authenticated admin
 * @access Private (Admin only)
 */
router.get('/', [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('trusted')
        .optional()
        .isBoolean()
        .withMessage('Trusted must be a boolean'),
    (0, express_validator_1.query)('active')
        .optional()
        .isBoolean()
        .withMessage('Active must be a boolean')
], deviceTrust_controller_1.getAdminDevices);
/**
 * @route GET /api/devices/:id
 * @desc Get device details by ID
 * @access Private (Admin only)
 */
router.get('/:id', [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Device ID must be a positive integer')
], deviceTrust_controller_1.getDeviceById);
/**
 * @route PUT /api/devices/:id/trust
 * @desc Trust a device
 * @access Private (Admin only)
 */
router.put('/:id/trust', [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Device ID must be a positive integer')
], deviceTrust_controller_1.trustDevice);
/**
 * @route PUT /api/devices/:id/untrust
 * @desc Untrust a device
 * @access Private (Admin only)
 */
router.put('/:id/untrust', [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Device ID must be a positive integer')
], deviceTrust_controller_1.untrustDevice);
/**
 * @route DELETE /api/devices/:id
 * @desc Deactivate a device
 * @access Private (Admin only)
 */
router.delete('/:id', [
    (0, express_validator_1.param)('id')
        .isInt({ min: 1 })
        .withMessage('Device ID must be a positive integer')
], deviceTrust_controller_1.deactivateDevice);
exports.default = router;
