"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const scholarship_controller_1 = require("../controllers/scholarship.controller");
const auth_new_1 = require("../middleware/auth.new");
const upload_middleware_1 = require("../middleware/upload.middleware");
const router = express_1.default.Router();
// Get all scholarships (public)
router.get('/', scholarship_controller_1.getScholarships);
// Search scholarships (public)
router.get('/search', scholarship_controller_1.searchScholarships);
// Get scholarship levels for dropdown (public)
router.get('/levels', scholarship_controller_1.getScholarshipLevels);
// Get funding sources for dropdown (public)
router.get('/funding-sources', scholarship_controller_1.getFundingSources);
// Get latest scholarships for sidebar (public)
router.get('/latest', scholarship_controller_1.getLatestScholarships);
// Get related scholarships for sidebar (public)
router.get('/related', scholarship_controller_1.getRelatedScholarships);
// Get countries for sidebar (public)
router.get('/countries-sidebar', scholarship_controller_1.getCountriesForSidebar);
// Get scholarship by ID (public)
router.get('/:id', scholarship_controller_1.getScholarshipById);
// Protected routes (require admin authentication)
router.use(auth_new_1.requireAdmin);
// Create scholarship
router.post('/', upload_middleware_1.handleScholarshipUpload, [
    (0, express_validator_1.body)('title').notEmpty().withMessage('Title is required'),
    (0, express_validator_1.body)('description').notEmpty().withMessage('Description is required'),
    (0, express_validator_1.body)('deadline').isISO8601().withMessage('Valid deadline date is required'),
], scholarship_controller_1.createScholarship);
// Update scholarship
router.put('/:id', upload_middleware_1.handleScholarshipUpload, [
    (0, express_validator_1.body)('title').notEmpty().withMessage('Title is required'),
    (0, express_validator_1.body)('description').notEmpty().withMessage('Description is required'),
    (0, express_validator_1.body)('deadline').isISO8601().withMessage('Valid deadline date is required'),
], scholarship_controller_1.updateScholarship);
// Delete scholarship
router.delete('/:id', scholarship_controller_1.deleteScholarship);
// Bulk import scholarships
router.post('/bulk', [
    (0, express_validator_1.body)('scholarships').isArray().withMessage('Scholarships must be an array'),
    (0, express_validator_1.body)('scholarships.*.title').notEmpty().withMessage('Title is required for each scholarship'),
    (0, express_validator_1.body)('scholarships.*.description').notEmpty().withMessage('Description is required for each scholarship'),
    (0, express_validator_1.body)('scholarships.*.deadline').isISO8601().withMessage('Valid deadline date is required for each scholarship'),
], scholarship_controller_1.bulkImportScholarships);
exports.default = router;
