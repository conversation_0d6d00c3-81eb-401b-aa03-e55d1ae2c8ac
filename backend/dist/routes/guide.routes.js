"use strict";
/**
 * Guide Routes
 *
 * API routes for guide management with proper authentication and validation
 */
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const guide_controller_1 = require("../controllers/guide.controller");
const auth_new_1 = require("../middleware/auth.new");
const router = (0, express_1.Router)();
// Public routes
router.get('/', guide_controller_1.getAllGuides);
router.get('/search', guide_controller_1.searchGuides);
router.get('/category/:category', guide_controller_1.getGuidesByCategory);
router.get('/slug/:slug', guide_controller_1.getGuideBySlug);
router.get('/:id', guide_controller_1.getGuideById);
// Admin-only routes
router.post('/', auth_new_1.requireAdmin, guide_controller_1.createGuide);
router.put('/:id', auth_new_1.requireAdmin, guide_controller_1.updateGuide);
router.delete('/:id', auth_new_1.requireAdmin, guide_controller_1.deleteGuide);
exports.default = router;
