"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const Newsletter_1 = require("../models/Newsletter");
const router = (0, express_1.Router)();
// Get all subscribers
router.get('/', async (req, res) => {
    try {
        const subscribers = await Newsletter_1.Newsletter.findAll();
        res.json(subscribers);
    }
    catch (error) {
        res.status(500).json({ error: 'Error fetching subscribers' });
    }
});
// Get all subscribers (alias for admin portal)
router.get('/subscribers', async (req, res) => {
    try {
        const subscribers = await Newsletter_1.Newsletter.findAll();
        res.json(subscribers);
    }
    catch (error) {
        res.status(500).json({ error: 'Error fetching subscribers' });
    }
});
// Add a new subscriber
router.post('/', async (req, res) => {
    try {
        const { email } = req.body;
        const subscriber = await Newsletter_1.Newsletter.create(email);
        res.json(subscriber);
    }
    catch (error) {
        res.status(500).json({ error: 'Error adding subscriber' });
    }
});
// Delete a subscriber
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await Newsletter_1.Newsletter.delete(Number(id));
        res.json({ message: 'Subscriber removed successfully' });
    }
    catch (error) {
        res.status(500).json({ error: 'Error removing subscriber' });
    }
});
// Bulk import subscribers
router.post('/bulk', [
    (0, express_validator_1.body)('emails').isArray({ min: 1 }).withMessage('At least one email is required'),
    (0, express_validator_1.body)('emails.*').isEmail().withMessage('Invalid email format')
], async (req, res) => {
    try {
        // Check for validation errors
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const { emails } = req.body;
        console.log(`Attempting to bulk import ${emails.length} subscribers`);
        // Track results
        const results = {
            success: 0,
            duplicates: 0,
            failures: 0,
            details: []
        };
        // Process each email
        for (const email of emails) {
            try {
                // Check if email already exists
                const existing = await Newsletter_1.Newsletter.findByEmail(email);
                if (existing) {
                    results.duplicates++;
                    results.details.push({ email, status: 'duplicate' });
                    continue;
                }
                // Create new subscriber
                await Newsletter_1.Newsletter.create(email);
                results.success++;
                results.details.push({ email, status: 'success' });
            }
            catch (error) {
                console.error(`Error adding subscriber ${email}:`, error);
                results.failures++;
                results.details.push({ email, status: 'error' });
            }
        }
        console.log(`Bulk import results: ${results.success} added, ${results.duplicates} duplicates, ${results.failures} failures`);
        res.json({
            message: `Successfully imported ${results.success} subscribers. ${results.duplicates} duplicates skipped. ${results.failures} failures.`,
            results
        });
    }
    catch (error) {
        console.error('Bulk import error:', error);
        res.status(500).json({ error: 'Error processing bulk import' });
    }
});
exports.default = router;
