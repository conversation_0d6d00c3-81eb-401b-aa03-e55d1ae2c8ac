"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const admin_routes_1 = __importDefault(require("./routes/admin.routes"));
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const scholarship_routes_1 = __importDefault(require("./routes/scholarship.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const database_1 = __importDefault(require("./config/database"));
const init_db_1 = __importDefault(require("./config/init-db"));
dotenv_1.default.config();
const app = (0, express_1.default)();
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
// Routes
app.use('/api/admin', admin_routes_1.default);
app.use('/api/auth', auth_routes_1.default);
app.use('/api/scholarships', scholarship_routes_1.default);
app.use('/api/users', user_routes_1.default);
// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Server error', error: process.env.NODE_ENV === 'development' ? err.message : undefined });
});
// Initialize database and start server
const PORT = process.env.PORT || 5000;
// Test database connection
const testDatabaseConnection = async () => {
    try {
        // Test Sequelize connection (for Admin)
        await database_1.default.authenticate();
        console.log('Sequelize database connection established successfully');
        // Initialize Prisma database
        await (0, init_db_1.default)();
        console.log('Prisma database initialized successfully');
        // Start server after database connections are established
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
        });
    }
    catch (error) {
        console.error('Unable to connect to the database:', error);
        process.exit(1);
    }
};
testDatabaseConnection();
