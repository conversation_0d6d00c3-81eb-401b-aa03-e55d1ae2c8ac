import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { scholarshipValidationRules } from '../../middleware/validation';

// Mock express-validator
jest.mock('express-validator', () => ({
  validationResult: jest.fn(),
  body: jest.fn().mockReturnThis(),
  check: jest.fn().mockReturnThis(),
  isEmail: jest.fn().mockReturnThis(),
  isLength: jest.fn().mockReturnThis(),
  isDate: jest.fn().mockReturnThis(),
  isURL: jest.fn().mockReturnThis(),
  isBoolean: jest.fn().mockReturnThis(),
  isIn: jest.fn().mockReturnThis(),
  optional: jest.fn().mockReturnThis(),
  withMessage: jest.fn().mockReturnThis(),
}));

describe('Scholarship Validation Rules', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: jest.Mock;
  
  beforeEach(() => {
    mockReq = {
      body: {},
    };
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    
    mockNext = jest.fn();
    
    (validationResult as jest.Mock).mockImplementation(() => ({
      isEmpty: jest.fn().mockReturnValue(true),
      array: jest.fn().mockReturnValue([]),
    }));
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  describe('createScholarshipValidation', () => {
    it('should pass validation with valid data', async () => {
      mockReq.body = {
        title: 'Test Scholarship',
        description: 'This is a test scholarship',
        deadline: new Date().toISOString(),
        level: 'Undergraduate',
        country: 'United States',
        isOpen: true,
        financial_benefits_summary: 'Financial benefits',
        eligibility_summary: 'Eligibility criteria',
        scholarship_link: 'https://example.com/scholarship',
      };
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.create) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
    
    it('should fail validation with missing required fields', async () => {
      mockReq.body = {
        // Missing title and description
        deadline: new Date().toISOString(),
      };
      
      // Mock validation errors
      (validationResult as jest.Mock).mockImplementation(() => ({
        isEmpty: jest.fn().mockReturnValue(false),
        array: jest.fn().mockReturnValue([
          { param: 'title', msg: 'Title is required' },
          { param: 'description', msg: 'Description is required' },
        ]),
      }));
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.create) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      // Validation should fail and return errors
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        errors: [
          { param: 'title', msg: 'Title is required' },
          { param: 'description', msg: 'Description is required' },
        ],
      });
    });
    
    it('should fail validation with invalid date format', async () => {
      mockReq.body = {
        title: 'Test Scholarship',
        description: 'This is a test scholarship',
        deadline: 'invalid-date', // Invalid date format
        level: 'Undergraduate',
        country: 'United States',
      };
      
      // Mock validation errors
      (validationResult as jest.Mock).mockImplementation(() => ({
        isEmpty: jest.fn().mockReturnValue(false),
        array: jest.fn().mockReturnValue([
          { param: 'deadline', msg: 'Deadline must be a valid date' },
        ]),
      }));
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.create) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      // Validation should fail and return errors
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        errors: [
          { param: 'deadline', msg: 'Deadline must be a valid date' },
        ],
      });
    });
    
    it('should fail validation with invalid URL', async () => {
      mockReq.body = {
        title: 'Test Scholarship',
        description: 'This is a test scholarship',
        deadline: new Date().toISOString(),
        level: 'Undergraduate',
        country: 'United States',
        scholarship_link: 'invalid-url', // Invalid URL
      };
      
      // Mock validation errors
      (validationResult as jest.Mock).mockImplementation(() => ({
        isEmpty: jest.fn().mockReturnValue(false),
        array: jest.fn().mockReturnValue([
          { param: 'scholarship_link', msg: 'Scholarship link must be a valid URL' },
        ]),
      }));
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.create) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      // Validation should fail and return errors
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        errors: [
          { param: 'scholarship_link', msg: 'Scholarship link must be a valid URL' },
        ],
      });
    });
    
    it('should fail validation with invalid level value', async () => {
      mockReq.body = {
        title: 'Test Scholarship',
        description: 'This is a test scholarship',
        deadline: new Date().toISOString(),
        level: 'InvalidLevel', // Invalid level
        country: 'United States',
      };
      
      // Mock validation errors
      (validationResult as jest.Mock).mockImplementation(() => ({
        isEmpty: jest.fn().mockReturnValue(false),
        array: jest.fn().mockReturnValue([
          { param: 'level', msg: 'Level must be one of: Undergraduate, Graduate, PhD' },
        ]),
      }));
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.create) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      // Validation should fail and return errors
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        errors: [
          { param: 'level', msg: 'Level must be one of: Undergraduate, Graduate, PhD' },
        ],
      });
    });
  });
  
  describe('updateScholarshipValidation', () => {
    it('should pass validation with valid data', async () => {
      mockReq.body = {
        title: 'Updated Scholarship',
        description: 'This is an updated scholarship',
      };
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.update) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
    
    it('should pass validation with partial data', async () => {
      mockReq.body = {
        title: 'Updated Scholarship',
        // Other fields are optional for update
      };
      
      // Run all validation middleware
      for (const validationFn of scholarshipValidationRules.update) {
        await validationFn(mockReq as Request, mockRes as Response, mockNext);
      }
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });
});
