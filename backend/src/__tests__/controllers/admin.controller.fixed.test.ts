import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Request, Response } from 'express';
import * as adminController from '../../controllers/admin.controller';
import { mockRequest, mockResponse, mockPrismaClient } from '../helpers/test-utils';

// Mock PrismaClient
jest.mock('@prisma/client', () => {
  return {
    PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient()),
  };
});

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
}));

describe('Admin Controller', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let prisma: any;
  
  beforeEach(() => {
    mockReq = mockRequest({
      user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true }
    });
    mockRes = mockResponse();
    prisma = mockPrismaClient();
    
    // Reset mock implementations for each test
    jest.clearAllMocks();
  });
  
  describe('getAdmins', () => {
    it('should return all admins', async () => {
      // Mock data
      const mockAdmins = [
        {
          id: 1,
          name: 'Admin 1',
          email: '<EMAIL>',
          role: 'admin',
        },
        {
          id: 2,
          name: 'Admin 2',
          email: '<EMAIL>',
          role: 'admin',
        },
      ];
      
      // Setup mocks
      prisma.admin.findMany.mockResolvedValue(mockAdmins);
      
      // Call the controller
      await adminController.getAdmins(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith(mockAdmins);
    });
    
    it('should handle errors', async () => {
      // Mock error
      const error = new Error('Database error');
      prisma.admin.findMany.mockRejectedValue(error);
      
      // Call the controller
      await adminController.getAdmins(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Error getting admins' });
    });
  });
  
  describe('createAdmin', () => {
    it('should create a new admin', async () => {
      // Mock data
      const adminData = {
        name: 'New Admin',
        email: '<EMAIL>',
        password: 'Password123!',
        privileges: ['users', 'scholarships'],
        isMainAdmin: false,
      };
      
      const createdAdmin = {
        id: 3,
        name: 'New Admin',
        email: '<EMAIL>',
        role: 'admin',
        privileges: JSON.stringify(['users', 'scholarships']),
        isMainAdmin: false,
        twoFactorEnabled: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Setup mocks
      mockReq.body = adminData;
      prisma.admin.findUnique.mockResolvedValue(null);
      prisma.admin.create.mockResolvedValue(createdAdmin);
      
      // Call the controller
      await adminController.createAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        id: createdAdmin.id,
        name: createdAdmin.name,
        email: createdAdmin.email,
        role: createdAdmin.role,
        privileges: createdAdmin.privileges,
        isMainAdmin: createdAdmin.isMainAdmin,
        twoFactorEnabled: createdAdmin.twoFactorEnabled,
        createdAt: createdAdmin.createdAt,
        updatedAt: createdAdmin.updatedAt
      });
    });
    
    it('should return 409 if admin with email already exists', async () => {
      // Setup mocks
      mockReq.body = {
        name: 'Existing Admin',
        email: '<EMAIL>',
        password: 'Password123!',
        privileges: ['users'],
      };
      
      prisma.admin.findUnique.mockResolvedValue({ id: 1 });
      
      // Call the controller
      await adminController.createAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(409);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin with this email already exists' });
    });
  });
  
  describe('updateAdmin', () => {
    it('should update an admin', async () => {
      // Mock data
      const updateData = {
        name: 'Updated Admin',
      };
      
      const existingAdmin = {
        id: 1,
        name: 'Admin 1',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: false,
      };
      
      const updatedAdmin = {
        id: 1,
        name: 'Updated Admin',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: false,
        updatedAt: new Date(),
      };
      
      // Setup mocks
      mockReq.params = { id: '1' };
      mockReq.body = updateData;
      prisma.admin.findUnique.mockResolvedValue(existingAdmin);
      prisma.admin.update.mockResolvedValue(updatedAdmin);
      
      // Call the controller
      await adminController.updateAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith(updatedAdmin);
    });
    
    it('should return 404 if admin not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockReq.body = { name: 'Updated Admin' };
      prisma.admin.findUnique.mockResolvedValue(null);
      
      // Call the controller
      await adminController.updateAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });
  });
  
  describe('deleteAdmin', () => {
    it('should delete an admin', async () => {
      // Mock data
      const adminToDelete = {
        id: 2,
        name: 'Admin 2',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: false,
      };
      
      // Setup mocks
      mockReq.params = { id: '2' };
      prisma.admin.findUnique.mockResolvedValue(adminToDelete);
      prisma.admin.delete.mockResolvedValue(adminToDelete);
      
      // Call the controller
      await adminController.deleteAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin deleted successfully' });
    });
    
    it('should return 404 if admin not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      prisma.admin.findUnique.mockResolvedValue(null);
      
      // Call the controller
      await adminController.deleteAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });
    
    it('should prevent deleting the last main admin', async () => {
      // Mock data
      const mainAdmin = {
        id: 1,
        name: 'Main Admin',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: true,
      };
      
      // Setup mocks
      mockReq.params = { id: '1' };
      prisma.admin.findUnique.mockResolvedValue(mainAdmin);
      prisma.admin.count.mockResolvedValue(1);
      
      // Call the controller
      await adminController.deleteAdmin(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Cannot delete the last main admin' });
    });
  });
});
