import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { updateProfile, changePassword, getUsers, deleteUser } from '../../controllers/user.controller';
import User from '../../models/user.model';
import { auth, adminAuth } from '../../middleware/auth.middleware';

const app = express();
app.use(express.json());

// Mock auth middleware
jest.mock('../../middleware/auth.middleware', () => ({
  auth: (req: any, res: any, next: any) => {
    req.user = req.headers.user ? JSON.parse(req.headers.user as string) : null;
    next();
  },
  adminAuth: (req: any, res: any, next: any) => next(),
}));

// Routes
app.put('/profile', auth, updateProfile);
app.put('/change-password', auth, changePassword);
app.get('/users', auth, adminAuth, getUsers);
app.delete('/users/:id', auth, adminAuth, deleteUser);

describe('User Controller', () => {
  let testUser: any;
  let adminUser: any;

  beforeEach(async () => {
    // Create test user
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
    });

    // Create admin user
    adminUser = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
    });
  });

  describe('updateProfile', () => {
    it('should update user profile', async () => {
      const response = await request(app)
        .put('/profile')
        .send({
          name: 'Updated Name',
          email: '<EMAIL>',
        })
        .set('user', JSON.stringify({ id: testUser._id.toString() }));

      expect(response.status).toBe(200);
      expect(response.body.user.name).toBe('Updated Name');
      expect(response.body.user.email).toBe('<EMAIL>');
    });
  });

  describe('changePassword', () => {
    it('should change user password', async () => {
      const response = await request(app)
        .put('/change-password')
        .send({
          currentPassword: 'password123',
          newPassword: 'newpassword123',
        })
        .set('user', JSON.stringify({ id: testUser._id.toString() }));

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Password changed successfully');
    });

    it('should fail with incorrect current password', async () => {
      const response = await request(app)
        .put('/change-password')
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'newpassword123',
        })
        .set('user', JSON.stringify({ id: testUser._id.toString() }));

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Current password is incorrect');
    });
  });

  describe('getUsers', () => {
    it('should get all users', async () => {
      const response = await request(app)
        .get('/users')
        .set('user', JSON.stringify({ id: adminUser._id.toString(), role: 'admin' }));

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(response.body[0]).not.toHaveProperty('password');
    });
  });

  describe('deleteUser', () => {
    it('should delete a user', async () => {
      const response = await request(app)
        .delete(`/users/${testUser._id}`)
        .set('user', JSON.stringify({ id: adminUser._id.toString(), role: 'admin' }));

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('User deleted successfully');

      const deletedUser = await User.findById(testUser._id);
      expect(deletedUser).toBeNull();
    });
  });
}); 