const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');

// Create mock functions before importing the controller
const mockPrisma = {
  admin: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  $connect: jest.fn(),
  $disconnect: jest.fn(),
};

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
}));

// Mock PrismaClient before importing the controller
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrisma),
}));

// Now import the controller
const adminController = require('../../controllers/admin.controller');

describe('Admin Controller', () => {
  let mockReq;
  let mockRes;
  
  beforeEach(() => {
    // Create mock request and response
    mockReq = {
      params: {},
      query: {},
      body: {},
      user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true },
    };
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };
    
    // Reset mock implementations for each test
    jest.clearAllMocks();
  });
  
  describe('getAdmins', () => {
    it('should return all admins', async () => {
      // Mock data
      const mockAdmins = [
        {
          id: 1,
          name: 'Admin 1',
          email: '<EMAIL>',
          role: 'admin',
        },
        {
          id: 2,
          name: 'Admin 2',
          email: '<EMAIL>',
          role: 'admin',
        },
      ];
      
      // Setup mocks
      mockPrisma.admin.findMany.mockResolvedValue(mockAdmins);
      
      // Call the controller
      await adminController.getAdmins(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockAdmins);
    });
    
    it('should handle errors', async () => {
      // Mock error
      const error = new Error('Database error');
      mockPrisma.admin.findMany.mockRejectedValue(error);
      
      // Call the controller
      await adminController.getAdmins(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Error getting admins' });
    });
  });
  
  describe('createAdmin', () => {
    it('should create a new admin', async () => {
      // Mock data
      const adminData = {
        name: 'New Admin',
        email: '<EMAIL>',
        password: 'Password123!',
        privileges: ['users', 'scholarships'],
        isMainAdmin: false,
      };
      
      const createdAdmin = {
        id: 3,
        name: 'New Admin',
        email: '<EMAIL>',
        role: 'admin',
        privileges: JSON.stringify(['users', 'scholarships']),
        isMainAdmin: false,
        twoFactorEnabled: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Setup mocks
      mockReq.body = adminData;
      mockPrisma.admin.findUnique.mockResolvedValue(null);
      mockPrisma.admin.create.mockResolvedValue(createdAdmin);
      
      // Call the controller
      await adminController.createAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        id: createdAdmin.id,
        name: createdAdmin.name,
        email: createdAdmin.email,
        role: createdAdmin.role,
        privileges: createdAdmin.privileges,
        isMainAdmin: createdAdmin.isMainAdmin,
        twoFactorEnabled: createdAdmin.twoFactorEnabled,
        createdAt: createdAdmin.createdAt,
        updatedAt: createdAdmin.updatedAt
      });
    });
    
    it('should return 409 if admin with email already exists', async () => {
      // Setup mocks
      mockReq.body = {
        name: 'Existing Admin',
        email: '<EMAIL>',
        password: 'Password123!',
        privileges: ['users'],
      };
      
      mockPrisma.admin.findUnique.mockResolvedValue({ id: 1 });
      
      // Call the controller
      await adminController.createAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(409);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin with this email already exists' });
    });
  });
  
  describe('updateAdmin', () => {
    it('should update an admin', async () => {
      // Mock data
      const updateData = {
        name: 'Updated Admin',
      };
      
      const existingAdmin = {
        id: 1,
        name: 'Admin 1',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: false,
      };
      
      const updatedAdmin = {
        id: 1,
        name: 'Updated Admin',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: false,
        updatedAt: new Date(),
      };
      
      // Setup mocks
      mockReq.params = { id: '1' };
      mockReq.body = updateData;
      mockPrisma.admin.findUnique.mockResolvedValue(existingAdmin);
      mockPrisma.admin.update.mockResolvedValue(updatedAdmin);
      
      // Call the controller
      await adminController.updateAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(updatedAdmin);
    });
    
    it('should return 404 if admin not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockReq.body = { name: 'Updated Admin' };
      mockPrisma.admin.findUnique.mockResolvedValue(null);
      
      // Call the controller
      await adminController.updateAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });
  });
  
  describe('deleteAdmin', () => {
    it('should delete an admin', async () => {
      // Mock data
      const adminToDelete = {
        id: 2,
        name: 'Admin 2',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: false,
      };
      
      // Setup mocks
      mockReq.params = { id: '2' };
      mockPrisma.admin.findUnique.mockResolvedValue(adminToDelete);
      mockPrisma.admin.count.mockResolvedValue(2);
      mockPrisma.admin.delete.mockResolvedValue(adminToDelete);
      
      // Call the controller
      await adminController.deleteAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin deleted successfully' });
    });
    
    it('should return 404 if admin not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockPrisma.admin.findUnique.mockResolvedValue(null);
      
      // Call the controller
      await adminController.deleteAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Admin not found' });
    });
    
    it('should prevent deleting the last main admin', async () => {
      // Mock data
      const mainAdmin = {
        id: 1,
        name: 'Main Admin',
        email: '<EMAIL>',
        role: 'admin',
        isMainAdmin: true,
      };
      
      // Setup mocks
      mockReq.params = { id: '1' };
      mockPrisma.admin.findUnique.mockResolvedValue(mainAdmin);
      mockPrisma.admin.count.mockResolvedValue(1);
      
      // Call the controller
      await adminController.deleteAdmin(mockReq, mockRes);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Cannot delete the last main admin' });
    });
  });
});
