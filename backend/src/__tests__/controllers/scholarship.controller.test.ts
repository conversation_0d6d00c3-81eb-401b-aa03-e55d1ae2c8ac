import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import * as scholarshipController from '../../controllers/scholarship.controller';

// Mock PrismaClient
jest.mock('@prisma/client', () => {
  const mockScholarship = {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };
  
  return {
    PrismaClient: jest.fn().mockImplementation(() => ({
      scholarship: mockScholarship,
      $connect: jest.fn(),
      $disconnect: jest.fn(),
    })),
  };
});

describe('Scholarship Controller', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let prisma: PrismaClient;
  
  beforeEach(() => {
    mockReq = {
      params: {},
      query: {},
      body: {},
      user: { id: 1, email: '<EMAIL>', role: 'admin', isMainAdmin: true },
    };
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    
    prisma = new PrismaClient();
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  describe('getScholarships', () => {
    it('should return scholarships with pagination', async () => {
      const mockScholarships = [
        { id: 1, title: 'Scholarship 1', description: 'Description 1', createdBy: 1 },
        { id: 2, title: 'Scholarship 2', description: 'Description 2', createdBy: 1 },
      ];
      
      const mockCount = 2;
      
      (prisma.scholarship.findMany as jest.Mock).mockResolvedValue(mockScholarships);
      (prisma.scholarship.findMany as jest.Mock).mockResolvedValueOnce(mockCount);
      
      await scholarshipController.getScholarships(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        scholarships: mockScholarships,
        pagination: {
          total: mockCount,
          page: 1,
          limit: 10,
          pages: 1,
        },
      });
    });
    
    it('should handle errors', async () => {
      const error = new Error('Database error');
      (prisma.scholarship.findMany as jest.Mock).mockRejectedValue(error);
      
      await scholarshipController.getScholarships(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Server error' });
    });
  });
  
  describe('getScholarshipById', () => {
    it('should return a scholarship by ID', async () => {
      const mockScholarship = {
        id: 1,
        title: 'Scholarship 1',
        description: 'Description 1',
        createdBy: 1,
      };
      
      mockReq.params = { id: '1' };
      (prisma.scholarship.findUnique as jest.Mock).mockResolvedValue(mockScholarship);
      
      await scholarshipController.getScholarshipById(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(mockScholarship);
    });
    
    it('should return 404 if scholarship not found', async () => {
      mockReq.params = { id: '999' };
      (prisma.scholarship.findUnique as jest.Mock).mockResolvedValue(null);
      
      await scholarshipController.getScholarshipById(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
    });
  });
  
  describe('createScholarship', () => {
    it('should create a new scholarship', async () => {
      const newScholarship = {
        title: 'New Scholarship',
        description: 'New Description',
        deadline: new Date(),
        createdBy: 1,
      };
      
      mockReq.body = newScholarship;
      
      const createdScholarship = {
        id: 1,
        ...newScholarship,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      (prisma.scholarship.create as jest.Mock).mockResolvedValue(createdScholarship);
      
      await scholarshipController.createScholarship(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(createdScholarship);
    });
  });
  
  describe('updateScholarship', () => {
    it('should update an existing scholarship', async () => {
      const updateData = {
        title: 'Updated Scholarship',
        description: 'Updated Description',
      };
      
      mockReq.params = { id: '1' };
      mockReq.body = updateData;
      
      const updatedScholarship = {
        id: 1,
        ...updateData,
        deadline: new Date(),
        createdBy: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      (prisma.scholarship.findUnique as jest.Mock).mockResolvedValue({ id: 1 });
      (prisma.scholarship.update as jest.Mock).mockResolvedValue(updatedScholarship);
      
      await scholarshipController.updateScholarship(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(updatedScholarship);
    });
    
    it('should return 404 if scholarship to update not found', async () => {
      mockReq.params = { id: '999' };
      mockReq.body = { title: 'Updated Scholarship' };
      
      (prisma.scholarship.findUnique as jest.Mock).mockResolvedValue(null);
      
      await scholarshipController.updateScholarship(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
    });
  });
  
  describe('deleteScholarship', () => {
    it('should delete an existing scholarship', async () => {
      mockReq.params = { id: '1' };
      
      (prisma.scholarship.findUnique as jest.Mock).mockResolvedValue({ id: 1 });
      (prisma.scholarship.delete as jest.Mock).mockResolvedValue({ id: 1 });
      
      await scholarshipController.deleteScholarship(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship deleted successfully' });
    });
    
    it('should return 404 if scholarship to delete not found', async () => {
      mockReq.params = { id: '999' };
      
      (prisma.scholarship.findUnique as jest.Mock).mockResolvedValue(null);
      
      await scholarshipController.deleteScholarship(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
    });
  });
});
