import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Request, Response } from 'express';

// Create mock functions before importing the controller
const mockPrisma = {
  scholarship: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  $connect: jest.fn(),
  $disconnect: jest.fn(),
};

// Mock PrismaClient before importing the controller
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrisma),
}));

// Now import the controller
import * as scholarshipController from '../../controllers/scholarship.controller';

describe('Scholarship Controller', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    // Create mock request and response
    mockReq = {
      params: {},
      query: {},
      body: {},
      user: { id: 1, email: '<EMAIL>', role: 'user', isMainAdmin: false },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    // Reset mock implementations for each test
    jest.clearAllMocks();
  });

  describe('getScholarships', () => {
    it('should return all scholarships with pagination', async () => {
      // Mock data
      const mockScholarships = [
        {
          id: 1,
          title: 'Scholarship 1',
          description: 'Description 1',
          createdBy: 1,
        },
        {
          id: 2,
          title: 'Scholarship 2',
          description: 'Description 2',
          createdBy: 1,
        },
      ];

      const mockCount = 2;

      // Setup mocks
      mockPrisma.scholarship.findMany.mockResolvedValue(mockScholarships);
      mockPrisma.scholarship.count.mockResolvedValue(mockCount);

      // Call the controller
      await scholarshipController.getScholarships(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        scholarships: mockScholarships,
        pagination: {
          total: mockCount,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });
    });

    it('should handle errors', async () => {
      // Mock error
      const error = new Error('Database error');
      mockPrisma.scholarship.findMany.mockRejectedValue(error);

      // Call the controller
      await scholarshipController.getScholarships(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Server error' });
    });
  });

  describe('getScholarshipById', () => {
    it('should return a scholarship by ID', async () => {
      // Mock data
      const mockScholarship = {
        id: 1,
        title: 'Scholarship 1',
        description: 'Description 1',
        createdBy: 1,
      };

      // Setup mocks
      mockReq.params = { id: '1' };
      mockPrisma.scholarship.findUnique.mockResolvedValue(mockScholarship);

      // Call the controller
      await scholarshipController.getScholarshipById(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith(mockScholarship);
    });

    it('should return 404 if scholarship not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockPrisma.scholarship.findUnique.mockResolvedValue(null);

      // Call the controller
      await scholarshipController.getScholarshipById(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
    });
  });

  describe('createScholarship', () => {
    it('should create a new scholarship', async () => {
      // Mock data
      const scholarshipData = {
        title: 'New Scholarship',
        description: 'New Description',
        deadline: new Date(),
        level: 'Undergraduate',
        country: 'United States',
      };

      const createdScholarship = {
        id: 1,
        ...scholarshipData,
        createdBy: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mocks
      mockReq.body = scholarshipData;
      mockReq.user = { id: 1, email: '<EMAIL>', role: 'user', isMainAdmin: false };
      mockPrisma.scholarship.create.mockResolvedValue(createdScholarship);

      // Call the controller
      await scholarshipController.createScholarship(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith(createdScholarship);
    });
  });

  describe('updateScholarship', () => {
    it('should update a scholarship', async () => {
      // Mock data
      const updateData = {
        title: 'Updated Scholarship',
        description: 'Updated Description',
      };

      const updatedScholarship = {
        id: 1,
        title: 'Updated Scholarship',
        description: 'Updated Description',
        deadline: new Date(),
        createdBy: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Setup mocks
      mockReq.params = { id: '1' };
      mockReq.body = updateData;
      mockPrisma.scholarship.findUnique.mockResolvedValue({ id: 1 });
      mockPrisma.scholarship.update.mockResolvedValue(updatedScholarship);

      // Call the controller
      await scholarshipController.updateScholarship(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith(updatedScholarship);
    });

    it('should return 404 if scholarship not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockReq.body = { title: 'Updated Scholarship' };
      mockPrisma.scholarship.findUnique.mockResolvedValue(null);

      // Call the controller
      await scholarshipController.updateScholarship(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
    });
  });

  describe('deleteScholarship', () => {
    it('should delete a scholarship', async () => {
      // Setup mocks
      mockReq.params = { id: '1' };
      mockPrisma.scholarship.findUnique.mockResolvedValue({ id: 1 });
      mockPrisma.scholarship.delete.mockResolvedValue({ id: 1 });

      // Call the controller
      await scholarshipController.deleteScholarship(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship deleted successfully' });
    });

    it('should return 404 if scholarship not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockPrisma.scholarship.findUnique.mockResolvedValue(null);

      // Call the controller
      await scholarshipController.deleteScholarship(mockReq as Request, mockRes as Response);

      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Scholarship not found' });
    });
  });
});
