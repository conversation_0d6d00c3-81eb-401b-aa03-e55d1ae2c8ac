import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { Request, Response } from 'express';
import * as userController from '../../controllers/user.controller';
import { mockRequest, mockResponse, mockPrismaClient } from '../helpers/test-utils';

// Mock PrismaClient
jest.mock('@prisma/client', () => {
  return {
    PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient()),
  };
});

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
}));

// Mock jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('mock_token'),
}));

describe('User Controller', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let prisma: any;
  let testUser: any;
  let adminUser: any;
  
  beforeEach(() => {
    mockReq = mockRequest();
    mockRes = mockResponse();
    prisma = mockPrismaClient();
    
    // Create test users
    testUser = {
      id: 1,
      name: 'Test User',
      email: '<EMAIL>',
      password: 'hashed_password',
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    adminUser = {
      id: 2,
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'hashed_password',
      role: 'admin',
      isMainAdmin: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Reset mock implementations for each test
    jest.clearAllMocks();
  });
  
  describe('getAllUsers', () => {
    it('should return all users (admin only)', async () => {
      // Mock data
      const mockUsers = [testUser, adminUser];
      
      // Setup mocks
      mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
      prisma.user.findMany.mockResolvedValue(mockUsers);
      
      // Call the controller
      await userController.getAllUsers(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith(mockUsers);
    });
    
    it('should handle errors', async () => {
      // Mock error
      const error = new Error('Database error');
      mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
      prisma.user.findMany.mockRejectedValue(error);
      
      // Call the controller
      await userController.getAllUsers(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'Error getting users' });
    });
  });
  
  describe('getUserById', () => {
    it('should return a user by ID', async () => {
      // Setup mocks
      mockReq.params = { id: '1' };
      prisma.user.findUnique.mockResolvedValue(testUser);
      
      // Call the controller
      await userController.getUserById(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith(testUser);
    });
    
    it('should return 404 if user not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      prisma.user.findUnique.mockResolvedValue(null);
      
      // Call the controller
      await userController.getUserById(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User not found' });
    });
  });
  
  describe('createUser', () => {
    it('should create a new user', async () => {
      // Mock data
      const userData = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      const createdUser = {
        id: 3,
        name: 'New User',
        email: '<EMAIL>',
        role: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Setup mocks
      mockReq.body = userData;
      prisma.user.findUnique.mockResolvedValue(null);
      prisma.user.create.mockResolvedValue(createdUser);
      
      // Call the controller
      await userController.createUser(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        user: {
          id: createdUser.id,
          name: createdUser.name,
          email: createdUser.email,
          role: createdUser.role,
          createdAt: createdUser.createdAt,
          updatedAt: createdUser.updatedAt
        },
        token: 'mock_token'
      });
    });
    
    it('should return 409 if user with email already exists', async () => {
      // Setup mocks
      mockReq.body = {
        name: 'Existing User',
        email: '<EMAIL>',
        password: 'Password123!',
      };
      
      prisma.user.findUnique.mockResolvedValue(testUser);
      
      // Call the controller
      await userController.createUser(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(409);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User with this email already exists' });
    });
  });
  
  describe('deleteUser', () => {
    it('should delete a user (admin only)', async () => {
      // Setup mocks
      mockReq.params = { id: '1' };
      mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
      prisma.user.findUnique.mockResolvedValue(testUser);
      prisma.user.delete.mockResolvedValue(testUser);
      
      // Call the controller
      await userController.deleteUser(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User deleted successfully' });
    });
    
    it('should return 404 if user not found', async () => {
      // Setup mocks
      mockReq.params = { id: '999' };
      mockReq.user = { id: 2, email: '<EMAIL>', role: 'admin', isMainAdmin: true };
      prisma.user.findUnique.mockResolvedValue(null);
      
      // Call the controller
      await userController.deleteUser(mockReq as Request, mockRes as Response);
      
      // Assertions
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({ message: 'User not found' });
    });
  });
});
