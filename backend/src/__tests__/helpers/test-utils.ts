import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { UserPayload } from '../../types/express';

/**
 * Create a mock Express request object
 */
export const mockRequest = (options: {
  params?: Record<string, any>;
  query?: Record<string, any>;
  body?: Record<string, any>;
  user?: UserPayload;
  headers?: Record<string, any>;
} = {}): Partial<Request> => {
  return {
    params: options.params || {},
    query: options.query || {},
    body: options.body || {},
    user: options.user || {
      id: 1,
      email: '<EMAIL>',
      role: 'user',
      isMainAdmin: false
    },
    headers: options.headers || {},
  };
};

/**
 * Create a mock Express response object
 */
export const mockResponse = (): Partial<Response> => {
  const res: Partial<Response> = {};
  
  // Use 'as any' to bypass TypeScript's type checking for these mocks
  res.status = jest.fn().mockReturnValue(res) as any;
  res.json = jest.fn().mockReturnValue(res) as any;
  res.send = jest.fn().mockReturnValue(res) as any;
  
  return res;
};

/**
 * Create a mock next function
 */
export const mockNext = jest.fn();

/**
 * Create a mock Prisma client for testing
 */
export const mockPrismaClient = () => {
  const mockPrisma: any = {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    scholarship: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    message: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    newsletter: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  };
  
  return mockPrisma;
};

/**
 * Mock for the User model (for backward compatibility with Mongoose tests)
 */
export const mockUserModel = {
  create: jest.fn(),
  findById: jest.fn(),
  findOne: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  findByIdAndDelete: jest.fn(),
};

/**
 * Mock for bcrypt
 */
export const mockBcrypt = {
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
};

/**
 * Mock for jsonwebtoken
 */
export const mockJwt = {
  sign: jest.fn().mockReturnValue('mock_token'),
  verify: jest.fn().mockReturnValue({ 
    id: 1, 
    email: '<EMAIL>', 
    role: 'user', 
    isMainAdmin: false 
  }),
};

/**
 * Mock for express-validator
 */
export const mockValidationResult = {
  isEmpty: jest.fn().mockReturnValue(true),
  array: jest.fn().mockReturnValue([]),
};

/**
 * Setup all mocks for a test file
 */
export const setupMocks = () => {
  // Mock PrismaClient
  jest.mock('@prisma/client', () => ({
    PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient()),
  }));
  
  // Mock bcryptjs
  jest.mock('bcryptjs', () => mockBcrypt);
  
  // Mock jsonwebtoken
  jest.mock('jsonwebtoken', () => mockJwt);
  
  // Mock express-validator
  jest.mock('express-validator', () => ({
    validationResult: jest.fn().mockImplementation(() => mockValidationResult),
    body: jest.fn().mockReturnThis(),
    check: jest.fn().mockReturnThis(),
    param: jest.fn().mockReturnThis(),
    query: jest.fn().mockReturnThis(),
    isEmail: jest.fn().mockReturnThis(),
    isLength: jest.fn().mockReturnThis(),
    isDate: jest.fn().mockReturnThis(),
    isURL: jest.fn().mockReturnThis(),
    isBoolean: jest.fn().mockReturnThis(),
    isIn: jest.fn().mockReturnThis(),
    optional: jest.fn().mockReturnThis(),
    withMessage: jest.fn().mockReturnThis(),
  }));
  
  // Mock User model for backward compatibility
  jest.mock('../../models/user.model', () => mockUserModel);
};

/**
 * Reset all mocks after tests
 */
export const resetMocks = () => {
  jest.resetAllMocks();
};

export default {
  mockRequest,
  mockResponse,
  mockNext,
  mockPrismaClient,
  mockUserModel,
  mockBcrypt,
  mockJwt,
  mockValidationResult,
  setupMocks,
  resetMocks,
};
