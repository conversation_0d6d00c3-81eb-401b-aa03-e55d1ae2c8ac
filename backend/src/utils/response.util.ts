/**
 * Response utility functions for standardized API responses
 */

import { Response } from 'express';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
  timestamp: string;
}

/**
 * Send success response
 */
export const sendSuccess = <T = any>(
  res: Response,
  message: string,
  data?: T,
  statusCode: number = 200
): void => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };

  res.status(statusCode).json(response);
};

/**
 * Send error response
 */
export const sendError = (
  res: Response,
  message: string,
  error?: any,
  statusCode: number = 500
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    error: process.env.NODE_ENV === 'development' ? error : undefined,
    timestamp: new Date().toISOString()
  };

  res.status(statusCode).json(response);
};

/**
 * Send validation error response
 */
export const sendValidationError = (
  res: Response,
  errors: string[],
  message: string = 'Validation failed'
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    error: { validationErrors: errors },
    timestamp: new Date().toISOString()
  };

  res.status(400).json(response);
};

/**
 * Send not found response
 */
export const sendNotFound = (
  res: Response,
  message: string = 'Resource not found'
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  res.status(404).json(response);
};

/**
 * Send unauthorized response
 */
export const sendUnauthorized = (
  res: Response,
  message: string = 'Unauthorized access'
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  res.status(401).json(response);
};

/**
 * Send forbidden response
 */
export const sendForbidden = (
  res: Response,
  message: string = 'Access forbidden'
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  res.status(403).json(response);
};
