import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Define log formats
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${info.data ? ' ' + JSON.stringify(info.data) : ''}`
  )
);

const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.json()
);

// Create the logger
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  defaultMeta: { service: 'mabourse-api' },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: consoleFormat,
    }),
    // Error log file transport
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Combined log file transport
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Database query log file transport (for debugging and performance monitoring)
    new winston.transports.File({
      filename: path.join(logDir, 'database.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// Create specialized loggers
export const dbLogger = {
  query: (message: string, data?: any) => {
    logger.info(`[DB:QUERY] ${message}`, { data, category: 'database' });
  },
  error: (message: string, data?: any) => {
    logger.error(`[DB:ERROR] ${message}`, { data, category: 'database' });
  },
  warn: (message: string, data?: any) => {
    logger.warn(`[DB:WARN] ${message}`, { data, category: 'database' });
  },
  info: (message: string, data?: any) => {
    logger.info(`[DB:INFO] ${message}`, { data, category: 'database' });
  },
  debug: (message: string, data?: any) => {
    logger.debug(`[DB:DEBUG] ${message}`, { data, category: 'database' });
  },
};

export const apiLogger = {
  error: (message: string, data?: any) => {
    logger.error(`[API:ERROR] ${message}`, { data, category: 'api' });
  },
  warn: (message: string, data?: any) => {
    logger.warn(`[API:WARN] ${message}`, { data, category: 'api' });
  },
  info: (message: string, data?: any) => {
    logger.info(`[API:INFO] ${message}`, { data, category: 'api' });
  },
  debug: (message: string, data?: any) => {
    logger.debug(`[API:DEBUG] ${message}`, { data, category: 'api' });
  },
  request: (req: any, res: any) => {
    const { method, originalUrl, ip, body, query, params } = req;
    const statusCode = res.statusCode;

    // Don't log sensitive information
    const sanitizedBody = { ...body };
    if (sanitizedBody.password) sanitizedBody.password = '[REDACTED]';
    if (sanitizedBody.resetPasswordToken) sanitizedBody.resetPasswordToken = '[REDACTED]';
    if (sanitizedBody.twoFactorSecret) sanitizedBody.twoFactorSecret = '[REDACTED]';

    logger.info(`[API:REQUEST] ${method} ${originalUrl} ${statusCode}`, {
      data: {
        method,
        url: originalUrl,
        statusCode,
        ip,
        body: sanitizedBody,
        query,
        params,
      },
      category: 'api',
    });
  },
};

export const authLogger = {
  error: (message: string, data?: any) => {
    logger.error(`[AUTH:ERROR] ${message}`, { data, category: 'auth' });
  },
  warn: (message: string, data?: any) => {
    logger.warn(`[AUTH:WARN] ${message}`, { data, category: 'auth' });
  },
  info: (message: string, data?: any) => {
    logger.info(`[AUTH:INFO] ${message}`, { data, category: 'auth' });
  },
  debug: (message: string, data?: any) => {
    logger.debug(`[AUTH:DEBUG] ${message}`, { data, category: 'auth' });
  },
  login: (user: any, success: boolean, reason?: string) => {
    const message = success
      ? `User ${user.email} logged in successfully`
      : `Failed login attempt for ${user.email}: ${reason || 'Unknown reason'}`;

    if (success) {
      logger.info(`[AUTH:LOGIN] ${message}`, {
        data: { userId: user.id, email: user.email, success },
        category: 'auth',
      });
    } else {
      logger.warn(`[AUTH:LOGIN] ${message}`, {
        data: { email: user.email, success, reason },
        category: 'auth',
      });
    }
  },
};

export const cacheLogger = {
  error: (message: string, data?: any) => {
    logger.error(`[CACHE:ERROR] ${message}`, { data, category: 'cache' });
  },
  warn: (message: string, data?: any) => {
    logger.warn(`[CACHE:WARN] ${message}`, { data, category: 'cache' });
  },
  info: (message: string, data?: any) => {
    logger.info(`[CACHE:INFO] ${message}`, { data, category: 'cache' });
  },
  debug: (message: string, data?: any) => {
    logger.debug(`[CACHE:DEBUG] ${message}`, { data, category: 'cache' });
  },
  hit: (key: string, data?: any) => {
    logger.debug(`[CACHE:HIT] Cache hit for key: ${key}`, { data, category: 'cache' });
  },
  miss: (key: string, data?: any) => {
    logger.debug(`[CACHE:MISS] Cache miss for key: ${key}`, { data, category: 'cache' });
  },
  set: (key: string, ttl: number, data?: any) => {
    logger.debug(`[CACHE:SET] Cache set for key: ${key} with TTL: ${ttl}s`, { data, category: 'cache' });
  },
  invalidate: (key: string, data?: any) => {
    logger.info(`[CACHE:INVALIDATE] Cache invalidated for key: ${key}`, { data, category: 'cache' });
  },
  clear: (data?: any) => {
    logger.info(`[CACHE:CLEAR] Cache cleared`, { data, category: 'cache' });
  },
};

export const emailLogger = {
  error: (message: string, data?: any) => {
    logger.error(`[EMAIL:ERROR] ${message}`, { data, category: 'email' });
  },
  warn: (message: string, data?: any) => {
    logger.warn(`[EMAIL:WARN] ${message}`, { data, category: 'email' });
  },
  info: (message: string, data?: any) => {
    logger.info(`[EMAIL:INFO] ${message}`, { data, category: 'email' });
  },
  debug: (message: string, data?: any) => {
    logger.debug(`[EMAIL:DEBUG] ${message}`, { data, category: 'email' });
  },
  send: (to: string, subject: string, data?: any) => {
    logger.info(`[EMAIL:SEND] Email sent to ${to} with subject: ${subject}`, { data, category: 'email' });
  },
};

export default logger;
