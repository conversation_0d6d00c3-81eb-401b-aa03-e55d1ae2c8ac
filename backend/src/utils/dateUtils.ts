/**
 * Date utility functions for consistent date handling across the application
 */

/**
 * Format options for different date display formats
 */
export enum DateFormat {
  SHORT = 'short',
  MEDIUM = 'medium',
  LONG = 'long',
  FULL = 'full',
  ISO = 'iso'
}

/**
 * Default locale for date formatting
 */
export const DEFAULT_LOCALE = 'fr-FR';

/**
 * Format a date string or Date object to a localized string
 * @param date Date string or Date object
 * @param format Format type
 * @param locale Locale for formatting (default: fr-FR)
 * @returns Formatted date string
 */
export const formatDate = (
  date: string | Date | null | undefined,
  format: DateFormat = DateFormat.MEDIUM,
  locale: string = DEFAULT_LOCALE
): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    console.error(`Invalid date: ${date}`);
    return '';
  }

  switch (format) {
    case DateFormat.SHORT:
      return dateObj.toLocaleDateString(locale, {
        day: 'numeric',
        month: 'numeric',
        year: 'numeric'
      });
    case DateFormat.MEDIUM:
      return dateObj.toLocaleDateString(locale, {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    case DateFormat.LONG:
      return dateObj.toLocaleDateString(locale, {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        weekday: 'long'
      });
    case DateFormat.FULL:
      return dateObj.toLocaleString(locale, {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        weekday: 'long',
        hour: '2-digit',
        minute: '2-digit'
      });
    case DateFormat.ISO:
      return dateObj.toISOString();
    default:
      return dateObj.toLocaleDateString(locale);
  }
};

/**
 * Parse a date string to a Date object
 * @param dateString Date string to parse
 * @returns Date object or null if invalid
 */
export const parseDate = (dateString: string | null | undefined): Date | null => {
  if (!dateString) return null;

  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
};

/**
 * Check if a date is valid
 * @param date Date to check
 * @returns True if date is valid
 */
export const isValidDate = (date: string | Date | null | undefined): boolean => {
  if (!date) return false;
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return !isNaN(dateObj.getTime());
};

/**
 * Check if a date is in the past
 * @param date Date to check
 * @returns True if date is in the past
 */
export const isDatePast = (date: string | Date | null | undefined): boolean => {
  if (!date) return false;
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  
  return dateObj < now;
};

/**
 * Check if a date is today
 * @param date Date to check
 * @returns True if date is today
 */
export const isDateToday = (date: string | Date | null | undefined): boolean => {
  if (!date) return false;
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  
  return (
    dateObj.getDate() === now.getDate() &&
    dateObj.getMonth() === now.getMonth() &&
    dateObj.getFullYear() === now.getFullYear()
  );
};

/**
 * Get the number of days remaining until a date
 * @param date Target date
 * @returns Number of days remaining (negative if date is in the past)
 */
export const getDaysRemaining = (date: string | Date | null | undefined): number => {
  if (!date) return 0;
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  
  // Reset time to midnight for accurate day calculation
  const targetDate = new Date(dateObj);
  targetDate.setHours(0, 0, 0, 0);
  
  const nowDate = new Date(now);
  nowDate.setHours(0, 0, 0, 0);
  
  const diffInMs = targetDate.getTime() - nowDate.getTime();
  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
};

/**
 * Get the current date in ISO format
 * @returns Current date in ISO format
 */
export const getCurrentDateISO = (): string => {
  return new Date().toISOString();
};

export default {
  formatDate,
  parseDate,
  isValidDate,
  isDatePast,
  isDateToday,
  getDaysRemaining,
  getCurrentDateISO,
  DateFormat
};
