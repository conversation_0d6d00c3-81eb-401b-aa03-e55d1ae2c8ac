/**
 * Validation utilities for data validation
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateScholarshipData = (data: any, isUpdate: boolean = false): ValidationResult => {
  const errors: string[] = [];

  // Required fields for creation
  if (!isUpdate) {
    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('Title is required and must be a non-empty string');
    }

    if (!data.description || typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push('Description is required and must be a non-empty string');
    }

    if (!data.deadline) {
      errors.push('Deadline is required');
    }
  }

  // Validate title if provided
  if (data.title !== undefined) {
    if (typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('Title must be a non-empty string');
    } else if (data.title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }
  }

  // Validate description if provided
  if (data.description !== undefined) {
    if (typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push('Description must be a non-empty string');
    }
  }

  // Validate level if provided
  if (data.level !== undefined) {
    const validLevels = ['Bachelor', 'Master', 'PhD', 'Postdoc'];
    if (!validLevels.includes(data.level)) {
      errors.push('Level must be one of: Bachelor, Master, PhD, Postdoc');
    }
  }

  // Validate country if provided
  if (data.country !== undefined) {
    if (typeof data.country !== 'string' || data.country.trim().length === 0) {
      errors.push('Country must be a non-empty string');
    } else if (data.country.length > 100) {
      errors.push('Country must be less than 100 characters');
    }
  }

  // Validate deadline if provided
  if (data.deadline !== undefined) {
    const deadline = new Date(data.deadline);
    if (isNaN(deadline.getTime())) {
      errors.push('Deadline must be a valid date');
    }
  }

  // Validate isOpen if provided
  if (data.isOpen !== undefined && typeof data.isOpen !== 'boolean') {
    errors.push('isOpen must be a boolean');
  }

  // Validate thumbnail if provided
  if (data.thumbnail !== undefined) {
    if (typeof data.thumbnail !== 'string') {
      errors.push('Thumbnail must be a string (URL or base64)');
    } else if (data.thumbnail.length > 1000) {
      errors.push('Thumbnail URL must be less than 1000 characters');
    }
  }

  // Validate URLs if provided
  const urlFields = ['scholarshipLink', 'youtubeLink'];
  urlFields.forEach(field => {
    if (data[field] !== undefined) {
      if (typeof data[field] !== 'string') {
        errors.push(`${field} must be a string`);
      } else if (data[field].length > 500) {
        errors.push(`${field} must be less than 500 characters`);
      } else if (data[field] && !isValidUrl(data[field])) {
        errors.push(`${field} must be a valid URL`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateGuideData = (data: any, isUpdate: boolean = false): ValidationResult => {
  const errors: string[] = [];

  // Required fields for creation
  if (!isUpdate) {
    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('Title is required and must be a non-empty string');
    }

    if (!data.content || typeof data.content !== 'string' || data.content.trim().length === 0) {
      errors.push('Content is required and must be a non-empty string');
    }

    if (!data.category || typeof data.category !== 'string') {
      errors.push('Category is required');
    }
  }

  // Validate title if provided
  if (data.title !== undefined) {
    if (typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('Title must be a non-empty string');
    } else if (data.title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }
  }

  // Validate content if provided
  if (data.content !== undefined) {
    if (typeof data.content !== 'string' || data.content.trim().length === 0) {
      errors.push('Content must be a non-empty string');
    }
  }

  // Validate category if provided
  if (data.category !== undefined) {
    const validCategories = ['application', 'documents', 'preparation', 'tips'];
    if (!validCategories.includes(data.category)) {
      errors.push('Category must be one of: application, documents, preparation, tips');
    }
  }

  // Validate slug if provided
  if (data.slug !== undefined) {
    if (typeof data.slug !== 'string' || data.slug.trim().length === 0) {
      errors.push('Slug must be a non-empty string');
    } else if (data.slug.length > 255) {
      errors.push('Slug must be less than 255 characters');
    } else if (!/^[a-z0-9-]+$/.test(data.slug)) {
      errors.push('Slug must contain only lowercase letters, numbers, and hyphens');
    }
  }

  // Validate excerpt if provided
  if (data.excerpt !== undefined && data.excerpt !== null) {
    if (typeof data.excerpt !== 'string') {
      errors.push('Excerpt must be a string');
    } else if (data.excerpt.length > 500) {
      errors.push('Excerpt must be less than 500 characters');
    }
  }

  // Validate isPublished if provided
  if (data.isPublished !== undefined && typeof data.isPublished !== 'boolean') {
    errors.push('isPublished must be a boolean');
  }

  // Validate readTime if provided
  if (data.readTime !== undefined) {
    if (!Number.isInteger(data.readTime) || data.readTime < 1) {
      errors.push('readTime must be a positive integer');
    }
  }

  // Validate tags if provided
  if (data.tags !== undefined) {
    if (!Array.isArray(data.tags)) {
      errors.push('Tags must be an array');
    } else {
      data.tags.forEach((tag: any, index: number) => {
        if (typeof tag !== 'string') {
          errors.push(`Tag at index ${index} must be a string`);
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateOpportunityData = (data: any, isUpdate: boolean = false): ValidationResult => {
  const errors: string[] = [];

  // Required fields for creation
  if (!isUpdate) {
    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('Title is required and must be a non-empty string');
    }

    if (!data.description || typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push('Description is required and must be a non-empty string');
    }

    if (!data.type || typeof data.type !== 'string') {
      errors.push('Type is required');
    }

    if (!data.organization || typeof data.organization !== 'string' || data.organization.trim().length === 0) {
      errors.push('Organization is required and must be a non-empty string');
    }

    if (!data.location || typeof data.location !== 'string' || data.location.trim().length === 0) {
      errors.push('Location is required and must be a non-empty string');
    }

    if (!data.deadline) {
      errors.push('Deadline is required');
    }
  }

  // Validate title if provided
  if (data.title !== undefined) {
    if (typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('Title must be a non-empty string');
    } else if (data.title.length > 255) {
      errors.push('Title must be less than 255 characters');
    }
  }

  // Validate description if provided
  if (data.description !== undefined) {
    if (typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push('Description must be a non-empty string');
    }
  }

  // Validate type if provided
  if (data.type !== undefined) {
    const validTypes = ['internship', 'training', 'conference', 'workshop', 'competition'];
    if (!validTypes.includes(data.type)) {
      errors.push('Type must be one of: internship, training, conference, workshop, competition');
    }
  }

  // Validate organization if provided
  if (data.organization !== undefined) {
    if (typeof data.organization !== 'string' || data.organization.trim().length === 0) {
      errors.push('Organization must be a non-empty string');
    } else if (data.organization.length > 255) {
      errors.push('Organization must be less than 255 characters');
    }
  }

  // Validate location if provided
  if (data.location !== undefined) {
    if (typeof data.location !== 'string' || data.location.trim().length === 0) {
      errors.push('Location must be a non-empty string');
    } else if (data.location.length > 255) {
      errors.push('Location must be less than 255 characters');
    }
  }

  // Validate isRemote if provided
  if (data.isRemote !== undefined && typeof data.isRemote !== 'boolean') {
    errors.push('isRemote must be a boolean');
  }

  // Validate deadline if provided
  if (data.deadline !== undefined) {
    const deadline = new Date(data.deadline);
    if (isNaN(deadline.getTime())) {
      errors.push('Deadline must be a valid date');
    }
  }

  // Validate dates if provided
  const dateFields = ['startDate', 'endDate'];
  dateFields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      const date = new Date(data[field]);
      if (isNaN(date.getTime())) {
        errors.push(`${field} must be a valid date`);
      }
    }
  });

  // Validate isActive if provided
  if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {
    errors.push('isActive must be a boolean');
  }

  // Validate URLs if provided
  const urlFields = ['applicationLink', 'website'];
  urlFields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      if (typeof data[field] !== 'string') {
        errors.push(`${field} must be a string`);
      } else if (data[field].length > 500) {
        errors.push(`${field} must be less than 500 characters`);
      } else if (data[field] && !isValidUrl(data[field])) {
        errors.push(`${field} must be a valid URL`);
      }
    }
  });

  // Validate email if provided
  if (data.contactEmail !== undefined && data.contactEmail !== null) {
    if (typeof data.contactEmail !== 'string') {
      errors.push('contactEmail must be a string');
    } else if (data.contactEmail && !isValidEmail(data.contactEmail)) {
      errors.push('contactEmail must be a valid email address');
    }
  }

  // Validate tags if provided
  if (data.tags !== undefined) {
    if (!Array.isArray(data.tags)) {
      errors.push('Tags must be an array');
    } else {
      data.tags.forEach((tag: any, index: number) => {
        if (typeof tag !== 'string') {
          errors.push(`Tag at index ${index} must be a string`);
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Helper functions
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
