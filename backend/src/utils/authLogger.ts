/**
 * Authentication Logger
 * 
 * Specialized logger for authentication-related events with enhanced security monitoring
 */

import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logDir = path.join(__dirname, '../../logs/auth');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Define log formats
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: [AUTH] ${info.message}${info.data ? ' ' + JSON.stringify(info.data) : ''}`
  )
);

const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.json()
);

// Create the auth logger
const authLogger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  defaultMeta: { service: 'mabourse-auth' },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: consoleFormat,
    }),
    // Auth log file transport
    new winston.transports.File({
      filename: path.join(logDir, 'auth.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 10,
    }),
    // Security events log file transport
    new winston.transports.File({
      filename: path.join(logDir, 'security.log'),
      level: 'warn',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 10,
    }),
  ],
});

// Define auth event types
export enum AuthEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGOUT = 'LOGOUT',
  PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',
  PASSWORD_RESET_SUCCESS = 'PASSWORD_RESET_SUCCESS',
  PASSWORD_RESET_FAILURE = 'PASSWORD_RESET_FAILURE',
  PASSWORD_RESET = 'PASSWORD_RESET',
  PASSWORD_RESET_FAILED = 'PASSWORD_RESET_FAILED',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  PASSWORD_CHANGE_REQUIRED = 'PASSWORD_CHANGE_REQUIRED',
  PASSWORD_CHANGE_FAILED = 'PASSWORD_CHANGE_FAILED',
  PASSWORD_EXPIRED = 'PASSWORD_EXPIRED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',
  ADMIN_CREATED = 'ADMIN_CREATED',
  ADMIN_CREATION_FAILED = 'ADMIN_CREATION_FAILED',
  ADMIN_SETUP_FAILED = 'ADMIN_SETUP_FAILED',
  TWO_FACTOR_ENABLED = 'TWO_FACTOR_ENABLED',
  TWO_FACTOR_DISABLED = 'TWO_FACTOR_DISABLED',
  TWO_FACTOR_SUCCESS = 'TWO_FACTOR_SUCCESS',
  TWO_FACTOR_FAILURE = 'TWO_FACTOR_FAILURE',
  TWO_FACTOR_SETUP_INITIATED = 'TWO_FACTOR_SETUP_INITIATED',
  TWO_FACTOR_VERIFIED = 'TWO_FACTOR_VERIFIED',
  TWO_FACTOR_VERIFICATION_FAILED = 'TWO_FACTOR_VERIFICATION_FAILED',
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
}

// Define security levels
export enum SecurityLevel {
  INFO = 'info',
  WARN = 'warn',
  WARNING = 'warn',
  ERROR = 'error',
  ALERT = 'alert',
  CRITICAL = 'critical',
}

// Define log functions
export const logAuthEvent = (
  eventType: AuthEventType,
  message: string,
  data?: any,
  securityLevel: SecurityLevel = SecurityLevel.INFO
) => {
  // Sanitize sensitive data
  const sanitizedData = { ...data };
  if (sanitizedData?.password) sanitizedData.password = '[REDACTED]';
  if (sanitizedData?.token) sanitizedData.token = '[REDACTED]';
  if (sanitizedData?.resetToken) sanitizedData.resetToken = '[REDACTED]';
  if (sanitizedData?.twoFactorSecret) sanitizedData.twoFactorSecret = '[REDACTED]';

  // Add event type to data
  sanitizedData.eventType = eventType;

  // Log based on security level
  switch (securityLevel) {
    case SecurityLevel.INFO:
      authLogger.info(message, { data: sanitizedData });
      break;
    case SecurityLevel.WARN:
      authLogger.warn(message, { data: sanitizedData });
      break;
    case SecurityLevel.ALERT:
      authLogger.error(message, { data: sanitizedData });
      break;
    case SecurityLevel.CRITICAL:
      authLogger.error(`[CRITICAL] ${message}`, { data: sanitizedData });
      // Could trigger additional alerts here (email, SMS, etc.)
      break;
  }
};

// Specialized logging functions
export const logLoginSuccess = (userId: number, email: string, role: string, ip: string, userAgent?: string) => {
  logAuthEvent(
    AuthEventType.LOGIN_SUCCESS,
    `User logged in successfully: ${email}`,
    { userId, email, role, ip, userAgent },
    SecurityLevel.INFO
  );
};

export const logLoginFailure = (
  email: string,
  reason: string,
  ip: string,
  failedAttempts: number,
  userAgent?: string
) => {
  const securityLevel = failedAttempts >= 3 ? SecurityLevel.WARN : SecurityLevel.INFO;
  
  logAuthEvent(
    AuthEventType.LOGIN_FAILURE,
    `Login failed for ${email}: ${reason}`,
    { email, reason, ip, failedAttempts, userAgent },
    securityLevel
  );
};

export const logAccountLocked = (userId: number, email: string, ip: string) => {
  logAuthEvent(
    AuthEventType.ACCOUNT_LOCKED,
    `Account locked: ${email}`,
    { userId, email, ip },
    SecurityLevel.ALERT
  );
};

export const logSuspiciousActivity = (
  userId: number | null,
  email: string | null,
  activity: string,
  ip: string,
  details?: any
) => {
  logAuthEvent(
    AuthEventType.SUSPICIOUS_ACTIVITY,
    `Suspicious activity detected: ${activity}`,
    { userId, email, activity, ip, details },
    SecurityLevel.ALERT
  );
};

export default {
  logAuthEvent,
  logLoginSuccess,
  logLoginFailure,
  logAccountLocked,
  logSuspiciousActivity,
  AuthEventType,
  SecurityLevel,
};
