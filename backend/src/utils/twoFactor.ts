import { authenticator } from 'otplib';
import crypto from 'crypto';
import QRCode from 'qrcode';

// Configure authenticator
authenticator.options = {
  window: 1, // Allow 1 step before and after current step (30 seconds window)
};

// Generate a new secret key
export const generateSecret = (email: string): string => {
  return authenticator.generateSecret();
};

// Generate a TOTP token
export const generateToken = (secret: string): string => {
  return authenticator.generate(secret);
};

// Verify a TOTP token
export const verifyToken = (token: string, secret: string): boolean => {
  try {
    return authenticator.verify({ token, secret });
  } catch (error) {
    console.error('Error verifying token:', error);
    return false;
  }
};

// Generate a QR code for the secret
export const generateQRCode = async (
  secret: string,
  email: string,
  issuer: string = 'MaBourse Admin'
): Promise<string> => {
  try {
    const otpauth = authenticator.keyuri(email, issuer, secret);
    return await QRCode.toDataURL(otpauth);
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
};

// Generate backup codes
export const generateBackupCodes = (count: number = 10): string[] => {
  const codes: string[] = [];
  for (let i = 0; i < count; i++) {
    // Generate a random 8-character code
    const code = crypto.randomBytes(4).toString('hex').toUpperCase();
    codes.push(code);
  }
  return codes;
};

// Verify a backup code
export const verifyBackupCode = (
  code: string,
  backupCodes: string[]
): { valid: boolean; remainingCodes: string[] } => {
  const normalizedCode = code.trim().toUpperCase();
  const codeIndex = backupCodes.indexOf(normalizedCode);
  
  if (codeIndex === -1) {
    return { valid: false, remainingCodes: backupCodes };
  }
  
  // Remove the used code
  const remainingCodes = [...backupCodes];
  remainingCodes.splice(codeIndex, 1);
  
  return { valid: true, remainingCodes };
};
