import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Utility function to clean up duplicate admin accounts
 */
export async function cleanupDuplicateAdmins() {
  try {
    console.log('Checking for duplicate admin accounts...');

    // No need to check for Sequelize admins anymore

    // Check for duplicate main admins in Prisma
    const prismaMainAdmins = await prisma.admin.findMany({ where: { isMainAdmin: true } });

    if (prismaMainAdmins.length > 1) {
      console.log(`Found ${prismaMainAdmins.length} main admins in Prisma. Keeping only the first one...`);

      // Keep the first main admin and remove the rest
      const adminToKeep = prismaMainAdmins[0];

      // Remove other main admins
      for (let i = 1; i < prismaMainAdmins.length; i++) {
        const adminToRemove = prismaMainAdmins[i];
        await prisma.admin.delete({ where: { id: adminToRemove.id } });
      }

      console.log('Duplicate Prisma main admins removed.');
    }

    // Check if the main admin in Prisma has a properly hashed password
    if (prismaMainAdmins.length > 0) {
      const mainAdmin = prismaMainAdmins[0];

      // Check if the password is not hashed (doesn't start with $2a$ or $2b$)
      if (!mainAdmin.password.startsWith('$2a$') && !mainAdmin.password.startsWith('$2b$')) {
        console.log('Main admin password is not properly hashed. Fixing...');
        const hashedPassword = await require('bcryptjs').hash('admin123', 10);
        await prisma.admin.update({
          where: { id: mainAdmin.id },
          data: { password: hashedPassword }
        });
        console.log('Main admin password fixed successfully.');
      }
    }

    console.log('Admin cleanup completed.');
    return true;
  } catch (error) {
    console.error('Error cleaning up duplicate admins:', error);
    return false;
  }
}

/**
 * Utility function to clean up duplicate scholarships
 */
export async function cleanupDuplicateScholarships() {
  try {
    console.log('Checking for duplicate scholarships...');

    // Check for duplicate scholarships in Prisma by title
    const scholarships = await prisma.scholarship.findMany();

    // Group scholarships by title
    const scholarshipsByTitle: Record<string, any[]> = {};
    scholarships.forEach(scholarship => {
      if (!scholarshipsByTitle[scholarship.title]) {
        scholarshipsByTitle[scholarship.title] = [];
      }
      scholarshipsByTitle[scholarship.title].push(scholarship);
    });

    // Find titles with duplicates
    const duplicateTitles = Object.keys(scholarshipsByTitle).filter(title =>
      scholarshipsByTitle[title].length > 1
    );

    if (duplicateTitles.length > 0) {
      console.log(`Found ${duplicateTitles.length} scholarship titles with duplicates.`);

      // Process each duplicate title
      for (const title of duplicateTitles) {
        const duplicates = scholarshipsByTitle[title];

        // Keep the newest scholarship and remove the rest
        duplicates.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        // Remove duplicates (keep the first/newest one)
        for (let i = 1; i < duplicates.length; i++) {
          await prisma.scholarship.delete({ where: { id: duplicates[i].id } });
        }
      }

      console.log('Duplicate scholarships removed.');
    }

    console.log('Scholarship cleanup completed.');
    return true;
  } catch (error) {
    console.error('Error cleaning up duplicate scholarships:', error);
    return false;
  }
}

/**
 * Utility function to clean up duplicate messages
 */
export async function cleanupDuplicateMessages() {
  try {
    console.log('Checking for duplicate messages...');

    // Check for duplicate messages in Prisma by content and email
    const messages = await prisma.message.findMany();

    // Group messages by content and email
    const messagesByContentAndEmail: Record<string, any[]> = {};
    messages.forEach(message => {
      const key = `${message.email}:${message.content}`;
      if (!messagesByContentAndEmail[key]) {
        messagesByContentAndEmail[key] = [];
      }
      messagesByContentAndEmail[key].push(message);
    });

    // Find keys with duplicates
    const duplicateKeys = Object.keys(messagesByContentAndEmail).filter(key =>
      messagesByContentAndEmail[key].length > 1
    );

    if (duplicateKeys.length > 0) {
      console.log(`Found ${duplicateKeys.length} message combinations with duplicates.`);

      // Process each duplicate key
      for (const key of duplicateKeys) {
        const duplicates = messagesByContentAndEmail[key];

        // Keep the newest message and remove the rest
        duplicates.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        // Remove duplicates (keep the first/newest one)
        for (let i = 1; i < duplicates.length; i++) {
          await prisma.message.delete({ where: { id: duplicates[i].id } });
        }
      }

      console.log('Duplicate messages removed.');
    }

    console.log('Message cleanup completed.');
    return true;
  } catch (error) {
    console.error('Error cleaning up duplicate messages:', error);
    return false;
  }
}

/**
 * Main cleanup function to run on server startup
 */
export async function performDataCleanup() {
  console.log('Starting data cleanup process...');

  await cleanupDuplicateAdmins();
  await cleanupDuplicateScholarships();
  await cleanupDuplicateMessages();

  console.log('Data cleanup process completed.');
}
