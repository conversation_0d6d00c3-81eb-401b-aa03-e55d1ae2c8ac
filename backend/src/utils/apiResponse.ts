import { Response } from 'express';

/**
 * Standard API response format
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
  timestamp: string;
  statusCode: number;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Paginated API response format
 */
export interface PaginatedApiResponse<T = any> extends ApiResponse<T[]> {
  pagination: PaginationMeta;
}

/**
 * Send a success response
 * @param res Express response object
 * @param data Response data
 * @param message Success message
 * @param statusCode HTTP status code (default: 200)
 */
export const sendSuccess = <T>(
  res: Response,
  data: T,
  message: string = 'Success',
  statusCode: number = 200
): Response => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString(),
    statusCode
  };

  return res.status(statusCode).json(response);
};

/**
 * Send a paginated success response
 * @param res Express response object
 * @param data Array of items
 * @param pagination Pagination metadata
 * @param message Success message
 * @param statusCode HTTP status code (default: 200)
 */
export const sendPaginatedSuccess = <T>(
  res: Response,
  data: T[],
  pagination: PaginationMeta,
  message: string = 'Success',
  statusCode: number = 200
): Response => {
  const response: PaginatedApiResponse<T> = {
    success: true,
    message,
    data,
    pagination,
    timestamp: new Date().toISOString(),
    statusCode
  };

  return res.status(statusCode).json(response);
};

/**
 * Send an error response
 * @param res Express response object
 * @param message Error message
 * @param error Error details (only included in development)
 * @param statusCode HTTP status code (default: 400)
 */
export const sendError = (
  res: Response,
  message: string = 'An error occurred',
  error: any = null,
  statusCode: number = 400
): Response => {
  const response: ApiResponse = {
    success: false,
    message,
    error: process.env.NODE_ENV === 'development' ? error : undefined,
    timestamp: new Date().toISOString(),
    statusCode
  };

  return res.status(statusCode).json(response);
};

/**
 * Send a not found response
 * @param res Express response object
 * @param message Not found message
 */
export const sendNotFound = (
  res: Response,
  message: string = 'Resource not found'
): Response => {
  return sendError(res, message, null, 404);
};

/**
 * Send an unauthorized response
 * @param res Express response object
 * @param message Unauthorized message
 */
export const sendUnauthorized = (
  res: Response,
  message: string = 'Unauthorized'
): Response => {
  return sendError(res, message, null, 401);
};

/**
 * Send a forbidden response
 * @param res Express response object
 * @param message Forbidden message
 */
export const sendForbidden = (
  res: Response,
  message: string = 'Forbidden'
): Response => {
  return sendError(res, message, null, 403);
};

/**
 * Send a validation error response
 * @param res Express response object
 * @param errors Validation errors
 * @param message Validation error message
 */
export const sendValidationError = (
  res: Response,
  errors: any,
  message: string = 'Validation error'
): Response => {
  return sendError(res, message, errors, 422);
};

export default {
  sendSuccess,
  sendPaginatedSuccess,
  sendError,
  sendNotFound,
  sendUnauthorized,
  sendForbidden,
  sendValidationError
};
