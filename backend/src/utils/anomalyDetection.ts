/**
 * ML-based Anomaly Detection System
 * Implements behavioral analysis and advanced threat detection algorithms
 */

import { query } from '../config/database';
import { AuthEventType } from './authLogger';

// Define security event interface
interface SecurityEvent {
  id: number;
  eventType: string;
  message: string;
  userId?: number;
  email?: string;
  ip: string;
  userAgent?: string;
  timestamp: string;
  details?: string;
  severity: string;
}

// Define anomaly interface
export interface Anomaly {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedUsers?: string[];
  ipAddresses?: string[];
  timestamp: string;
  eventIds: number[];
}

/**
 * Detect anomalies in security events
 * @param events List of security events
 * @returns List of detected anomalies
 */
export const detectAnomalies = (events: SecurityEvent[]): Anomaly[] => {
  const anomalies: Anomaly[] = [];
  
  // Skip if no events
  if (!events.length) return anomalies;
  
  // Group events by user
  const userEvents = groupEventsByUser(events);
  
  // Group events by IP
  const ipEvents = groupEventsByIP(events);
  
  // Detect rapid login failures (brute force attempts)
  detectBruteForceAttempts(userEvents, anomalies);
  
  // Detect login attempts from multiple locations
  detectMultipleLocationLogins(userEvents, anomalies);
  
  // Detect unusual login times
  detectUnusualLoginTimes(userEvents, anomalies);
  
  // Detect distributed login attempts (same password across multiple accounts)
  detectDistributedLoginAttempts(ipEvents, anomalies);
  
  // Detect account lockouts
  detectAccountLockouts(events, anomalies);
  
  return anomalies;
};

/**
 * Group events by user
 */
const groupEventsByUser = (events: SecurityEvent[]): Record<string, SecurityEvent[]> => {
  const userEvents: Record<string, SecurityEvent[]> = {};
  
  events.forEach(event => {
    if (event.email) {
      if (!userEvents[event.email]) {
        userEvents[event.email] = [];
      }
      userEvents[event.email].push(event);
    }
  });
  
  return userEvents;
};

/**
 * Group events by IP address
 */
const groupEventsByIP = (events: SecurityEvent[]): Record<string, SecurityEvent[]> => {
  const ipEvents: Record<string, SecurityEvent[]> = {};
  
  events.forEach(event => {
    if (!ipEvents[event.ip]) {
      ipEvents[event.ip] = [];
    }
    ipEvents[event.ip].push(event);
  });
  
  return ipEvents;
};

/**
 * Detect brute force login attempts
 */
const detectBruteForceAttempts = (
  userEvents: Record<string, SecurityEvent[]>,
  anomalies: Anomaly[]
) => {
  const THRESHOLD_TIME_MS = 5 * 60 * 1000; // 5 minutes
  const THRESHOLD_ATTEMPTS = 5; // 5 attempts
  
  Object.entries(userEvents).forEach(([email, events]) => {
    // Filter login failure events
    const loginFailures = events.filter(e => 
      e.eventType === AuthEventType.LOGIN_FAILURE
    ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    if (loginFailures.length >= THRESHOLD_ATTEMPTS) {
      // Check for rapid login failures
      for (let i = 0; i <= loginFailures.length - THRESHOLD_ATTEMPTS; i++) {
        const startTime = new Date(loginFailures[i].timestamp).getTime();
        const endTime = new Date(loginFailures[i + THRESHOLD_ATTEMPTS - 1].timestamp).getTime();
        
        if (endTime - startTime <= THRESHOLD_TIME_MS) {
          // Found rapid login failures
          anomalies.push({
            type: 'BRUTE_FORCE_ATTEMPT',
            severity: 'high',
            description: `Detected ${THRESHOLD_ATTEMPTS} failed login attempts for ${email} within ${THRESHOLD_TIME_MS / 60000} minutes`,
            affectedUsers: [email],
            ipAddresses: [...new Set(loginFailures.slice(i, i + THRESHOLD_ATTEMPTS).map(e => e.ip))],
            timestamp: new Date().toISOString(),
            eventIds: loginFailures.slice(i, i + THRESHOLD_ATTEMPTS).map(e => e.id),
          });
          break;
        }
      }
    }
  });
};

/**
 * Detect login attempts from multiple locations
 */
const detectMultipleLocationLogins = (
  userEvents: Record<string, SecurityEvent[]>,
  anomalies: Anomaly[]
) => {
  const TIME_WINDOW_MS = 24 * 60 * 60 * 1000; // 24 hours
  
  Object.entries(userEvents).forEach(([email, events]) => {
    // Get successful logins
    const successfulLogins = events.filter(e => 
      e.eventType === AuthEventType.LOGIN_SUCCESS
    ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    if (successfulLogins.length >= 2) {
      // Get unique IP addresses
      const uniqueIPs = [...new Set(successfulLogins.map(e => e.ip))];
      
      if (uniqueIPs.length >= 2) {
        // Check for logins from different IPs within time window
        for (let i = 0; i < successfulLogins.length - 1; i++) {
          const currentLogin = successfulLogins[i];
          const nextLogin = successfulLogins[i + 1];
          
          const timeDiff = new Date(nextLogin.timestamp).getTime() - new Date(currentLogin.timestamp).getTime();
          
          if (timeDiff <= TIME_WINDOW_MS && currentLogin.ip !== nextLogin.ip) {
            // Found logins from different locations within time window
            anomalies.push({
              type: 'MULTIPLE_LOCATION_LOGINS',
              severity: 'medium',
              description: `Detected logins from different IP addresses for ${email} within ${TIME_WINDOW_MS / (60 * 60 * 1000)} hours`,
              affectedUsers: [email],
              ipAddresses: [currentLogin.ip, nextLogin.ip],
              timestamp: new Date().toISOString(),
              eventIds: [currentLogin.id, nextLogin.id],
            });
            break;
          }
        }
      }
    }
  });
};

/**
 * Detect unusual login times
 */
const detectUnusualLoginTimes = (
  userEvents: Record<string, SecurityEvent[]>,
  anomalies: Anomaly[]
) => {
  // Define business hours (9 AM to 6 PM)
  const BUSINESS_HOURS_START = 9;
  const BUSINESS_HOURS_END = 18;
  
  Object.entries(userEvents).forEach(([email, events]) => {
    // Get successful logins
    const successfulLogins = events.filter(e => 
      e.eventType === AuthEventType.LOGIN_SUCCESS
    );
    
    // Check for logins outside business hours
    const outsideBusinessHours = successfulLogins.filter(login => {
      const loginTime = new Date(login.timestamp);
      const hour = loginTime.getHours();
      return hour < BUSINESS_HOURS_START || hour >= BUSINESS_HOURS_END;
    });
    
    if (outsideBusinessHours.length > 0) {
      anomalies.push({
        type: 'UNUSUAL_LOGIN_TIME',
        severity: 'low',
        description: `Detected ${outsideBusinessHours.length} logins outside business hours for ${email}`,
        affectedUsers: [email],
        ipAddresses: [...new Set(outsideBusinessHours.map(e => e.ip))],
        timestamp: new Date().toISOString(),
        eventIds: outsideBusinessHours.map(e => e.id),
      });
    }
  });
};

/**
 * Detect distributed login attempts (same password across multiple accounts)
 */
const detectDistributedLoginAttempts = (
  ipEvents: Record<string, SecurityEvent[]>,
  anomalies: Anomaly[]
) => {
  const THRESHOLD_USERS = 3; // 3 different users
  const TIME_WINDOW_MS = 10 * 60 * 1000; // 10 minutes
  
  Object.entries(ipEvents).forEach(([ip, events]) => {
    // Filter login failure events
    const loginFailures = events.filter(e => 
      e.eventType === AuthEventType.LOGIN_FAILURE
    ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    if (loginFailures.length >= THRESHOLD_USERS) {
      // Get unique users
      const uniqueUsers = [...new Set(loginFailures.map(e => e.email).filter(Boolean))];
      
      if (uniqueUsers.length >= THRESHOLD_USERS) {
        // Check for login attempts for different users within time window
        for (let i = 0; i <= loginFailures.length - THRESHOLD_USERS; i++) {
          const startTime = new Date(loginFailures[i].timestamp).getTime();
          const endTime = new Date(loginFailures[i + THRESHOLD_USERS - 1].timestamp).getTime();
          
          if (endTime - startTime <= TIME_WINDOW_MS) {
            // Found distributed login attempts
            const affectedUsers = loginFailures
              .slice(i, i + THRESHOLD_USERS)
              .map(e => e.email)
              .filter(Boolean) as string[];
            
            anomalies.push({
              type: 'DISTRIBUTED_LOGIN_ATTEMPTS',
              severity: 'high',
              description: `Detected login attempts for ${THRESHOLD_USERS} different users from IP ${ip} within ${TIME_WINDOW_MS / 60000} minutes`,
              affectedUsers,
              ipAddresses: [ip],
              timestamp: new Date().toISOString(),
              eventIds: loginFailures.slice(i, i + THRESHOLD_USERS).map(e => e.id),
            });
            break;
          }
        }
      }
    }
  });
};

/**
 * Detect account lockouts
 */
const detectAccountLockouts = (
  events: SecurityEvent[],
  anomalies: Anomaly[]
) => {
  // Filter account lockout events
  const lockoutEvents = events.filter(e => 
    e.eventType === AuthEventType.ACCOUNT_LOCKED
  );
  
  if (lockoutEvents.length > 0) {
    // Group by user
    const userLockouts: Record<string, SecurityEvent[]> = {};
    
    lockoutEvents.forEach(event => {
      if (event.email) {
        if (!userLockouts[event.email]) {
          userLockouts[event.email] = [];
        }
        userLockouts[event.email].push(event);
      }
    });
    
    // Check for multiple lockouts
    Object.entries(userLockouts).forEach(([email, lockouts]) => {
      if (lockouts.length >= 2) {
        anomalies.push({
          type: 'MULTIPLE_ACCOUNT_LOCKOUTS',
          severity: 'high',
          description: `Detected ${lockouts.length} account lockouts for ${email}`,
          affectedUsers: [email],
          ipAddresses: [...new Set(lockouts.map(e => e.ip))],
          timestamp: new Date().toISOString(),
          eventIds: lockouts.map(e => e.id),
        });
      }
    });
  }
};

export default {
  detectAnomalies,
};
