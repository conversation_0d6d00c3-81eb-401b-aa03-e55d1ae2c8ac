/**
 * Environment variable validation utility
 * 
 * This utility validates that all required environment variables are set
 * and logs warnings for missing optional variables.
 */
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables based on NODE_ENV
const loadEnv = () => {
  const NODE_ENV = process.env.NODE_ENV || 'development';
  const envPath = path.resolve(process.cwd(), `.env.${NODE_ENV}`);
  
  // Check if environment-specific file exists
  if (fs.existsSync(envPath)) {
    console.log(`Loading environment variables from ${envPath}`);
    dotenv.config({ path: envPath });
  } else {
    // Fall back to default .env file
    console.log('Loading environment variables from .env');
    dotenv.config();
  }
};

// Required environment variables
const requiredVariables = [
  'PORT',
  'JWT_SECRET',
  'DATABASE_URL',
];

// Optional environment variables with default values
const optionalVariables: Record<string, string> = {
  'NODE_ENV': 'development',
  'JWT_EXPIRATION': '1d',
  'REFRESH_TOKEN_SECRET': 'mabourse-refresh-token-secret',
  'REFRESH_TOKEN_EXPIRATION': '7d',
  'CORS_ORIGIN': 'http://localhost:3000',
  'FRONTEND_URL': 'http://localhost:3000',
  'LOG_LEVEL': 'info',
  'RATE_LIMIT_WINDOW_MS': '60000',
  'RATE_LIMIT_MAX_REQUESTS': '100',
};

/**
 * Validates environment variables and logs warnings/errors
 * @returns {boolean} True if all required variables are set, false otherwise
 */
export const validateEnv = (): boolean => {
  // Load environment variables first
  loadEnv();
  
  let isValid = true;
  const missingVars: string[] = [];

  // Check required variables
  requiredVariables.forEach(varName => {
    if (!process.env[varName]) {
      console.error(`❌ Required environment variable ${varName} is not set`);
      missingVars.push(varName);
      isValid = false;
    }
  });

  // Check optional variables and set defaults if needed
  Object.entries(optionalVariables).forEach(([varName, defaultValue]) => {
    if (!process.env[varName]) {
      console.warn(`⚠️ Optional environment variable ${varName} is not set, using default: ${defaultValue}`);
      process.env[varName] = defaultValue;
    }
  });

  // Log environment mode
  console.info(`🌍 Environment: ${process.env.NODE_ENV}`);
  
  if (!isValid) {
    console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
    console.error('Please check your .env file or environment configuration');
  } else {
    console.info('✅ Environment validation passed');
  }

  return isValid;
};

/**
 * Gets an environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {string} fallback - The fallback value if the variable is not set
 * @returns {string} The environment variable value or fallback
 */
export const getEnv = (name: string, fallback: string): string => {
  return process.env[name] || fallback;
};

/**
 * Gets a boolean environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {boolean} fallback - The fallback value if the variable is not set
 * @returns {boolean} The environment variable value as boolean or fallback
 */
export const getBoolEnv = (name: string, fallback: boolean): boolean => {
  const value = process.env[name];
  if (value === undefined) return fallback;
  return value.toLowerCase() === 'true';
};

/**
 * Gets a number environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {number} fallback - The fallback value if the variable is not set
 * @returns {number} The environment variable value as number or fallback
 */
export const getNumEnv = (name: string, fallback: number): number => {
  const value = process.env[name];
  if (value === undefined) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
};

export default validateEnv;
