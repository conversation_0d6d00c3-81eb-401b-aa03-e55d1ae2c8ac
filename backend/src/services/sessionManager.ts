/**
 * Professional Session Management Service
 * Industry-standard session handling with security features
 */

import { query } from '../config/database';

export interface SessionData {
  id: string;
  adminId: number;
  jti: string; // JWT ID
  ip: string;
  userAgent: string;
  isActive: boolean;
  createdAt: Date;
  lastActivity: Date;
  expiresAt: Date;
}

export class SessionManager {
  private static readonly MAX_CONCURRENT_SESSIONS = 3;
  private static readonly SESSION_TIMEOUT = 2 * 60 * 60 * 1000; // 2 hours

  /**
   * Create a new session record
   */
  static async createSession(
    adminId: number,
    jti: string,
    ip: string,
    userAgent: string
  ): Promise<SessionData> {
    // Clean up expired sessions first
    await this.cleanupExpiredSessions();

    // Check for concurrent session limit
    await this.enforceConcurrentSessionLimit(adminId);

    const sessionId = `session-${adminId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.SESSION_TIMEOUT);

    const result = await query(`
      INSERT INTO admin_sessions (
        id, admin_id, jti, ip, user_agent, is_active, 
        created_at, last_activity, expires_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [sessionId, adminId, jti, ip, userAgent, true, now, now, expiresAt]);

    return result.rows[0];
  }

  /**
   * Update session activity
   */
  static async updateSessionActivity(jti: string): Promise<boolean> {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.SESSION_TIMEOUT);

    const result = await query(`
      UPDATE admin_sessions 
      SET last_activity = $1, expires_at = $2
      WHERE jti = $3 AND is_active = true AND expires_at > NOW()
      RETURNING id
    `, [now, expiresAt, jti]);

    return result.rows.length > 0;
  }

  /**
   * Invalidate a specific session
   */
  static async invalidateSession(jti: string): Promise<boolean> {
    const result = await query(`
      UPDATE admin_sessions 
      SET is_active = false
      WHERE jti = $1
      RETURNING id
    `, [jti]);

    return result.rows.length > 0;
  }

  /**
   * Invalidate all sessions for an admin
   */
  static async invalidateAllSessions(adminId: number): Promise<number> {
    const result = await query(`
      UPDATE admin_sessions 
      SET is_active = false
      WHERE admin_id = $1 AND is_active = true
      RETURNING id
    `, [adminId]);

    return result.rows.length;
  }

  /**
   * Check if session is valid
   */
  static async isSessionValid(jti: string): Promise<boolean> {
    const result = await query(`
      SELECT id FROM admin_sessions 
      WHERE jti = $1 AND is_active = true AND expires_at > NOW()
    `, [jti]);

    return result.rows.length > 0;
  }

  /**
   * Get active sessions for an admin
   */
  static async getActiveSessions(adminId: number): Promise<SessionData[]> {
    const result = await query(`
      SELECT * FROM admin_sessions 
      WHERE admin_id = $1 AND is_active = true AND expires_at > NOW()
      ORDER BY last_activity DESC
    `, [adminId]);

    return result.rows;
  }

  /**
   * Enforce concurrent session limit
   */
  private static async enforceConcurrentSessionLimit(adminId: number): Promise<void> {
    const activeSessions = await this.getActiveSessions(adminId);
    
    if (activeSessions.length >= this.MAX_CONCURRENT_SESSIONS) {
      // Invalidate oldest sessions
      const sessionsToInvalidate = activeSessions
        .slice(this.MAX_CONCURRENT_SESSIONS - 1)
        .map(session => session.jti);

      if (sessionsToInvalidate.length > 0) {
        await query(`
          UPDATE admin_sessions 
          SET is_active = false
          WHERE jti = ANY($1)
        `, [sessionsToInvalidate]);
      }
    }
  }

  /**
   * Clean up expired sessions
   */
  static async cleanupExpiredSessions(): Promise<number> {
    const result = await query(`
      UPDATE admin_sessions 
      SET is_active = false
      WHERE expires_at <= NOW() AND is_active = true
      RETURNING id
    `);

    return result.rows.length;
  }

  /**
   * Get session statistics
   */
  static async getSessionStats(): Promise<{
    totalActive: number;
    totalExpired: number;
    totalInvalidated: number;
  }> {
    const result = await query(`
      SELECT 
        COUNT(*) FILTER (WHERE is_active = true AND expires_at > NOW()) as total_active,
        COUNT(*) FILTER (WHERE expires_at <= NOW()) as total_expired,
        COUNT(*) FILTER (WHERE is_active = false) as total_invalidated
      FROM admin_sessions
    `);

    return {
      totalActive: parseInt(result.rows[0].total_active) || 0,
      totalExpired: parseInt(result.rows[0].total_expired) || 0,
      totalInvalidated: parseInt(result.rows[0].total_invalidated) || 0,
    };
  }

  /**
   * Create sessions table if it doesn't exist
   */
  static async initializeSessionsTable(): Promise<void> {
    await query(`
      CREATE TABLE IF NOT EXISTS admin_sessions (
        id VARCHAR(255) PRIMARY KEY,
        admin_id INTEGER NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
        jti VARCHAR(255) NOT NULL UNIQUE,
        ip VARCHAR(45) NOT NULL,
        user_agent TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL
      );

      CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_id ON admin_sessions(admin_id);
      CREATE INDEX IF NOT EXISTS idx_admin_sessions_jti ON admin_sessions(jti);
      CREATE INDEX IF NOT EXISTS idx_admin_sessions_active ON admin_sessions(is_active, expires_at);
    `);
  }
}

export default SessionManager;
