/**
 * Production-Grade Image Service
 * 
 * This service handles image processing, validation, optimization, and serving
 * following industry best practices for performance, security, and scalability.
 */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import crypto from 'crypto';
import { Request, Response } from 'express';

// Image configuration constants
export const IMAGE_CONFIG = {
  // Supported formats
  SUPPORTED_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],
  SUPPORTED_MIME_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  
  // Size limits
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_DIMENSION: 4096, // 4K max width/height
  
  // Thumbnail sizes
  THUMBNAIL_SIZES: {
    small: { width: 150, height: 150 },
    medium: { width: 300, height: 300 },
    large: { width: 600, height: 400 },
    card: { width: 400, height: 225 } // 16:9 aspect ratio for cards
  },
  
  // Quality settings
  JPEG_QUALITY: 85,
  WEBP_QUALITY: 80,
  PNG_COMPRESSION: 6,
  
  // Cache settings
  CACHE_MAX_AGE: 31536000, // 1 year in seconds
  CACHE_CONTROL: 'public, max-age=31536000, immutable',

  // CDN settings
  CDN_BASE_URL: process.env.CDN_BASE_URL || '', // Optional CDN URL
  IMAGE_VERSION: process.env.IMAGE_VERSION || 'v1', // For cache busting

  // Performance settings
  ENABLE_WEBP: true,
  ENABLE_AVIF: false, // Can be enabled when browser support improves
  COMPRESSION_LEVEL: 6
};

export interface ImageProcessingOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  background?: string;
}

export class ImageService {
  private static uploadsDir = path.join(__dirname, '../../uploads');
  private static scholarshipsDir = path.join(this.uploadsDir, 'scholarships');
  private static thumbnailsDir = path.join(this.scholarshipsDir, 'thumbnails');

  /**
   * Initialize image service directories
   */
  static async initialize(): Promise<void> {
    try {
      // Ensure all required directories exist
      await this.ensureDirectoryExists(this.uploadsDir);
      await this.ensureDirectoryExists(this.scholarshipsDir);
      await this.ensureDirectoryExists(this.thumbnailsDir);
      
      console.log('✅ Image service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize image service:', error);
      throw error;
    }
  }

  /**
   * Validate uploaded image file
   */
  static async validateImage(filePath: string): Promise<{
    isValid: boolean;
    metadata?: sharp.Metadata;
    error?: string;
  }> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return { isValid: false, error: 'File does not exist' };
      }

      // Get file stats
      const stats = fs.statSync(filePath);
      
      // Check file size
      if (stats.size > IMAGE_CONFIG.MAX_FILE_SIZE) {
        return { 
          isValid: false, 
          error: `File size ${(stats.size / 1024 / 1024).toFixed(2)}MB exceeds maximum ${IMAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB` 
        };
      }

      // Get image metadata using Sharp
      const metadata = await sharp(filePath).metadata();
      
      // Validate format
      if (!metadata.format || !IMAGE_CONFIG.SUPPORTED_FORMATS.includes(metadata.format)) {
        return { 
          isValid: false, 
          error: `Unsupported format: ${metadata.format}. Supported: ${IMAGE_CONFIG.SUPPORTED_FORMATS.join(', ')}` 
        };
      }

      // Validate dimensions
      if (metadata.width && metadata.width > IMAGE_CONFIG.MAX_DIMENSION) {
        return { 
          isValid: false, 
          error: `Width ${metadata.width}px exceeds maximum ${IMAGE_CONFIG.MAX_DIMENSION}px` 
        };
      }

      if (metadata.height && metadata.height > IMAGE_CONFIG.MAX_DIMENSION) {
        return { 
          isValid: false, 
          error: `Height ${metadata.height}px exceeds maximum ${IMAGE_CONFIG.MAX_DIMENSION}px` 
        };
      }

      return { isValid: true, metadata };
    } catch (error) {
      return { 
        isValid: false, 
        error: `Image validation failed: ${(error as Error).message}` 
      };
    }
  }

  /**
   * Process and optimize image
   */
  static async processImage(
    inputPath: string, 
    outputPath: string, 
    options: ImageProcessingOptions = {}
  ): Promise<{ success: boolean; outputPath?: string; error?: string }> {
    try {
      let pipeline = sharp(inputPath);

      // Apply transformations
      if (options.width || options.height) {
        pipeline = pipeline.resize(options.width, options.height, {
          fit: options.fit || 'cover',
          background: options.background || { r: 255, g: 255, b: 255, alpha: 1 }
        });
      }

      // Apply format-specific optimizations
      const format = options.format || 'jpeg';
      switch (format) {
        case 'jpeg':
          pipeline = pipeline.jpeg({ 
            quality: options.quality || IMAGE_CONFIG.JPEG_QUALITY,
            progressive: true,
            mozjpeg: true
          });
          break;
        case 'png':
          pipeline = pipeline.png({ 
            compressionLevel: IMAGE_CONFIG.PNG_COMPRESSION,
            progressive: true
          });
          break;
        case 'webp':
          pipeline = pipeline.webp({ 
            quality: options.quality || IMAGE_CONFIG.WEBP_QUALITY,
            effort: 6
          });
          break;
      }

      // Ensure output directory exists
      await this.ensureDirectoryExists(path.dirname(outputPath));

      // Process and save
      await pipeline.toFile(outputPath);

      return { success: true, outputPath };
    } catch (error) {
      return { 
        success: false, 
        error: `Image processing failed: ${(error as Error).message}` 
      };
    }
  }

  /**
   * Generate thumbnails for scholarship images
   */
  static async generateThumbnails(originalPath: string, baseFilename: string): Promise<{
    success: boolean;
    thumbnails?: Record<string, string>;
    error?: string;
  }> {
    try {
      const thumbnails: Record<string, string> = {};

      // Generate thumbnails for each size
      for (const [sizeName, dimensions] of Object.entries(IMAGE_CONFIG.THUMBNAIL_SIZES)) {
        const thumbnailFilename = `${baseFilename}_${sizeName}.webp`;
        const thumbnailPath = path.join(this.thumbnailsDir, thumbnailFilename);

        const result = await this.processImage(originalPath, thumbnailPath, {
          width: dimensions.width,
          height: dimensions.height,
          format: 'webp',
          fit: 'cover'
        });

        if (result.success) {
          thumbnails[sizeName] = `/uploads/scholarships/thumbnails/${thumbnailFilename}`;
        } else {
          console.error(`Failed to generate ${sizeName} thumbnail:`, result.error);
        }
      }

      return { success: true, thumbnails };
    } catch (error) {
      return { 
        success: false, 
        error: `Thumbnail generation failed: ${(error as Error).message}` 
      };
    }
  }

  /**
   * Serve image with proper headers and error handling
   */
  static async serveImage(req: Request, res: Response): Promise<void> {
    try {
      const { category, filename } = req.params;
      const filePath = path.join(this.uploadsDir, category, filename);

      // Security check - prevent directory traversal
      const normalizedPath = path.normalize(filePath);
      if (!normalizedPath.startsWith(this.uploadsDir)) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        res.status(404).json({ error: 'Image not found' });
        return;
      }

      // Get file stats for headers
      const stats = fs.statSync(filePath);
      const ext = path.extname(filename).toLowerCase();

      // Set appropriate headers for CDN and browser caching
      res.setHeader('Content-Type', this.getMimeType(ext));
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Cache-Control', IMAGE_CONFIG.CACHE_CONTROL);
      res.setHeader('ETag', this.generateETag(filePath, stats));
      res.setHeader('Last-Modified', stats.mtime.toUTCString());

      // CDN-friendly headers
      res.setHeader('Vary', 'Accept-Encoding');
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');

      // Performance headers
      if (ext === '.webp') {
        res.setHeader('Content-Encoding', 'identity');
      }

      // Handle conditional requests
      const ifNoneMatch = req.headers['if-none-match'];
      const ifModifiedSince = req.headers['if-modified-since'];

      if (ifNoneMatch && ifNoneMatch === res.getHeader('ETag')) {
        res.status(304).end();
        return;
      }

      if (ifModifiedSince && new Date(ifModifiedSince) >= stats.mtime) {
        res.status(304).end();
        return;
      }

      // Stream the file
      const stream = fs.createReadStream(filePath);
      stream.pipe(res);

    } catch (error) {
      console.error('Error serving image:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Clean up old or unused images
   */
  static async cleanupImages(activeImagePaths: string[]): Promise<{
    success: boolean;
    deletedCount?: number;
    error?: string;
  }> {
    try {
      let deletedCount = 0;
      const allFiles = fs.readdirSync(this.scholarshipsDir);

      for (const file of allFiles) {
        const filePath = path.join(this.scholarshipsDir, file);
        const relativePath = `/uploads/scholarships/${file}`;

        // Skip if file is still in use
        if (activeImagePaths.includes(relativePath)) {
          continue;
        }

        // Delete unused file
        fs.unlinkSync(filePath);
        deletedCount++;
        console.log(`Deleted unused image: ${file}`);
      }

      return { success: true, deletedCount };
    } catch (error) {
      return { 
        success: false, 
        error: `Cleanup failed: ${(error as Error).message}` 
      };
    }
  }

  /**
   * Generate CDN-ready image URL
   */
  static generateCDNUrl(imagePath: string, size?: string): string {
    const baseUrl = IMAGE_CONFIG.CDN_BASE_URL || process.env.API_BASE_URL || 'http://localhost:5000';
    const version = IMAGE_CONFIG.IMAGE_VERSION;

    if (size && size !== 'original') {
      const pathParts = imagePath.split('/');
      const filename = pathParts[pathParts.length - 1];
      const filenameWithoutExt = filename.replace(/\.[^/.]+$/, '');
      return `${baseUrl}/uploads/scholarships/thumbnails/${filenameWithoutExt}_${size}.webp?v=${version}`;
    }

    return `${baseUrl}${imagePath}?v=${version}`;
  }

  /**
   * Get responsive image URLs for different screen sizes
   */
  static getResponsiveImageUrls(imagePath: string): {
    mobile: string;
    tablet: string;
    desktop: string;
    original: string;
  } {
    return {
      mobile: this.generateCDNUrl(imagePath, 'small'),
      tablet: this.generateCDNUrl(imagePath, 'medium'),
      desktop: this.generateCDNUrl(imagePath, 'large'),
      original: this.generateCDNUrl(imagePath, 'original')
    };
  }

  /**
   * Generate image metadata for SEO and social sharing
   */
  static generateImageMetadata(imagePath: string, title: string): {
    url: string;
    width: number;
    height: number;
    alt: string;
    type: string;
  } {
    const cardUrl = this.generateCDNUrl(imagePath, 'card');
    const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES.card;

    return {
      url: cardUrl,
      width: dimensions.width,
      height: dimensions.height,
      alt: `${title} - Scholarship thumbnail`,
      type: 'image/webp'
    };
  }

  // Helper methods
  private static async ensureDirectoryExists(dirPath: string): Promise<void> {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  private static getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.gif': 'image/gif'
    };
    return mimeTypes[extension] || 'application/octet-stream';
  }

  private static generateETag(filePath: string, stats: fs.Stats): string {
    return `"${stats.size}-${stats.mtime.getTime()}"`;
  }
}
