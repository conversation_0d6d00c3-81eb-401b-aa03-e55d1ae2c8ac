/**
 * DATABASE MIGRATION NOTICE
 *
 * This application has been fully migrated from Sequelize to PostgreSQL.
 * All database operations now use direct PostgreSQL queries for improved performance.
 *
 * The migration was completed and Prisma has been completely removed.
 */

// Load environment variables first
import dotenv from 'dotenv';
dotenv.config();

import express, { ErrorRequestHandler, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import morgan from 'morgan';
import path from 'path';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import helmet from 'helmet';
import slowDown from 'express-slow-down';
import bcrypt from 'bcryptjs';
import { initializeDatabase, testConnection, closeDatabase } from './config/database';
import { Admin } from './models/Admin';
import validateEnv, { getEnv, getBoolEnv, getNumEnv } from './utils/envValidator';
import { validateCsrfToken, generateCsrfToken } from './middleware/csrf.middleware';
import apiCache from './middleware/apiCache.middleware';
import validation from './middleware/validation.middleware';
import { ImageService } from './services/imageService';
import rateLimiting from './middleware/rateLimiting.middleware';
import { createCSP } from './middleware/advancedCSP';
import { errorHandler, notFoundHandler } from './middleware/error.middleware';

// Import routes
import newAuthRoutes from './routes/auth.new'; // Secure authentication system with HTTP-only cookies
import adminRoutes from './routes/admin.routes'; // Admin management routes
import scholarshipRoutes from "./routes/scholarship.routes";
import userRoutes from './routes/user.routes';
import adminPasswordRoutes from './routes/admin.password.routes';
import messagesRoutes from './routes/messages';
import newsletterRoutes from './routes/newsletter';
import securityDashboardRoutes from './routes/security.dashboard.routes';
import twoFactorRoutes from './routes/twoFactor.routes';
import deviceTrustRoutes from './routes/deviceTrust.routes';
import securityCSPRoutes from './routes/security.csp.routes';
import guideRoutes from './routes/guide.routes';
import opportunityRoutes from './routes/opportunity.routes';
import countryRoutes from './routes/country.routes';
import imageRoutes from './routes/images';
import contactSettingsRoutes from './routes/contactSettings.routes';

// Validate and load environment variables
if (!validateEnv()) {
  console.error('Environment validation failed. Exiting application.');
  process.exit(1);
}

// CRITICAL: Validate JWT secret strength for industry standards
const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret || jwtSecret.length < 32 || jwtSecret === 'your-secret-key' || jwtSecret === 'fallback-secret-key') {
  console.error('❌ SECURITY ERROR: JWT_SECRET must be at least 32 characters and not use default values');
  console.error('   Current JWT_SECRET length:', jwtSecret?.length || 0);
  console.error('   Please set a strong JWT_SECRET in your environment variables');
  process.exit(1);
}
console.log('✅ JWT secret validation passed');

// Log application startup information
console.info(`Starting MaBourse backend in ${process.env.NODE_ENV} mode`);
console.info(`Server port: ${process.env.PORT}`);
console.info(`Database: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);

// Initialize PostgreSQL database connection
const db = initializeDatabase();

// Initialize image service
ImageService.initialize().catch(error => {
  console.error('Failed to initialize image service:', error);
  process.exit(1);
});

// Create Express app
const app = express();

// Middleware
// Configure CORS based on environment
const corsOrigins = process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000', 'http://localhost:3001'];
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow file:// protocol for testing
    if (origin.startsWith('file://')) return callback(null, true);

    // Allow configured origins
    if (corsOrigins.includes(origin)) return callback(null, true);

    // In development, be more permissive
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }

    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Debug-Mode'],
  exposedHeaders: ['Set-Cookie']
}));

// Log CORS configuration
console.info(`CORS configured for origins: ${corsOrigins.join(', ')}`);

// Basic middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser()); // Add cookie parser middleware
app.use(morgan('dev'));

// PROFESSIONAL-GRADE SECURITY MIDDLEWARE
app.use(helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  // X-Frame-Options
  frameguard: { action: 'deny' },
  // X-Content-Type-Options
  noSniff: true,
  // Referrer Policy
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  // Cross-Origin-Embedder-Policy
  crossOriginEmbedderPolicy: false, // Disable for API
  // Cross-Origin-Resource-Policy
  crossOriginResourcePolicy: { policy: 'same-origin' },
  // Cross-Origin-Opener-Policy
  crossOriginOpenerPolicy: { policy: 'same-origin' },
  // Origin-Agent-Cluster
  originAgentCluster: true,
  // X-DNS-Prefetch-Control
  dnsPrefetchControl: { allow: false },
  // X-Download-Options
  ieNoOpen: true,
  // X-Permitted-Cross-Domain-Policies
  permittedCrossDomainPolicies: false,
  // X-XSS-Protection
  xssFilter: true
}));
app.use(compression()); // Compress responses

// PROFESSIONAL SLOW DOWN MIDDLEWARE - Progressive delays for suspicious activity
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 10, // Allow 10 requests per window without delay
  delayMs: (hits: number) => hits * 100, // Add 100ms delay per request after delayAfter
  maxDelayMs: 5000, // Maximum delay of 5 seconds
  skipSuccessfulRequests: true, // Don't count successful requests
  skipFailedRequests: false, // Count failed requests
  keyGenerator: (req: any) => req.ip || 'unknown'
});

app.use('/api/auth', speedLimiter); // Apply to authentication routes
app.use(validation.securityHeaders); // Add additional security headers
app.use(validation.sanitizeUrlParams); // Sanitize URL parameters

// Advanced Content Security Policy
const environment = process.env.NODE_ENV === 'production' ? 'production' : 'development';
app.use(createCSP(environment)); // Apply CSP based on environment

// Caching middleware for public endpoints
app.use(apiCache.addCacheHeaders()); // Add cache headers to responses

// Production-grade static file serving with proper headers and security
app.use('/uploads', (req, res, next) => {
  // Security headers for static files
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year cache
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin'); // Allow cross-origin access for images

  // Log image requests for monitoring
  console.log(`Image request: ${req.method} ${req.url} from ${req.ip}`);

  next();
}, express.static(path.join(__dirname, '../uploads'), {
  // Express static options for production
  maxAge: '1y', // 1 year cache
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Set appropriate content type based on file extension
    const ext = path.extname(filePath).toLowerCase();
    if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)) {
      res.setHeader('Content-Type', `image/${ext.slice(1) === 'jpg' ? 'jpeg' : ext.slice(1)}`);
    }
  }
}));

// Apply general API rate limiting to all routes
app.use('/api', rateLimiting.apiLimiter);

// Routes
// UNIFIED SECURE AUTHENTICATION SYSTEM (HTTP-only cookies + Enhanced Security)
app.use('/api/auth',
  validation.sanitizeRequestBody, // Input sanitization
  newAuthRoutes // Secure auth routes
);

// Shared routes with caching for public endpoints
app.use('/api/scholarships', apiCache.cacheApiResponse(300), validation.sanitizeRequestBody, scholarshipRoutes);
app.use('/api/users', validation.sanitizeRequestBody, userRoutes);
app.use('/api/messages', rateLimiting.contactFormLimiter, validation.sanitizeRequestBody, messagesRoutes);
app.use('/api/newsletter', rateLimiting.contactFormLimiter, validation.sanitizeRequestBody, newsletterRoutes);
app.use('/api/guides', apiCache.cacheApiResponse(300), validation.sanitizeRequestBody, guideRoutes);
app.use('/api/opportunities', apiCache.cacheApiResponse(300), validation.sanitizeRequestBody, opportunityRoutes);
app.use('/api/countries', apiCache.cacheApiResponse(600), validation.sanitizeRequestBody, countryRoutes);
// Enhanced image serving with optimization and caching
app.use('/api/images', apiCache.cacheApiResponse(3600), imageRoutes); // 1 hour cache for images
// Contact settings routes (public endpoint cached, admin endpoints protected)
app.use('/api/contact-settings', apiCache.cacheApiResponse(3600), validation.sanitizeRequestBody, contactSettingsRoutes);
// Admin password routes must be registered before admin routes to avoid authentication middleware
app.use('/api/admin/password', rateLimiting.passwordResetLimiter, validation.sanitizeRequestBody, adminPasswordRoutes); // Re-enabled for admin functionality
// Admin routes (protected)
app.use('/api/admin', rateLimiting.authLimiter, validation.sanitizeRequestBody, adminRoutes);
// Security dashboard routes (admin only)
app.use('/api/security', rateLimiting.authLimiter, validation.sanitizeRequestBody, securityDashboardRoutes);
// Two-factor authentication routes
app.use('/api/2fa', rateLimiting.authLimiter, validation.sanitizeRequestBody, twoFactorRoutes);
// Device trust management routes
app.use('/api/devices', rateLimiting.authLimiter, validation.sanitizeRequestBody, deviceTrustRoutes);
// CSP security routes
app.use('/api/security', rateLimiting.authLimiter, validation.sanitizeRequestBody, securityCSPRoutes);

// Health check endpoint with caching
app.get('/api/health', apiCache.cacheApiResponse(60), (req, res) => {
  res.json({
    status: 'ok',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
  try {
    // Test database connection
    const isConnected = await testConnection();

    if (!isConnected) {
      throw new Error('Database connection test failed');
    }

    // Test model queries
    const adminCount = await Admin.count();
    const { User } = await import('./models/User');
    const userCount = await User.count();
    const { Scholarship } = await import('./models/Scholarship');
    const scholarshipCount = await Scholarship.count();

    res.json({
      success: true,
      message: 'Database connection is working properly',
      data: {
        adminCount,
        userCount,
        scholarshipCount
      }
    });
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection test failed',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

// Cache statistics endpoint (admin only)
app.get('/api/admin/cache-stats', (req, res) => {
  if (!req.user || !req.user.isMainAdmin) {
    return res.status(403).json({
      success: false,
      message: 'Access denied',
      error: 'Admin privileges required'
    });
  }

  res.json({
    success: true,
    message: 'Cache statistics retrieved successfully',
    data: {
      apiCache: apiCache.getCacheStats()
      // Additional cache stats can be added here
    }
  });
});

// Professional error handling middleware
app.use(notFoundHandler); // Handle 404 errors
app.use(errorHandler); // Handle all other errors

// Import cleanup utilities
// import { performDataCleanup } from './utils/cleanupUtils'; // Temporarily disabled

// Initialize database and start server
const PORT = process.env.PORT || 5000;

// Start server
const startServer = async () => {
  try {
    // Test database connection
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }
    console.log('PostgreSQL database connection established successfully.');

    // Check if main admin exists (temporarily disabled for debugging)
    // const mainAdmin = await Admin.findMainAdmin();

    // Only create admin if none exists (this should already be handled by migration)
    // if (!mainAdmin) {
    //   console.log('Creating main admin...');
    //   await Admin.create({
    //     name: 'Main Admin',
    //     email: '<EMAIL>',
    //     password: 'admin123',
    //     role: 'super_admin',
    //     privileges: ['all'],
    //     isMainAdmin: true,
    //     failedLoginAttempts: 0,
    //     twoFactorEnabled: false
    //   });
    //   console.log('Main admin created successfully.');
    // }

    // Run data cleanup to fix any duplicate data issues
    // Temporarily disabled to fix server startup issues
    // console.log('Running data cleanup process...');
    // await performDataCleanup();

    // Start server
    console.log('About to start server on port', PORT);
    const server = app.listen(PORT, () => {
      console.log(`✅ Server is running on port ${PORT}`);
      console.log(`🌐 Health check: http://localhost:${PORT}/health`);
      console.log(`📚 API docs: http://localhost:${PORT}/api-docs`);
    });

    server.on('error', (error) => {
      console.error('Server error:', error);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    await closeDatabase();
    process.exit(1);
  }
};

// Handle application shutdown
process.on('SIGINT', async () => {
  await closeDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closeDatabase();
  process.exit(0);
});

startServer();