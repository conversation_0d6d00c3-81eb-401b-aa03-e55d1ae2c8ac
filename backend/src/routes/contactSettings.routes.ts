/**
 * Contact Settings Routes
 * 
 * Production-grade routes for managing contact information and social media links
 * with proper authentication, validation, and rate limiting.
 */

import express from 'express';
import {
  getAllContactSettings,
  getContactSettingsGrouped,
  getPublicContactSettings,
  getContactSettingsByCategory,
  createContactSetting,
  updateContactSetting,
  deleteContactSetting,
  bulkUpdateContactSettings,
  validateContactSetting,
  validateBulkUpdate
} from '../controllers/contactSettings.controller';
import { requireAdmin } from '../middleware/auth.new';

const router = express.Router();

// Public routes (no authentication required)
/**
 * GET /api/contact-settings/public
 * Get public contact settings for frontend display
 */
router.get('/public', getPublicContactSettings);

// Admin routes (authentication required)
/**
 * GET /api/contact-settings
 * Get all contact settings (admin only)
 */
router.get('/', requireAdmin, getAllContactSettings);

/**
 * GET /api/contact-settings/grouped
 * Get contact settings grouped by category (admin only)
 */
router.get('/grouped', requireAdmin, getContactSettingsGrouped);

/**
 * GET /api/contact-settings/category/:category
 * Get contact settings by category (admin only)
 */
router.get('/category/:category', requireAdmin, getContactSettingsByCategory);

/**
 * POST /api/contact-settings
 * Create a new contact setting (admin only)
 */
router.post(
  '/',
  requireAdmin,
  validateContactSetting,
  createContactSetting
);

/**
 * PUT /api/contact-settings/:id
 * Update a contact setting (admin only)
 */
router.put(
  '/:id',
  requireAdmin,
  validateContactSetting,
  updateContactSetting
);

/**
 * DELETE /api/contact-settings/:id
 * Delete a contact setting (admin only)
 */
router.delete('/:id', requireAdmin, deleteContactSetting);

/**
 * PUT /api/contact-settings/bulk
 * Bulk update contact settings (admin only)
 */
router.put(
  '/bulk',
  requireAdmin,
  validateBulkUpdate,
  bulkUpdateContactSettings
);

export default router;
