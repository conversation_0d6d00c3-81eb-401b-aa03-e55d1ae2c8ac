/**
 * Guide Routes
 * 
 * API routes for guide management with proper authentication and validation
 */

import { Router } from 'express';
import {
  getAllGuides,
  getGuideById,
  getGuideBySlug,
  getGuidesByCategory,
  searchGuides,
  createGuide,
  updateGuide,
  deleteGuide
} from '../controllers/guide.controller';
import { requireAdmin } from '../middleware/auth.new';

const router = Router();

// Public routes
router.get('/', getAllGuides);
router.get('/search', searchGuides);
router.get('/category/:category', getGuidesByCategory);
router.get('/slug/:slug', getGuideBySlug);
router.get('/:id', getGuideById);

// Admin-only routes
router.post('/', requireAdmin, createGuide);
router.put('/:id', requireAdmin, updateGuide);
router.delete('/:id', requireAdmin, deleteGuide);

export default router;
