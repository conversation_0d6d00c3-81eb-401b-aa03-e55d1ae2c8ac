/**
 * Enhanced Image Routes
 * 
 * Production-grade image serving with optimization, caching, and security
 */

import express from 'express';
import { ImageService } from '../services/imageService';
import { sendError, sendSuccess } from '../utils/apiResponse';

const router = express.Router();

/**
 * Serve optimized images with proper caching
 * GET /api/images/:category/:filename
 */
router.get('/:category/:filename', async (req, res) => {
  await ImageService.serveImage(req, res);
});

/**
 * Get image metadata and thumbnails
 * GET /api/images/:category/:filename/info
 */
router.get('/:category/:filename/info', async (req, res) => {
  try {
    const { category, filename } = req.params;
    const filePath = `uploads/${category}/${filename}`;
    
    // This would typically get metadata from database
    // For now, return basic info
    return sendSuccess(res, {
      filename,
      category,
      url: `/uploads/${category}/${filename}`,
      thumbnails: {
        small: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_small.webp`,
        medium: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_medium.webp`,
        large: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_large.webp`,
        card: `/uploads/${category}/thumbnails/${filename.replace(/\.[^/.]+$/, '')}_card.webp`
      }
    }, 'Image info retrieved successfully');
  } catch (error) {
    console.error('Error getting image info:', error);
    return sendError(res, 'Failed to get image info', error);
  }
});

/**
 * Health check for image service
 * GET /api/images/health
 */
router.get('/health', (req, res) => {
  return sendSuccess(res, {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'image-service'
  }, 'Image service is healthy');
});

export default router;
