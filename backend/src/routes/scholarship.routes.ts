import express from 'express';
import { body } from 'express-validator';
import {
  createScholarship,
  getScholarships,
  getScholarshipById,
  updateScholarship,
  deleteScholarship,
  searchScholarships,
  bulkImportScholarships,
  getScholarshipLevels,
  getFundingSources,
  getLatestScholarships,
  getRelatedScholarships,
  getCountriesForSidebar
} from "../controllers/scholarship.controller";
import { requireAdmin } from '../middleware/auth.new';
import { handleScholarshipUpload } from '../middleware/upload.middleware';

const router = express.Router();

// Get all scholarships (public)
router.get('/', getScholarships);

// Search scholarships (public)
router.get('/search', searchScholarships);

// Get scholarship levels for dropdown (public)
router.get('/levels', getScholarshipLevels);

// Get funding sources for dropdown (public)
router.get('/funding-sources', getFundingSources);

// Get latest scholarships for sidebar (public)
router.get('/latest', getLatestScholarships);

// Get related scholarships for sidebar (public)
router.get('/related', getRelatedScholarships);

// Get countries for sidebar (public)
router.get('/countries-sidebar', getCountriesForSidebar);

// Get scholarship by ID (public)
router.get('/:id', getScholarshipById);

// Protected routes (require admin authentication)
router.use(requireAdmin);

// Create scholarship
router.post(
  '/',
  handleScholarshipUpload,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('deadline').isISO8601().withMessage('Valid deadline date is required'),
  ],
  createScholarship
);

// Update scholarship
router.put(
  '/:id',
  handleScholarshipUpload,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('deadline').isISO8601().withMessage('Valid deadline date is required'),
  ],
  updateScholarship
);

// Delete scholarship
router.delete('/:id', deleteScholarship);

// Bulk import scholarships
router.post(
  '/bulk',
  [
    body('scholarships').isArray().withMessage('Scholarships must be an array'),
    body('scholarships.*.title').notEmpty().withMessage('Title is required for each scholarship'),
    body('scholarships.*.description').notEmpty().withMessage('Description is required for each scholarship'),
    body('scholarships.*.deadline').isISO8601().withMessage('Valid deadline date is required for each scholarship'),
  ],
  bulkImportScholarships
);

export default router;
