/**
 * CSP Security Routes
 * Handles Content Security Policy violation reports and CSP management
 */

import express, { Request, Response } from 'express';
import { body, query } from 'express-validator';
import { authenticate, requireAdmin } from '../middleware/auth.new';
import { AdvancedCSPService } from '../middleware/advancedCSP';
import {
  sendSuccess,
  sendError,
  sendValidationError,
  sendPaginatedSuccess
} from '../utils/apiResponse';
import { query as dbQuery } from '../config/database';
import { validationResult } from 'express-validator';

const router = express.Router();

/**
 * @route POST /api/security/csp-violation
 * @desc Handle CSP violation reports
 * @access Public (CSP reports come from browsers)
 */
router.post('/csp-violation', AdvancedCSPService.cspViolationHandler);

/**
 * @route GET /api/security/csp-violations
 * @desc Get CSP violation reports (admin only)
 * @access Private (Super Admin only)
 */
router.get('/csp-violations', [
  authenticate,
  requireAdmin,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid severity level'),
  query('from')
    .optional()
    .isISO8601()
    .withMessage('From date must be in ISO8601 format'),
  query('to')
    .optional()
    .isISO8601()
    .withMessage('To date must be in ISO8601 format')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { 
      page = '1', 
      limit = '20', 
      severity, 
      from, 
      to 
    } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const offset = (pageNumber - 1) * limitNumber;

    // Build WHERE clause
    let whereClause = "WHERE event_type = 'SUSPICIOUS_ACTIVITY' AND message LIKE '%CSP violation%'";
    const params: any[] = [];
    let paramCount = 1;

    if (severity) {
      whereClause += ` AND severity = $${paramCount}`;
      params.push(severity);
      paramCount++;
    }

    if (from) {
      whereClause += ` AND timestamp >= $${paramCount}`;
      params.push(from);
      paramCount++;
    }

    if (to) {
      whereClause += ` AND timestamp <= $${paramCount}`;
      params.push(to);
      paramCount++;
    }

    // Get total count
    const countResult = await dbQuery(`
      SELECT COUNT(*) as total FROM security_events ${whereClause}
    `, params);

    // Get violations with pagination
    const violationsResult = await dbQuery(`
      SELECT 
        id,
        timestamp,
        message,
        email,
        ip,
        user_agent,
        severity,
        risk_score,
        details
      FROM security_events 
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...params, limitNumber, offset]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limitNumber);

    // Process violations to extract CSP-specific data
    const violations = violationsResult.rows.map(row => ({
      id: row.id,
      timestamp: row.timestamp,
      message: row.message,
      email: row.email,
      ip: row.ip,
      userAgent: row.user_agent,
      severity: row.severity,
      riskScore: row.risk_score,
      violation: row.details?.violation || {},
      suspiciousContent: row.details?.suspiciousContent || null
    }));

    return sendPaginatedSuccess(
      res,
      violations,
      {
        page: pageNumber,
        limit: limitNumber,
        total,
        totalPages
      },
      'CSP violations retrieved successfully'
    );
  } catch (error) {
    console.error('Get CSP violations error:', error);
    return sendError(res, 'Failed to retrieve CSP violations', error);
  }
});

/**
 * @route GET /api/security/csp-analytics
 * @desc Get CSP violation analytics
 * @access Private (Super Admin only)
 */
router.get('/csp-analytics', [
  authenticate,
  requireAdmin,
  query('days')
    .optional()
    .isInt({ min: 1, max: 90 })
    .withMessage('Days must be between 1 and 90')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { days = '7' } = req.query;
    const daysNumber = parseInt(days as string, 10);

    // Get violation trends
    const trendsResult = await dbQuery(`
      SELECT 
        DATE_TRUNC('day', timestamp) as date,
        COUNT(*) as violation_count,
        COUNT(DISTINCT ip) as unique_ips,
        AVG(risk_score) as avg_risk_score
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
      GROUP BY DATE_TRUNC('day', timestamp)
      ORDER BY date DESC
    `);

    // Get top violated directives
    const directivesResult = await dbQuery(`
      SELECT 
        details->'violation'->>'violated-directive' as directive,
        COUNT(*) as count
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
        AND details->'violation'->>'violated-directive' IS NOT NULL
      GROUP BY details->'violation'->>'violated-directive'
      ORDER BY count DESC
      LIMIT 10
    `);

    // Get top source IPs
    const ipsResult = await dbQuery(`
      SELECT 
        ip,
        COUNT(*) as violation_count,
        MAX(risk_score) as max_risk_score,
        COUNT(DISTINCT details->'violation'->>'violated-directive') as unique_directives
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
      GROUP BY ip
      ORDER BY violation_count DESC
      LIMIT 10
    `);

    // Get blocked URIs
    const blockedUrisResult = await dbQuery(`
      SELECT 
        details->'violation'->>'blocked-uri' as blocked_uri,
        COUNT(*) as count,
        MAX(risk_score) as max_risk_score
      FROM security_events 
      WHERE event_type = 'SUSPICIOUS_ACTIVITY' 
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '${daysNumber} days'
        AND details->'violation'->>'blocked-uri' IS NOT NULL
        AND details->'violation'->>'blocked-uri' != ''
      GROUP BY details->'violation'->>'blocked-uri'
      ORDER BY count DESC
      LIMIT 10
    `);

    const analytics = {
      trends: trendsResult.rows,
      topViolatedDirectives: directivesResult.rows,
      topSourceIPs: ipsResult.rows,
      topBlockedURIs: blockedUrisResult.rows,
      summary: {
        totalViolations: trendsResult.rows.reduce((sum, row) => sum + parseInt(row.violation_count), 0),
        uniqueIPs: ipsResult.rows.length,
        avgRiskScore: trendsResult.rows.length > 0 
          ? trendsResult.rows.reduce((sum, row) => sum + parseFloat(row.avg_risk_score || '0'), 0) / trendsResult.rows.length
          : 0
      }
    };

    return sendSuccess(res, analytics, 'CSP analytics retrieved successfully');
  } catch (error) {
    console.error('Get CSP analytics error:', error);
    return sendError(res, 'Failed to retrieve CSP analytics', error);
  }
});

/**
 * @route POST /api/security/csp-config
 * @desc Update CSP configuration (super admin only)
 * @access Private (Super Admin only)
 */
router.post('/csp-config', [
  authenticate,
  requireAdmin,
  body('enableNonce')
    .optional()
    .isBoolean()
    .withMessage('enableNonce must be a boolean'),
  body('strictMode')
    .optional()
    .isBoolean()
    .withMessage('strictMode must be a boolean'),
  body('reportOnly')
    .optional()
    .isBoolean()
    .withMessage('reportOnly must be a boolean'),
  body('allowedDomains')
    .optional()
    .isArray()
    .withMessage('allowedDomains must be an array'),
  body('trustedScriptSources')
    .optional()
    .isArray()
    .withMessage('trustedScriptSources must be an array'),
  body('allowInlineStyles')
    .optional()
    .isBoolean()
    .withMessage('allowInlineStyles must be a boolean'),
  body('allowInlineScripts')
    .optional()
    .isBoolean()
    .withMessage('allowInlineScripts must be a boolean')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const config = req.body;

    // Store CSP configuration in database
    await dbQuery(`
      INSERT INTO system_config (key, value, updated_by, updated_at)
      VALUES ('csp_config', $1, $2, CURRENT_TIMESTAMP)
      ON CONFLICT (key) 
      DO UPDATE SET 
        value = EXCLUDED.value,
        updated_by = EXCLUDED.updated_by,
        updated_at = EXCLUDED.updated_at
    `, [JSON.stringify(config), req.user?.id]);

    return sendSuccess(res, config, 'CSP configuration updated successfully');
  } catch (error) {
    console.error('Update CSP config error:', error);
    return sendError(res, 'Failed to update CSP configuration', error);
  }
});

export default router;
