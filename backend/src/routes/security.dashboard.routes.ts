import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getSecurityDashboard,
  getSecurityEvents,
  getSecurityAlerts,
  acknowledgeAlert,
  resolveAlert,
  getIPAnalysis
} from '../controllers/security.dashboard.controller';
import { authenticate, requireAdmin } from '../middleware/auth.new';

const router = express.Router();

/**
 * @route GET /api/security/dashboard
 * @desc Get security dashboard overview
 * @access Private (Admin only)
 */
router.get(
  '/dashboard',
  authenticate,
  requireAdmin,
  [
    query('timeframe')
      .optional()
      .isIn(['hour', 'day', 'week'])
      .withMessage('Timeframe must be hour, day, or week')
  ],
  getSecurityDashboard
);

/**
 * @route GET /api/security/events
 * @desc Get security events with filtering and pagination
 * @access Private (Admin only)
 */
router.get(
  '/events',
  authenticate,
  requireAdmin,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('severity')
      .optional()
      .isIn(['info', 'warn', 'error', 'alert'])
      .withMessage('Invalid severity level'),
    query('eventType')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('Event type must be 1-100 characters'),
    query('ip')
      .optional()
      .isIP()
      .withMessage('Invalid IP address format'),
    query('email')
      .optional()
      .isEmail()
      .withMessage('Invalid email format'),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be in ISO8601 format'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be in ISO8601 format'),
    query('resolved')
      .optional()
      .isBoolean()
      .withMessage('Resolved must be a boolean')
  ],
  getSecurityEvents
);

/**
 * @route GET /api/security/alerts
 * @desc Get security alerts with filtering and pagination
 * @access Private (Admin only)
 */
router.get(
  '/alerts',
  authenticate,
  requireAdmin,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('severity')
      .optional()
      .isIn(['low', 'medium', 'high', 'critical'])
      .withMessage('Invalid severity level'),
    query('alertType')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('Alert type must be 1-100 characters'),
    query('acknowledged')
      .optional()
      .isBoolean()
      .withMessage('Acknowledged must be a boolean'),
    query('resolved')
      .optional()
      .isBoolean()
      .withMessage('Resolved must be a boolean')
  ],
  getSecurityAlerts
);

/**
 * @route POST /api/security/alerts/:id/acknowledge
 * @desc Acknowledge a security alert
 * @access Private (Admin only)
 */
router.post(
  '/alerts/:id/acknowledge',
  authenticate,
  requireAdmin,
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Alert ID must be a positive integer')
  ],
  acknowledgeAlert
);

/**
 * @route POST /api/security/alerts/:id/resolve
 * @desc Resolve a security alert
 * @access Private (Admin only)
 */
router.post(
  '/alerts/:id/resolve',
  authenticate,
  requireAdmin,
  [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Alert ID must be a positive integer'),
    body('resolutionNotes')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('Resolution notes cannot exceed 1000 characters')
  ],
  resolveAlert
);

/**
 * @route GET /api/security/ip/:ip/analysis
 * @desc Get IP address analysis including geolocation and risk assessment
 * @access Private (Admin only)
 */
router.get(
  '/ip/:ip/analysis',
  authenticate,
  requireAdmin,
  [
    param('ip')
      .isIP()
      .withMessage('Invalid IP address format')
  ],
  getIPAnalysis
);

/**
 * @route GET /api/security/metrics
 * @desc Get real-time security metrics
 * @access Private (Admin only)
 */
router.get(
  '/metrics',
  authenticate,
  requireAdmin,
  async (req, res) => {
    try {
      const { SecurityMonitor } = await import('../utils/securityMonitor');
      const timeframe = (req.query.timeframe as string) || 'day';
      
      const metrics = await SecurityMonitor.getSecurityMetrics(timeframe as any);
      
      res.json({
        success: true,
        data: metrics,
        message: 'Security metrics retrieved successfully'
      });
    } catch (error) {
      console.error('Security metrics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve security metrics',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
);

/**
 * @route GET /api/security/stats
 * @desc Get security statistics summary
 * @access Private (Admin only)
 */
router.get(
  '/stats',
  authenticate,
  requireAdmin,
  async (req, res) => {
    try {
      const { query } = await import('../config/database');
      
      // Get various security statistics
      const [
        totalEvents,
        activeAlerts,
        recentLogins,
        trustedDevices,
        riskEvents
      ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM security_events WHERE timestamp > NOW() - INTERVAL \'24 hours\''),
        query('SELECT COUNT(*) as count FROM security_alerts WHERE resolved = false'),
        query('SELECT COUNT(*) as count FROM login_attempts WHERE timestamp > NOW() - INTERVAL \'24 hours\' AND success = true'),
        query('SELECT COUNT(*) as count FROM trusted_devices WHERE trusted = true AND is_active = true'),
        query('SELECT COUNT(*) as count FROM security_events WHERE risk_score > 50 AND timestamp > NOW() - INTERVAL \'24 hours\'')
      ]);

      res.json({
        success: true,
        data: {
          totalEvents24h: parseInt(totalEvents.rows[0].count),
          activeAlerts: parseInt(activeAlerts.rows[0].count),
          successfulLogins24h: parseInt(recentLogins.rows[0].count),
          trustedDevices: parseInt(trustedDevices.rows[0].count),
          highRiskEvents24h: parseInt(riskEvents.rows[0].count),
          timestamp: new Date().toISOString()
        },
        message: 'Security statistics retrieved successfully'
      });
    } catch (error) {
      console.error('Security stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve security statistics',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
);

/**
 * @route GET /api/security/health
 * @desc Get security system health status
 * @access Private (Admin only)
 */
router.get(
  '/health',
  authenticate,
  requireAdmin,
  async (req, res) => {
    try {
      const { GeolocationService } = await import('../utils/geolocation');
      
      // Test various security components
      const healthChecks = {
        database: false,
        geolocation: false,
        monitoring: false,
        timestamp: new Date().toISOString()
      };

      try {
        const { query } = await import('../config/database');
        await query('SELECT 1');
        healthChecks.database = true;
      } catch (dbError) {
        console.error('Database health check failed:', dbError);
      }

      try {
        // Test geolocation service with a known IP
        await GeolocationService.getGeolocation('*******');
        healthChecks.geolocation = true;
      } catch (geoError) {
        console.error('Geolocation health check failed:', geoError);
      }

      try {
        const { SecurityMonitor } = await import('../utils/securityMonitor');
        await SecurityMonitor.getSecurityMetrics('hour');
        healthChecks.monitoring = true;
      } catch (monitorError) {
        console.error('Monitoring health check failed:', monitorError);
      }

      const allHealthy = Object.values(healthChecks).every(check => 
        typeof check === 'boolean' ? check : true
      );

      res.status(allHealthy ? 200 : 503).json({
        success: allHealthy,
        data: healthChecks,
        message: allHealthy ? 'All security systems operational' : 'Some security systems have issues'
      });
    } catch (error) {
      console.error('Security health check error:', error);
      res.status(500).json({
        success: false,
        message: 'Security health check failed',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
);

export default router;
