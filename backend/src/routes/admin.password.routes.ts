import express from 'express';
import { body } from 'express-validator';
import {
  forgotPassword,
  resetPassword,
  validateResetToken
} from '../controllers/admin.password.controller';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting for password reset requests
const passwordResetLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 requests per windowMs
  message: 'Too many password reset attempts, please try again after 15 minutes'
});

// Public routes - no authentication required

// Request password reset
router.post(
  '/forgot-password',
  passwordResetLimiter,
  [
    body('email').isEmail().withMessage('Please enter a valid email')
  ],
  forgotPassword
);

// Reset password with token
router.post(
  '/reset-password',
  [
    body('token').notEmpty().withMessage('Token is required'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long')
  ],
  resetPassword
);

// Validate reset token
router.get('/validate-token/:token', validateResetToken);

export default router;
