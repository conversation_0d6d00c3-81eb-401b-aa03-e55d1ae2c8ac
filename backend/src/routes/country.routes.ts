/**
 * Country Routes
 * 
 * API routes for country-related operations and scholarship filtering
 */

import { Router } from 'express';
import {
  getAllCountries,
  getScholarshipsByCountry,
  getCountryStatistics,
  searchCountries,
  getAllCountriesForSidebar
} from '../controllers/country.controller';

const router = Router();

// Public routes
router.get('/', getAllCountries);
router.get('/search', searchCountries);
router.get('/sidebar', getAllCountriesForSidebar);
router.get('/:country/scholarships', getScholarshipsByCountry);
router.get('/:country/statistics', getCountryStatistics);

export default router;
