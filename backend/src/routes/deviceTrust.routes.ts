import express from 'express';
import { param, query } from 'express-validator';
import { authenticate, requireAdmin } from '../middleware/auth.new';
import {
  getAdminDevices,
  getDeviceById,
  trustDevice,
  untrustDevice,
  deactivateDevice
} from '../controllers/deviceTrust.controller';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @route GET /api/devices
 * @desc Get all devices for the authenticated admin
 * @access Private (Admin only)
 */
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('trusted')
    .optional()
    .isBoolean()
    .withMessage('Trusted must be a boolean'),
  query('active')
    .optional()
    .isBoolean()
    .withMessage('Active must be a boolean')
], getAdminDevices);

/**
 * @route GET /api/devices/:id
 * @desc Get device details by ID
 * @access Private (Admin only)
 */
router.get('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Device ID must be a positive integer')
], getDeviceById);

/**
 * @route PUT /api/devices/:id/trust
 * @desc Trust a device
 * @access Private (Admin only)
 */
router.put('/:id/trust', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Device ID must be a positive integer')
], trustDevice);

/**
 * @route PUT /api/devices/:id/untrust
 * @desc Untrust a device
 * @access Private (Admin only)
 */
router.put('/:id/untrust', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Device ID must be a positive integer')
], untrustDevice);

/**
 * @route DELETE /api/devices/:id
 * @desc Deactivate a device
 * @access Private (Admin only)
 */
router.delete('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Device ID must be a positive integer')
], deactivateDevice);

export default router;
