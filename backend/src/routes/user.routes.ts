import express from 'express';
import { body } from 'express-validator';
import {
  updateProfile,
  changePassword,
  getUsers,
  deleteUser,
} from '../controllers/user.controller';
import { authenticate, requireAdmin } from '../middleware/auth.new';

const router = express.Router();

// Update profile route
router.put(
  '/profile',
  authenticate,
  [
    body('name').optional().trim().notEmpty().withMessage('Name cannot be empty'),
    body('email').optional().isEmail().withMessage('Please enter a valid email'),
  ],
  updateProfile
);

// Change password route
router.put(
  '/change-password',
  authenticate,
  [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('New password must be at least 6 characters long'),
  ],
  changePassword
);

// Get all users route (admin only)
router.get('/', requireAdmin, getUsers);

// Delete user route (admin only)
router.delete('/:id', requireAdmin, deleteUser);

export default router; 