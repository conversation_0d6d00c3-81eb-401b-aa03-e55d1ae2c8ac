import { Router, Request, Response } from 'express';
import { sendEmail } from '../utils/email';
import { Message } from '../models/Message';

const router = Router();

// Get all messages
router.get('/', async (req: Request, res: Response) => {
  try {
    const messages = await Message.findAll();
    res.json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ error: 'Error fetching messages' });
  }
});

// Create a new message
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, email, subject, content } = req.body;

    // Validate required fields
    if (!name || !email || !subject || !content) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    // Create the message
    const message = await Message.create({
      name,
      email,
      subject,
      content,
      status: 'pending',
    });

    console.log(`New message created from ${name} (${email}): ${subject}`);
    res.status(201).json(message);
  } catch (error) {
    console.error('Error creating message:', error);
    res.status(500).json({ error: 'Error creating message' });
  }
});

// Get a single message
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const message = await Message.findById(Number(id));
    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }
    res.json(message);
  } catch (error) {
    console.error('Error fetching message:', error);
    res.status(500).json({ error: 'Error fetching message' });
  }
});

// Update message status
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status || !['pending', 'read', 'replied'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status value' });
    }

    const message = await Message.update(Number(id), { status });

    console.log(`Message ${id} status updated to ${status}`);
    res.json(message);
  } catch (error) {
    console.error('Error updating message:', error);
    res.status(500).json({ error: 'Error updating message' });
  }
});

// Delete a message
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await Message.delete(Number(id));
    res.json({ message: 'Message deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Error deleting message' });
  }
});

// Reply to a message
router.post('/:id/reply', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { reply } = req.body;

    if (!reply || reply.trim() === '') {
      return res.status(400).json({ error: 'Reply content is required' });
    }

    // Get the message to reply to
    const message = await Message.findById(Number(id));

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    // Update message status to replied
    await Message.update(Number(id), { status: 'replied' });

    // Send email reply
    try {
      await sendEmail({
        to: message.email,
        subject: `Re: ${message.subject}`,
        text: `Dear ${message.name},\n\n${reply}\n\nRegards,\nMaBourse Team`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4a6ee0;">Response to Your Message</h2>
            <p>Dear ${message.name},</p>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="white-space: pre-wrap;">${reply}</p>
            </div>
            <p>Original message: <em>${message.subject}</em></p>
            <p>Regards,<br>MaBourse Team</p>
          </div>
        `
      });

      console.log(`Reply sent to ${message.email}`);

      res.json({
        success: true,
        message: 'Reply sent successfully',
        messageId: message.id
      });
    } catch (emailError) {
      console.error('Error sending email reply:', emailError);
      // Still return success but with a warning
      res.status(200).json({
        success: true,
        warning: 'Message status updated but email delivery failed',
        messageId: message.id
      });
    }
  } catch (error) {
    console.error('Error replying to message:', error);
    res.status(500).json({ error: 'Error replying to message' });
  }
});

export default router;