import { Request, Response, NextFunction } from 'express';
// Removed Prisma import as we're using PostgreSQL directly
import logger from '../utils/logger';

// Define error types for better classification
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  DATABASE = 'DATABASE_ERROR',
  INTERNAL = 'INTERNAL_ERROR',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE_ERROR',
}

// Custom error class with type
export class AppError extends Error {
  type: ErrorType;
  statusCode: number;
  details?: any;

  constructor(message: string, type: ErrorType, statusCode: number, details?: any) {
    super(message);
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.name = 'AppError';
  }
}

// Middleware to handle errors
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Default error values
  let statusCode = 500;
  let errorType = ErrorType.INTERNAL;
  let message = 'Internal server error';
  let details = undefined;

  // Handle AppError instances
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    errorType = err.type;
    message = err.message;
    details = err.details;
  }
  // Handle PostgreSQL errors
  else if ((err as any).code && typeof (err as any).code === 'string') {
    errorType = ErrorType.DATABASE;

    // Handle specific PostgreSQL error codes
    switch ((err as any).code) {
      case '23505': // unique_violation
        statusCode = 409;
        message = 'A record with this data already exists';
        break;
      case '23503': // foreign_key_violation
        statusCode = 400;
        message = 'Referenced record does not exist';
        break;
      case '23502': // not_null_violation
        statusCode = 400;
        message = 'Required field is missing';
        break;
      default:
        message = 'Database error';
    }
  }
  // Handle validation errors from express-validator
  else if (err.name === 'ValidationError' || (err as any).errors) {
    statusCode = 400;
    errorType = ErrorType.VALIDATION;
    message = 'Validation error';
    details = (err as any).errors || err.message;
  }
  // Handle JWT errors
  else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    statusCode = 401;
    errorType = ErrorType.AUTHENTICATION;
    message = err.name === 'TokenExpiredError' ? 'Token expired' : 'Invalid token';
  }

  // Log the error
  if (statusCode >= 500) {
    logger.error(`[${errorType}] ${message}`, {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
        details,
      },
      request: {
        method: req.method,
        url: req.originalUrl,
        body: req.body,
        params: req.params,
        query: req.query,
      },
    });
  } else {
    logger.warn(`[${errorType}] ${message}`, {
      error: {
        name: err.name,
        message: err.message,
        details,
      },
      request: {
        method: req.method,
        url: req.originalUrl,
      },
    });
  }

  // Send response
  res.status(statusCode).json({
    error: {
      type: errorType,
      message,
      ...(details && { details }),
      ...(process.env.NODE_ENV !== 'production' && { stack: err.stack }),
    },
  });
};

// Middleware to handle 404 errors
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new AppError(
    `Not Found - ${req.originalUrl}`,
    ErrorType.NOT_FOUND,
    404
  );
  next(error);
};

// Utility function to wrap async route handlers
export const asyncHandler = (fn: Function) => (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
