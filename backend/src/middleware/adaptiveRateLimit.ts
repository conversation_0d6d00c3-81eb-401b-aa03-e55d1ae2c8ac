/**
 * Adaptive Rate Limiting Middleware
 * Implements intelligent rate limiting based on user behavior and risk assessment
 */

import { Request, Response, NextFunction } from 'express';
import { query } from '../config/database';
import { MLAnomalyDetectionEngine } from '../utils/mlAnomalyDetection';
import { SecurityMonitor } from '../utils/securityMonitor';
import { AuthEventType, SecurityLevel } from '../utils/authLogger';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
  onLimitReached?: (req: Request, res: Response) => void;
}

interface AdaptiveRateLimitOptions extends RateLimitConfig {
  baseConfig: RateLimitConfig;
  trustedUserMultiplier: number;
  suspiciousUserDivisor: number;
  enableBehaviorAnalysis: boolean;
}

class AdaptiveRateLimiter {
  private config: AdaptiveRateLimitOptions;
  private requestCounts: Map<string, { count: number; resetTime: number; riskLevel: string }> = new Map();

  constructor(config: AdaptiveRateLimitOptions) {
    this.config = config;
    
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Main middleware function
   */
  middleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const key = this.config.keyGenerator ? this.config.keyGenerator(req) : this.getDefaultKey(req);
      const now = Date.now();
      
      // Get or create request tracking
      let requestData = this.requestCounts.get(key);
      if (!requestData || now > requestData.resetTime) {
        requestData = {
          count: 0,
          resetTime: now + this.config.windowMs,
          riskLevel: 'low'
        };
        this.requestCounts.set(key, requestData);
      }

      // Increment request count
      requestData.count++;

      // Calculate adaptive limits based on user behavior
      const adaptiveLimits = await this.calculateAdaptiveLimits(req, requestData.riskLevel);
      
      // Check if limit exceeded
      if (requestData.count > adaptiveLimits.maxRequests) {
        // Log rate limit violation
        await this.logRateLimitViolation(req, requestData.count, adaptiveLimits.maxRequests);
        
        // Update risk level
        requestData.riskLevel = 'high';
        
        // Call custom handler if provided
        if (this.config.onLimitReached) {
          this.config.onLimitReached(req, res);
          return;
        }

        // Default rate limit response
        return res.status(429).json({
          success: false,
          message: 'Too many requests',
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
          limit: adaptiveLimits.maxRequests,
          remaining: 0,
          resetTime: new Date(requestData.resetTime).toISOString()
        });
      }

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': adaptiveLimits.maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, adaptiveLimits.maxRequests - requestData.count).toString(),
        'X-RateLimit-Reset': new Date(requestData.resetTime).toISOString(),
        'X-RateLimit-Window': this.config.windowMs.toString()
      });

      next();
    } catch (error) {
      console.error('Adaptive rate limiter error:', error);
      // Continue on error to avoid blocking legitimate requests
      next();
    }
  };

  /**
   * Calculate adaptive limits based on user behavior and risk assessment
   */
  private async calculateAdaptiveLimits(req: Request, currentRiskLevel: string) {
    const baseLimits = this.config.baseConfig;
    let multiplier = 1;

    try {
      // Get user information if authenticated
      const userEmail = req.user?.email;
      const ip = req.ip || 'unknown';

      if (this.config.enableBehaviorAnalysis && userEmail) {
        // Get user's behavioral profile and risk assessment
        const userProfile = await this.getUserRiskProfile(userEmail, ip);
        
        // Adjust limits based on user trust level
        if (userProfile.trustLevel === 'high') {
          multiplier = this.config.trustedUserMultiplier;
        } else if (userProfile.trustLevel === 'low' || currentRiskLevel === 'high') {
          multiplier = 1 / this.config.suspiciousUserDivisor;
        }

        // Additional adjustments based on recent behavior
        if (userProfile.recentViolations > 0) {
          multiplier *= 0.5; // Reduce limits for users with recent violations
        }

        if (userProfile.accountAge > 30) {
          multiplier *= 1.2; // Slightly increase limits for established accounts
        }
      } else {
        // For unauthenticated users, check IP reputation
        const ipRisk = await this.getIPRiskLevel(ip);
        if (ipRisk === 'high') {
          multiplier = 1 / this.config.suspiciousUserDivisor;
        }
      }

      // Apply time-based adjustments (stricter during off-hours)
      const hour = new Date().getHours();
      if (hour < 6 || hour > 22) {
        multiplier *= 0.7; // Reduce limits during off-hours
      }

    } catch (error) {
      console.error('Error calculating adaptive limits:', error);
      // Use base limits on error
    }

    return {
      maxRequests: Math.max(1, Math.floor(baseLimits.maxRequests * multiplier)),
      windowMs: baseLimits.windowMs
    };
  }

  /**
   * Get user risk profile for adaptive rate limiting
   */
  private async getUserRiskProfile(email: string, ip: string) {
    try {
      // Get user's recent activity and violations
      const userStats = await query(`
        SELECT 
          COUNT(*) FILTER (WHERE timestamp > NOW() - INTERVAL '24 hours') as requests_24h,
          COUNT(*) FILTER (WHERE timestamp > NOW() - INTERVAL '1 hour') as requests_1h,
          COUNT(*) FILTER (WHERE event_type LIKE '%RATE_LIMIT%') as rate_limit_violations,
          MIN(timestamp) as first_seen,
          AVG(risk_score) as avg_risk_score
        FROM security_events 
        WHERE email = $1 OR ip = $2
      `, [email, ip]);

      const stats = userStats.rows[0];
      const accountAge = stats.first_seen ? 
        Math.floor((Date.now() - new Date(stats.first_seen).getTime()) / (1000 * 60 * 60 * 24)) : 0;

      // Determine trust level
      let trustLevel = 'medium';
      const avgRiskScore = parseFloat(stats.avg_risk_score) || 50;
      const recentRequests = parseInt(stats.requests_24h) || 0;
      const violations = parseInt(stats.rate_limit_violations) || 0;

      if (avgRiskScore < 30 && violations === 0 && accountAge > 7) {
        trustLevel = 'high';
      } else if (avgRiskScore > 70 || violations > 2 || recentRequests > 1000) {
        trustLevel = 'low';
      }

      return {
        trustLevel,
        accountAge,
        recentViolations: violations,
        avgRiskScore,
        recentRequests
      };
    } catch (error) {
      console.error('Error getting user risk profile:', error);
      return {
        trustLevel: 'medium',
        accountAge: 0,
        recentViolations: 0,
        avgRiskScore: 50,
        recentRequests: 0
      };
    }
  }

  /**
   * Get IP risk level
   */
  private async getIPRiskLevel(ip: string): Promise<string> {
    try {
      // Check recent security events for this IP
      const ipEvents = await query(`
        SELECT 
          COUNT(*) as total_events,
          COUNT(*) FILTER (WHERE severity IN ('high', 'critical')) as high_risk_events,
          MAX(risk_score) as max_risk_score
        FROM security_events 
        WHERE ip = $1 AND timestamp > NOW() - INTERVAL '24 hours'
      `, [ip]);

      const events = ipEvents.rows[0];
      const totalEvents = parseInt(events.total_events) || 0;
      const highRiskEvents = parseInt(events.high_risk_events) || 0;
      const maxRiskScore = parseInt(events.max_risk_score) || 0;

      if (highRiskEvents > 5 || maxRiskScore > 80) {
        return 'high';
      } else if (totalEvents > 100 || maxRiskScore > 50) {
        return 'medium';
      }

      return 'low';
    } catch (error) {
      console.error('Error getting IP risk level:', error);
      return 'medium';
    }
  }

  /**
   * Log rate limit violation
   */
  private async logRateLimitViolation(req: Request, currentCount: number, limit: number) {
    try {
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.SUSPICIOUS_ACTIVITY,
        message: `Rate limit exceeded: ${currentCount}/${limit} requests`,
        email: req.user?.email || '',
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.WARNING,
        sessionId: req.sessionID,
        riskScore: Math.min(100, (currentCount / limit) * 50),
        details: {
          endpoint: req.path,
          method: req.method,
          currentCount,
          limit,
          userAgent: req.get('User-Agent')
        }
      });
    } catch (error) {
      console.error('Error logging rate limit violation:', error);
    }
  }

  /**
   * Generate default key for rate limiting
   */
  private getDefaultKey(req: Request): string {
    // Use user ID if authenticated, otherwise use IP
    if (req.user?.id) {
      return `user:${req.user.id}`;
    }
    return `ip:${req.ip}`;
  }

  /**
   * Clean up expired entries
   */
  private cleanup() {
    const now = Date.now();
    for (const [key, data] of this.requestCounts.entries()) {
      if (now > data.resetTime) {
        this.requestCounts.delete(key);
      }
    }
  }
}

/**
 * Create adaptive rate limiter with default configuration
 */
export function createAdaptiveRateLimit(options: Partial<AdaptiveRateLimitOptions> = {}): (req: Request, res: Response, next: NextFunction) => void {
  const defaultConfig: AdaptiveRateLimitOptions = {
    baseConfig: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100
    },
    windowMs: 15 * 60 * 1000,
    maxRequests: 100,
    trustedUserMultiplier: 2.0,
    suspiciousUserDivisor: 3.0,
    enableBehaviorAnalysis: true,
    ...options
  };

  const limiter = new AdaptiveRateLimiter(defaultConfig);
  return limiter.middleware;
}

/**
 * Predefined adaptive rate limiters for different endpoints
 */
export const adaptiveRateLimiters = {
  // Strict limits for authentication endpoints
  auth: createAdaptiveRateLimit({
    baseConfig: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 10
    },
    trustedUserMultiplier: 1.5,
    suspiciousUserDivisor: 2.0
  }),

  // Moderate limits for API endpoints
  api: createAdaptiveRateLimit({
    baseConfig: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100
    },
    trustedUserMultiplier: 2.0,
    suspiciousUserDivisor: 3.0
  }),

  // Lenient limits for public endpoints
  public: createAdaptiveRateLimit({
    baseConfig: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 200
    },
    trustedUserMultiplier: 1.5,
    suspiciousUserDivisor: 2.0
  })
};
