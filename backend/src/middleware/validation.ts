import { Request, Response, NextFunction } from 'express';
import { body, param, validationResult } from 'express-validator';

// Validation middleware to check for validation errors
export const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Scholarship validation rules
export const scholarshipValidationRules = {
  // Rules for creating a new scholarship
  create: [
    body('title')
      .notEmpty()
      .withMessage('Title is required')
      .isLength({ max: 200 })
      .withMessage('Title cannot exceed 200 characters'),
    
    body('description')
      .notEmpty()
      .withMessage('Description is required')
      .isLength({ max: 5000 })
      .withMessage('Description cannot exceed 5000 characters'),
    
    body('deadline')
      .notEmpty()
      .withMessage('Deadline is required')
      .isISO8601()
      .withMessage('Deadline must be a valid date'),
    
    body('level')
      .optional()
      .isIn(['Undergraduate', 'Graduate', 'PhD'])
      .withMessage('Level must be one of: Undergraduate, Graduate, PhD'),
    
    body('country')
      .optional()
      .isString()
      .withMessage('Country must be a string'),
    
    body('isOpen')
      .optional()
      .isBoolean()
      .withMessage('isOpen must be a boolean'),
    
    body('financial_benefits_summary')
      .optional()
      .isString()
      .withMessage('Financial benefits summary must be a string')
      .isLength({ max: 1000 })
      .withMessage('Financial benefits summary cannot exceed 1000 characters'),
    
    body('eligibility_summary')
      .optional()
      .isString()
      .withMessage('Eligibility summary must be a string')
      .isLength({ max: 1000 })
      .withMessage('Eligibility summary cannot exceed 1000 characters'),
    
    body('scholarship_link')
      .optional()
      .isURL()
      .withMessage('Scholarship link must be a valid URL'),
    
    body('youtube_link')
      .optional()
      .isURL()
      .withMessage('YouTube link must be a valid URL'),
    
    validateRequest,
  ],
  
  // Rules for updating an existing scholarship
  update: [
    param('id')
      .isInt()
      .withMessage('Scholarship ID must be an integer'),
    
    body('title')
      .optional()
      .isLength({ max: 200 })
      .withMessage('Title cannot exceed 200 characters'),
    
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('Description cannot exceed 5000 characters'),
    
    body('deadline')
      .optional()
      .isISO8601()
      .withMessage('Deadline must be a valid date'),
    
    body('level')
      .optional()
      .isIn(['Undergraduate', 'Graduate', 'PhD'])
      .withMessage('Level must be one of: Undergraduate, Graduate, PhD'),
    
    body('country')
      .optional()
      .isString()
      .withMessage('Country must be a string'),
    
    body('isOpen')
      .optional()
      .isBoolean()
      .withMessage('isOpen must be a boolean'),
    
    body('financial_benefits_summary')
      .optional()
      .isString()
      .withMessage('Financial benefits summary must be a string')
      .isLength({ max: 1000 })
      .withMessage('Financial benefits summary cannot exceed 1000 characters'),
    
    body('eligibility_summary')
      .optional()
      .isString()
      .withMessage('Eligibility summary must be a string')
      .isLength({ max: 1000 })
      .withMessage('Eligibility summary cannot exceed 1000 characters'),
    
    body('scholarship_link')
      .optional()
      .isURL()
      .withMessage('Scholarship link must be a valid URL'),
    
    body('youtube_link')
      .optional()
      .isURL()
      .withMessage('YouTube link must be a valid URL'),
    
    validateRequest,
  ],
  
  // Rules for deleting a scholarship
  delete: [
    param('id')
      .isInt()
      .withMessage('Scholarship ID must be an integer'),
    
    validateRequest,
  ],
};

// User validation rules
export const userValidationRules = {
  // Rules for creating a new user
  create: [
    body('name')
      .notEmpty()
      .withMessage('Name is required')
      .isLength({ max: 100 })
      .withMessage('Name cannot exceed 100 characters'),
    
    body('email')
      .notEmpty()
      .withMessage('Email is required')
      .isEmail()
      .withMessage('Email must be a valid email address'),
    
    body('password')
      .notEmpty()
      .withMessage('Password is required')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
    validateRequest,
  ],
  
  // Rules for updating a user
  update: [
    body('name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Name cannot exceed 100 characters'),
    
    body('email')
      .optional()
      .isEmail()
      .withMessage('Email must be a valid email address'),
    
    validateRequest,
  ],
  
  // Rules for changing password
  changePassword: [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    
    body('newPassword')
      .notEmpty()
      .withMessage('New password is required')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('New password must include at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
    validateRequest,
  ],
};

// Admin validation rules
export const adminValidationRules = {
  // Rules for creating a new admin
  create: [
    body('name')
      .notEmpty()
      .withMessage('Name is required')
      .isLength({ max: 100 })
      .withMessage('Name cannot exceed 100 characters'),
    
    body('email')
      .notEmpty()
      .withMessage('Email is required')
      .isEmail()
      .withMessage('Email must be a valid email address'),
    
    body('password')
      .notEmpty()
      .withMessage('Password is required')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
    body('privileges')
      .notEmpty()
      .withMessage('Privileges are required')
      .isArray()
      .withMessage('Privileges must be an array'),
    
    body('isMainAdmin')
      .optional()
      .isBoolean()
      .withMessage('isMainAdmin must be a boolean'),
    
    validateRequest,
  ],
  
  // Rules for updating an admin
  update: [
    body('name')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Name cannot exceed 100 characters'),
    
    body('email')
      .optional()
      .isEmail()
      .withMessage('Email must be a valid email address'),
    
    body('privileges')
      .optional()
      .isArray()
      .withMessage('Privileges must be an array'),
    
    body('isMainAdmin')
      .optional()
      .isBoolean()
      .withMessage('isMainAdmin must be a boolean'),
    
    validateRequest,
  ],
};
