/**
 * Request Signing Middleware
 * Implements cryptographic request verification for enhanced API security
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { query } from '../config/database';
import { SecurityMonitor } from '../utils/securityMonitor';
import { AuthEventType, SecurityLevel } from '../utils/authLogger';

interface SignedRequest extends Request {
  signature?: {
    valid: boolean;
    keyId: string;
    timestamp: number;
    nonce: string;
  };
}

interface APIKey {
  id: string;
  adminId: number;
  keyId: string;
  secretKey: string;
  name: string;
  permissions: string[];
  isActive: boolean;
  expiresAt?: Date;
  lastUsed?: Date;
  createdAt: Date;
}

class RequestSigningService {
  private static readonly SIGNATURE_HEADER = 'X-Signature';
  private static readonly TIMESTAMP_HEADER = 'X-Timestamp';
  private static readonly NONCE_HEADER = 'X-Nonce';
  private static readonly KEY_ID_HEADER = 'X-Key-ID';
  private static readonly ALGORITHM = 'sha256';
  private static readonly MAX_TIMESTAMP_SKEW = 5 * 60 * 1000; // 5 minutes
  private static readonly NONCE_CACHE_SIZE = 10000;
  
  private static usedNonces = new Set<string>();

  /**
   * Middleware to verify request signatures
   */
  static verifySignature = async (req: SignedRequest, res: Response, next: NextFunction) => {
    try {
      const signature = req.get(RequestSigningService.SIGNATURE_HEADER);
      const timestamp = req.get(RequestSigningService.TIMESTAMP_HEADER);
      const nonce = req.get(RequestSigningService.NONCE_HEADER);
      const keyId = req.get(RequestSigningService.KEY_ID_HEADER);

      // Check if all required headers are present
      if (!signature || !timestamp || !nonce || !keyId) {
        return RequestSigningService.handleSignatureError(
          req, res, 'Missing required signature headers', 'MISSING_HEADERS'
        );
      }

      // Verify timestamp (prevent replay attacks)
      const requestTime = parseInt(timestamp);
      const now = Date.now();
      if (Math.abs(now - requestTime) > RequestSigningService.MAX_TIMESTAMP_SKEW) {
        return RequestSigningService.handleSignatureError(
          req, res, 'Request timestamp is too old or too far in the future', 'INVALID_TIMESTAMP'
        );
      }

      // Verify nonce (prevent replay attacks)
      if (RequestSigningService.usedNonces.has(nonce)) {
        return RequestSigningService.handleSignatureError(
          req, res, 'Nonce has already been used', 'DUPLICATE_NONCE'
        );
      }

      // Get API key
      const apiKey = await RequestSigningService.getAPIKey(keyId);
      if (!apiKey) {
        return RequestSigningService.handleSignatureError(
          req, res, 'Invalid API key', 'INVALID_KEY'
        );
      }

      if (!apiKey.isActive) {
        return RequestSigningService.handleSignatureError(
          req, res, 'API key is disabled', 'DISABLED_KEY'
        );
      }

      if (apiKey.expiresAt && new Date() > apiKey.expiresAt) {
        return RequestSigningService.handleSignatureError(
          req, res, 'API key has expired', 'EXPIRED_KEY'
        );
      }

      // Generate expected signature
      const expectedSignature = RequestSigningService.generateSignature(
        req.method,
        req.originalUrl,
        JSON.stringify(req.body || {}),
        timestamp,
        nonce,
        apiKey.secretKey
      );

      // Verify signature
      if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
        return RequestSigningService.handleSignatureError(
          req, res, 'Invalid signature', 'INVALID_SIGNATURE'
        );
      }

      // Add nonce to used set (with cleanup)
      RequestSigningService.addUsedNonce(nonce);

      // Update API key last used timestamp
      await RequestSigningService.updateAPIKeyUsage(keyId);

      // Add signature info to request
      req.signature = {
        valid: true,
        keyId,
        timestamp: requestTime,
        nonce
      };

      // Log successful signature verification
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.LOGIN_SUCCESS,
        message: 'Request signature verified successfully',
        adminId: apiKey.adminId,
        email: '',
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.INFO,
        sessionId: (req as any).sessionID || 'unknown',
        details: {
          keyId,
          endpoint: req.originalUrl,
          method: req.method,
          signatureValid: true
        }
      });

      next();
    } catch (error) {
      console.error('Request signature verification error:', error);
      return RequestSigningService.handleSignatureError(
        req, res, 'Signature verification failed', 'VERIFICATION_ERROR'
      );
    }
  };

  /**
   * Generate HMAC signature for request
   */
  static generateSignature(
    method: string,
    url: string,
    body: string,
    timestamp: string,
    nonce: string,
    secretKey: string
  ): string {
    const stringToSign = [
      method.toUpperCase(),
      url,
      body,
      timestamp,
      nonce
    ].join('\n');

    return crypto
      .createHmac(RequestSigningService.ALGORITHM, secretKey)
      .update(stringToSign)
      .digest('hex');
  }

  /**
   * Get API key from database
   */
  private static async getAPIKey(keyId: string): Promise<APIKey | null> {
    try {
      const result = await query(`
        SELECT 
          id, admin_id, key_id, secret_key, name, permissions,
          is_active, expires_at, last_used, created_at
        FROM api_keys 
        WHERE key_id = $1
      `, [keyId]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        id: row.id,
        adminId: row.admin_id,
        keyId: row.key_id,
        secretKey: row.secret_key,
        name: row.name,
        permissions: row.permissions || [],
        isActive: row.is_active,
        expiresAt: row.expires_at,
        lastUsed: row.last_used,
        createdAt: row.created_at
      };
    } catch (error) {
      console.error('Error getting API key:', error);
      return null;
    }
  }

  /**
   * Update API key last used timestamp
   */
  private static async updateAPIKeyUsage(keyId: string): Promise<void> {
    try {
      await query(`
        UPDATE api_keys 
        SET last_used = CURRENT_TIMESTAMP 
        WHERE key_id = $1
      `, [keyId]);
    } catch (error) {
      console.error('Error updating API key usage:', error);
    }
  }

  /**
   * Add nonce to used set with cleanup
   */
  private static addUsedNonce(nonce: string): void {
    RequestSigningService.usedNonces.add(nonce);
    
    // Clean up old nonces if cache is too large
    if (RequestSigningService.usedNonces.size > RequestSigningService.NONCE_CACHE_SIZE) {
      const noncesToDelete = Array.from(RequestSigningService.usedNonces).slice(0, 1000);
      noncesToDelete.forEach(n => RequestSigningService.usedNonces.delete(n));
    }
  }

  /**
   * Handle signature verification errors
   */
  private static async handleSignatureError(
    req: Request,
    res: Response,
    message: string,
    errorCode: string
  ): Promise<void> {
    // Log security event
    await SecurityMonitor.logSecurityEvent({
      eventType: AuthEventType.LOGIN_FAILED,
      message: `Request signature verification failed: ${message}`,
      email: '',
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      severity: SecurityLevel.WARNING,
      sessionId: (req as any).sessionID || 'unknown',
      riskScore: 70,
      details: {
        errorCode,
        endpoint: req.originalUrl,
        method: req.method,
        headers: {
          signature: req.get('X-Signature') ? 'present' : 'missing',
          timestamp: req.get('X-Timestamp') ? 'present' : 'missing',
          nonce: req.get('X-Nonce') ? 'present' : 'missing',
          keyId: req.get('X-Key-ID') ? 'present' : 'missing'
        }
      }
    });

    res.status(401).json({
      success: false,
      message: 'Request signature verification failed',
      error: message,
      code: errorCode
    });
  }

  /**
   * Generate new API key pair
   */
  static async generateAPIKey(
    adminId: number,
    name: string,
    permissions: string[] = [],
    expiresAt?: Date
  ): Promise<{ keyId: string; secretKey: string }> {
    const keyId = 'ak_' + crypto.randomBytes(16).toString('hex');
    const secretKey = crypto.randomBytes(32).toString('hex');

    await query(`
      INSERT INTO api_keys (admin_id, key_id, secret_key, name, permissions, expires_at)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [adminId, keyId, secretKey, name, JSON.stringify(permissions), expiresAt]);

    return { keyId, secretKey };
  }

  /**
   * Revoke API key
   */
  static async revokeAPIKey(keyId: string): Promise<boolean> {
    try {
      const result = await query(`
        UPDATE api_keys 
        SET is_active = false 
        WHERE key_id = $1
      `, [keyId]);

      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error('Error revoking API key:', error);
      return false;
    }
  }

  /**
   * List API keys for admin
   */
  static async listAPIKeys(adminId: number): Promise<Partial<APIKey>[]> {
    try {
      const result = await query(`
        SELECT 
          key_id, name, permissions, is_active, expires_at, last_used, created_at
        FROM api_keys 
        WHERE admin_id = $1
        ORDER BY created_at DESC
      `, [adminId]);

      return result.rows.map(row => ({
        keyId: row.key_id,
        name: row.name,
        permissions: row.permissions || [],
        isActive: row.is_active,
        expiresAt: row.expires_at,
        lastUsed: row.last_used,
        createdAt: row.created_at
      }));
    } catch (error) {
      console.error('Error listing API keys:', error);
      return [];
    }
  }
}

/**
 * Middleware factory for optional signature verification
 */
export function optionalSignatureVerification() {
  return async (req: SignedRequest, res: Response, next: NextFunction) => {
    const hasSignatureHeaders = req.get('X-Signature') ||
                               req.get('X-Key-ID');

    if (hasSignatureHeaders) {
      // If signature headers are present, verify them
      return RequestSigningService.verifySignature(req, res, next);
    } else {
      // If no signature headers, continue without verification
      req.signature = { valid: false, keyId: '', timestamp: 0, nonce: '' };
      next();
    }
  };
}

export { RequestSigningService };
export default RequestSigningService.verifySignature;
