import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';

// Ensure upload directories exist
const createUploadDirectories = () => {
  const uploadDir = path.join(__dirname, '../../uploads');
  const scholarshipsDir = path.join(uploadDir, 'scholarships');

  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  if (!fs.existsSync(scholarshipsDir)) {
    fs.mkdirSync(scholarshipsDir, { recursive: true });
  }
};

// Create upload directories on module load
createUploadDirectories();

// Configure storage
const storage = multer.diskStorage({
  destination: (_req: Request, _file: Express.Multer.File, cb: any) => {
    cb(null, path.join(__dirname, '../../uploads/scholarships'));
  },
  filename: (_req: Request, file: Express.Multer.File, cb: any) => {
    // Create a secure unique filename with original extension
    // Use crypto for more secure random values than Math.random()
    const randomBytes = crypto.randomBytes(16).toString('hex');
    const timestamp = Date.now();
    const ext = path.extname(file.originalname).toLowerCase();

    // Sanitize the original filename to remove any potentially harmful characters
    const sanitizedName = path.basename(file.originalname, ext)
      .replace(/[^a-zA-Z0-9]/g, '_')
      .substring(0, 20); // Limit the length

    cb(null, `${sanitizedName}-${timestamp}-${randomBytes}${ext}`);
  }
});

// File filter function
const fileFilter = (_req: Request, file: Express.Multer.File, cb: any) => {
  // Accept only image files
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  const allowedExtensions = ['.jpg', '.jpeg', '.png'];

  // Check both mimetype and file extension for security
  const ext = path.extname(file.originalname).toLowerCase();

  console.log('File upload attempt:', {
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
    extension: ext
  });

  if (allowedMimeTypes.includes(file.mimetype) && allowedExtensions.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error(`Only .jpg, .jpeg, and .png files are allowed. Got mimetype: ${file.mimetype}, extension: ${ext}`), false);
  }
};

// Create multer upload instance
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
    files: 1 // Only allow 1 file per request
  },
  fileFilter
});

// Error handling middleware for multer errors
export const handleMulterError = (err: any, _req: Request, res: Response, next: NextFunction) => {
  if (err instanceof multer.MulterError) {
    // A Multer error occurred when uploading
    console.error('Multer error:', err);
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(413).json({
        error: 'File too large',
        message: 'File size should not exceed 5MB'
      });
    }
    return res.status(400).json({
      error: err.code,
      message: err.message
    });
  } else if (err) {
    // An unknown error occurred
    console.error('Upload error:', err);
    return res.status(500).json({
      error: 'upload_error',
      message: err.message
    });
  }

  // No error occurred, continue
  next();
};

// Export middleware for different use cases
export const uploadScholarshipThumbnail = upload.single('thumbnail');

// Helper function to process uploaded file and add path to request body
export const processUploadedFile = (req: Request, _res: Response, next: NextFunction) => {
  console.log('Processing uploaded file. Request file:', req.file);

  if (req.file) {
    // Add the file path to the request body
    req.body.thumbnail = `/uploads/scholarships/${req.file.filename}`;

    // Log successful upload
    console.log(`File uploaded successfully: ${req.file.filename}`);
    console.log('Updated request body with thumbnail path:', req.body.thumbnail);
  } else {
    console.log('No file was uploaded with this request');
  }
  next();
};

// Export combined middleware
export const handleScholarshipUpload = [
  uploadScholarshipThumbnail,
  handleMulterError,
  processUploadedFile
];
