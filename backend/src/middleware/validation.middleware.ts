/**
 * Enhanced Input Validation and Sanitization Middleware
 * 
 * This middleware provides comprehensive input validation and sanitization
 * to protect against common security vulnerabilities.
 */

import { Request, Response, NextFunction } from 'express';
import { validationResult, ValidationChain } from 'express-validator';
import { sendValidationError } from '../utils/apiResponse';
import { apiLogger } from '../utils/logger';
import xss from 'xss';

/**
 * Sanitize request body to prevent XSS attacks
 * @param req Express request
 */
export const sanitizeRequestBody = (req: Request, res: Response, next: NextFunction) => {
  if (req.body) {
    // Create a sanitized copy of the request body
    const sanitizedBody: any = {};
    
    // Recursively sanitize object properties
    const sanitizeObject = (obj: any, target: any) => {
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const value = obj[key];
          
          // Skip sanitization for specific fields
          const skipSanitization = [
            'password',
            'confirmPassword',
            'token',
            'resetPasswordToken',
            'twoFactorSecret',
            'twoFactorBackupCodes',
          ];
          
          if (skipSanitization.includes(key)) {
            target[key] = value;
            continue;
          }
          
          // Recursively sanitize nested objects
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            target[key] = {};
            sanitizeObject(value, target[key]);
          } 
          // Sanitize arrays
          else if (Array.isArray(value)) {
            target[key] = value.map(item => {
              if (typeof item === 'string') {
                return xss(item);
              } else if (item && typeof item === 'object') {
                const sanitizedItem = {};
                sanitizeObject(item, sanitizedItem);
                return sanitizedItem;
              }
              return item;
            });
          } 
          // Sanitize strings
          else if (typeof value === 'string') {
            target[key] = xss(value);
          } 
          // Keep other types as is
          else {
            target[key] = value;
          }
        }
      }
    };
    
    // Perform sanitization
    sanitizeObject(req.body, sanitizedBody);
    
    // Replace the original body with the sanitized version
    req.body = sanitizedBody;
  }
  
  next();
};

/**
 * Validate request parameters
 * @param validations Array of validation chains
 */
export const validate = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Execute all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    // Check for validation errors
    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }
    
    // Log validation errors
    apiLogger.warn('Validation errors', { 
      path: req.originalUrl, 
      method: req.method,
      errors: errors.array() 
    });
    
    // Return validation errors
    return sendValidationError(res, errors.array());
  };
};

/**
 * Validate and sanitize request parameters
 * @param validations Array of validation chains
 */
export const validateAndSanitize = (validations: ValidationChain[]) => {
  return [
    sanitizeRequestBody,
    validate(validations)
  ];
};

/**
 * Sanitize URL parameters to prevent XSS attacks
 */
export const sanitizeUrlParams = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize query parameters
  if (req.query) {
    for (const key in req.query) {
      if (Object.prototype.hasOwnProperty.call(req.query, key)) {
        const value = req.query[key];
        if (typeof value === 'string') {
          req.query[key] = xss(value);
        }
      }
    }
  }
  
  // Sanitize URL parameters
  if (req.params) {
    for (const key in req.params) {
      if (Object.prototype.hasOwnProperty.call(req.params, key)) {
        const value = req.params[key];
        if (typeof value === 'string') {
          req.params[key] = xss(value);
        }
      }
    }
  }
  
  next();
};

/**
 * Middleware to prevent common security vulnerabilities
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Set security headers
  res.set('X-Content-Type-Options', 'nosniff');
  res.set('X-Frame-Options', 'DENY');
  res.set('X-XSS-Protection', '1; mode=block');
  res.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.set('Content-Security-Policy', "default-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; script-src 'self'");
  res.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
};

export default {
  sanitizeRequestBody,
  validate,
  validateAndSanitize,
  sanitizeUrlParams,
  securityHeaders,
};
