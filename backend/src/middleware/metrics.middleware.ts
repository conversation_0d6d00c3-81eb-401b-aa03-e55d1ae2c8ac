import { Request, Response, NextFunction } from 'express';
import fs from 'fs';
import path from 'path';
import logger from '../utils/logger';

// Interface for request metrics
interface RequestMetric {
  method: string;
  path: string;
  statusCode: number;
  responseTime: number;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  userId?: number;
  userRole?: string;
}

// In-memory storage for metrics (will be periodically written to disk)
let requestMetrics: RequestMetric[] = [];
const METRICS_FLUSH_INTERVAL = 60 * 1000; // 1 minute
const MAX_METRICS_BEFORE_FLUSH = 100;

// Ensure metrics directory exists
const metricsDir = path.join(__dirname, '../../logs/metrics');
if (!fs.existsSync(metricsDir)) {
  fs.mkdirSync(metricsDir, { recursive: true });
}

// Function to write metrics to disk
const flushMetricsToDisk = () => {
  if (requestMetrics.length === 0) return;
  
  const date = new Date();
  const fileName = `metrics-${date.toISOString().split('T')[0]}.json`;
  const filePath = path.join(metricsDir, fileName);
  
  try {
    // Read existing metrics if file exists
    let existingMetrics: RequestMetric[] = [];
    if (fs.existsSync(filePath)) {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      existingMetrics = JSON.parse(fileContent);
    }
    
    // Combine existing and new metrics
    const allMetrics = [...existingMetrics, ...requestMetrics];
    
    // Write to file
    fs.writeFileSync(filePath, JSON.stringify(allMetrics, null, 2));
    
    // Clear in-memory metrics
    requestMetrics = [];
    
    logger.info(`Flushed ${requestMetrics.length} metrics to disk`, {
      category: 'metrics',
      file: fileName,
    });
  } catch (error) {
    logger.error(`Failed to flush metrics to disk: ${(error as Error).message}`, {
      category: 'metrics',
      error,
    });
  }
};

// Set up periodic flushing
setInterval(flushMetricsToDisk, METRICS_FLUSH_INTERVAL);

// Ensure metrics are flushed when the application exits
process.on('SIGINT', () => {
  flushMetricsToDisk();
  process.exit(0);
});

process.on('SIGTERM', () => {
  flushMetricsToDisk();
  process.exit(0);
});

// Middleware to collect request metrics
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Record start time
  const startTime = process.hrtime();
  
  // Store original end method
  const originalEnd = res.end;
  
  // Override end method to capture response metrics
  res.end = function(chunk?: any, encoding?: any, callback?: any) {
    // Calculate response time
    const hrtime = process.hrtime(startTime);
    const responseTimeMs = hrtime[0] * 1000 + hrtime[1] / 1000000;
    
    // Create metric object
    const metric: RequestMetric = {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      responseTime: parseFloat(responseTimeMs.toFixed(2)),
      timestamp: new Date(),
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      userId: (req as any).user?.id,
      userRole: (req as any).user?.role,
    };
    
    // Add to in-memory storage
    requestMetrics.push(metric);
    
    // Flush if we've accumulated enough metrics
    if (requestMetrics.length >= MAX_METRICS_BEFORE_FLUSH) {
      flushMetricsToDisk();
    }
    
    // Log slow responses
    if (responseTimeMs > 1000) {
      logger.warn(`Slow response detected: ${req.method} ${req.path} took ${responseTimeMs.toFixed(2)}ms`, {
        category: 'performance',
        metric,
      });
    }
    
    // Call original end method
    return originalEnd.call(this, chunk, encoding, callback);
  };
  
  next();
};

// Utility function to get metrics for a specific date
export const getMetricsForDate = (date: Date): RequestMetric[] => {
  const dateString = date.toISOString().split('T')[0];
  const filePath = path.join(metricsDir, `metrics-${dateString}.json`);
  
  if (!fs.existsSync(filePath)) {
    return [];
  }
  
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(fileContent);
  } catch (error) {
    logger.error(`Failed to read metrics file: ${(error as Error).message}`, {
      category: 'metrics',
      error,
    });
    return [];
  }
};

// Utility function to get metrics for a date range
export const getMetricsForDateRange = (startDate: Date, endDate: Date): RequestMetric[] => {
  const metrics: RequestMetric[] = [];
  
  // Clone start date to avoid modifying the original
  const currentDate = new Date(startDate);
  
  // Iterate through each day in the range
  while (currentDate <= endDate) {
    const dateMetrics = getMetricsForDate(currentDate);
    metrics.push(...dateMetrics);
    
    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return metrics;
};

export default metricsMiddleware;
