import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { sendError } from '../utils/apiResponse';

/**
 * CSRF token generation middleware
 * Generates a CSRF token and sets it in a cookie
 */
export const generateCsrfToken = (req: Request, res: Response, next: NextFunction) => {
  // Generate a random token
  const csrfToken = crypto.randomBytes(32).toString('hex');

  // Set the token in a cookie
  res.cookie('csrf_token', csrfToken, {
    httpOnly: false, // Must be accessible from JavaScript
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax', // Changed from strict to lax to allow cross-site navigation
    maxAge: 24 * 60 * 60 * 1000, // 1 day
    path: '/'
  });

  // Also send the token in the response for the client to store
  res.locals.csrfToken = csrfToken;

  next();
};

/**
 * CSRF token validation middleware
 * Validates the CSRF token from the request against the one in the cookie
 */
export const validateCsrfToken = (req: Request, res: Response, next: NextFunction) => {
  // Skip CSRF validation for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Get the token from the request header
  const headerToken = req.header('X-CSRF-Token');

  // Get the token from the cookie
  const cookieToken = req.cookies.csrf_token;

  // If either token is missing, return an error
  if (!headerToken || !cookieToken) {
    return sendError(
      res,
      'CSRF token validation failed',
      'Missing CSRF token',
      403
    );
  }

  // Compare the tokens
  if (headerToken !== cookieToken) {
    return sendError(
      res,
      'CSRF token validation failed',
      'Invalid CSRF token',
      403
    );
  }

  next();
};

export default {
  generateCsrfToken,
  validateCsrfToken
};
