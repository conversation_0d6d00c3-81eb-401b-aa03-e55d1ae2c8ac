/**
 * API Response Caching Middleware
 *
 * This middleware adds HTTP caching headers to API responses
 * and implements server-side caching for frequently accessed endpoints.
 */

import { Request, Response, NextFunction } from 'express';
import NodeCache from 'node-cache';
import { apiLogger, cacheLogger } from '../utils/logger';

// Cache configuration
const CACHE_TTL = 300; // Default cache TTL in seconds (5 minutes)
const CACHE_CHECK_PERIOD = 600; // Check for expired cache entries every 10 minutes

// Create a cache instance
const apiCache = new NodeCache({
  stdTTL: CACHE_TTL,
  checkperiod: CACHE_CHECK_PERIOD,
  useClones: false, // For better performance
});

// Cache statistics
const cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  invalidations: 0,
};

/**
 * Generate a cache key from request
 * @param req Express request
 * @returns Cache key
 */
const generateCacheKey = (req: Request): string => {
  return `${req.method}:${req.originalUrl}`;
};

/**
 * Check if a request should be cached
 * @param req Express request
 * @returns Whether the request should be cached
 */
const shouldCacheRequest = (req: Request): boolean => {
  // Only cache GET requests
  if (req.method !== 'GET') {
    return false;
  }

  // Don't cache authenticated requests
  if (req.user) {
    return false;
  }

  // Don't cache requests with query parameters that should invalidate cache
  if (req.query.nocache === 'true') {
    return false;
  }

  // Cache specific endpoints
  const cachableEndpoints = [
    '/api/scholarships',
    '/api/scholarships/search',
    '/api/scholarships/',
    '/api/health',
  ];

  return cachableEndpoints.some(endpoint => req.originalUrl.startsWith(endpoint));
};

/**
 * Get cache statistics
 * @returns Cache statistics
 */
export const getCacheStats = (): typeof cacheStats => {
  return { ...cacheStats };
};

/**
 * Clear the entire cache
 */
export const clearCache = (): void => {
  apiCache.flushAll();
  cacheLogger.clear({ type: 'api' });
};

/**
 * Invalidate cache for a specific path
 * @param path Path to invalidate
 */
export const invalidateCache = (path: string): void => {
  // Get all cache keys
  const keys = apiCache.keys();

  // Filter keys for the specified path
  const pathKeys = keys.filter(key => key.includes(path));

  // Delete matching keys
  if (pathKeys.length > 0) {
    apiCache.del(pathKeys);
    cacheStats.invalidations += pathKeys.length;
    cacheLogger.invalidate(path, { count: pathKeys.length, keys: pathKeys });
  }
};

/**
 * Middleware to add cache headers to responses
 * @param ttl Cache TTL in seconds
 */
export const addCacheHeaders = (ttl: number = CACHE_TTL) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip for non-GET requests or authenticated requests
    if (req.method !== 'GET' || req.user) {
      return next();
    }

    // Set cache headers
    res.set('Cache-Control', `public, max-age=${ttl}`);
    res.set('Expires', new Date(Date.now() + ttl * 1000).toUTCString());

    next();
  };
};

/**
 * Middleware to cache API responses
 * @param ttl Cache TTL in seconds
 */
export const cacheApiResponse = (ttl: number = CACHE_TTL) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip if request should not be cached
    if (!shouldCacheRequest(req)) {
      return next();
    }

    // Generate cache key
    const cacheKey = generateCacheKey(req);

    // Try to get from cache
    const cachedResponse = apiCache.get(cacheKey);
    if (cachedResponse) {
      cacheStats.hits++;
      cacheLogger.hit(cacheKey, { url: req.originalUrl, method: req.method });

      // Set cache headers
      res.set('Cache-Control', `public, max-age=${ttl}`);
      res.set('Expires', new Date(Date.now() + ttl * 1000).toUTCString());
      res.set('X-Cache', 'HIT');

      // Send cached response
      return res.status(200).json(cachedResponse);
    }

    // Cache miss
    cacheStats.misses++;
    cacheLogger.miss(cacheKey, { url: req.originalUrl, method: req.method });

    // Store original send method
    const originalSend = res.json;

    // Override send method to cache response
    res.json = function(body: any): Response {
      // Set cache headers
      res.set('Cache-Control', `public, max-age=${ttl}`);
      res.set('Expires', new Date(Date.now() + ttl * 1000).toUTCString());
      res.set('X-Cache', 'MISS');

      // Cache the response if it's successful
      if (res.statusCode >= 200 && res.statusCode < 300) {
        apiCache.set(cacheKey, body, ttl);
        cacheStats.sets++;
        cacheLogger.set(cacheKey, ttl, { url: req.originalUrl, method: req.method });
      }

      // Call original send method
      return originalSend.call(this, body);
    };

    next();
  };
};

export default {
  addCacheHeaders,
  cacheApiResponse,
  invalidateCache,
  getCacheStats,
  clearCache,
};
