/**
 * Rate Limiting Middleware
 *
 * This middleware implements rate limiting for API endpoints to prevent abuse.
 * It uses different limits for different types of requests and user roles.
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { getEnv } from '../utils/envValidator';
import { sendError } from '../utils/apiResponse';

// Default rate limit window in milliseconds (1 minute)
const DEFAULT_WINDOW_MS = 60 * 1000;

// Default maximum number of requests per window
const DEFAULT_MAX_REQUESTS = 100;

// Get rate limit configuration from environment variables
const RATE_LIMIT_WINDOW_MS = parseInt(getEnv('RATE_LIMIT_WINDOW_MS', DEFAULT_WINDOW_MS.toString()));
const RATE_LIMIT_MAX_REQUESTS = parseInt(getEnv('RATE_LIMIT_MAX_REQUESTS', DEFAULT_MAX_REQUESTS.toString()));

/**
 * Create a rate limiter with custom configuration
 * @param windowMs Time window in milliseconds
 * @param max Maximum number of requests per window
 * @param message Error message to display when rate limit is exceeded
 * @returns Rate limiter middleware
 */
const createRateLimiter = (
  windowMs: number = RATE_LIMIT_WINDOW_MS,
  max: number = RATE_LIMIT_MAX_REQUESTS,
  message: string = 'Too many requests, please try again later.'
) => {
  return rateLimit({
    windowMs,
    max,
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req: Request, res: Response) => {
      sendError(
        res,
        message,
        'Rate limit exceeded',
        429
      );
    },
    // Skip rate limiting for trusted IPs or admin users
    skip: (req: Request) => {
      // Skip for admin users
      if (req.user && (req.user.role === 'admin' || req.user.role === 'super_admin')) {
        return true;
      }

      // Skip for trusted IPs (e.g., internal network)
      const trustedIps = ['127.0.0.1', '::1'];
      if (req.ip && trustedIps.includes(req.ip)) {
        return true;
      }

      return false;
    },
  });
};

/**
 * General API rate limiter
 * Limits all API requests to prevent abuse
 */
export const apiLimiter = createRateLimiter(
  RATE_LIMIT_WINDOW_MS,
  RATE_LIMIT_MAX_REQUESTS,
  'Too many requests, please try again later.'
);

/**
 * Authentication rate limiter
 * More strict limits for authentication endpoints to prevent brute force attacks
 */
export const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 requests per 15 minutes
  'Too many login attempts, please try again later.'
);

/**
 * Password reset rate limiter
 * Strict limits for password reset to prevent abuse
 */
export const passwordResetLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  3, // 3 requests per hour
  'Too many password reset attempts, please try again later.'
);

/**
 * User registration rate limiter
 * Limits user registration to prevent spam accounts
 */
export const registrationLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  5, // 5 registrations per hour
  'Too many registration attempts, please try again later.'
);

/**
 * Contact form rate limiter
 * Limits contact form submissions to prevent spam
 */
export const contactFormLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  5, // 5 submissions per hour
  'Too many contact form submissions, please try again later.'
);

export default {
  apiLimiter,
  authLimiter,
  passwordResetLimiter,
  registrationLimiter,
  contactFormLimiter,
};
