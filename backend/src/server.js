const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;
require('dotenv').config();
const bodyParser = require('body-parser');
const nodemailer = require('nodemailer');
const multer = require('multer');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(bodyParser.json());

// Create necessary directories if they don't exist
async function createDirectories() {
    const dirs = [
        path.join(__dirname, '../frontend'),
        path.join(__dirname, '../frontend/css'),
        path.join(__dirname, '../frontend/js'),
        path.join(__dirname, '../frontend/assets'),
        path.join(__dirname, '../frontend/pages'),
        path.join(__dirname, '../database'),
        path.join(__dirname, '../uploads/scholarships')
    ];

    for (const dir of dirs) {
        try {
            await fs.access(dir);
        } catch {
            await fs.mkdir(dir, { recursive: true });
        }
    }
}

// Initialize database files
async function initializeDB() {
    const dbFiles = [
        { path: path.join(__dirname, '../database/scholarships.json'), data: [] },
        { path: path.join(__dirname, '../database/newsletter.json'), data: [] }
    ];

    for (const file of dbFiles) {
        try {
            await fs.access(file.path);
        } catch {
            await fs.writeFile(file.path, JSON.stringify(file.data, null, 2));
        }
    }
}

// Multer config
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../uploads/scholarships'));
  },
  filename: function (req, file, cb) {
    const ext = path.extname(file.originalname);
    const uniqueName = Date.now() + '-' + Math.round(Math.random() * 1E9) + ext;
    cb(null, uniqueName);
  }
});
const upload = multer({
  storage: storage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB
  fileFilter: (req, file, cb) => {
    const allowed = ['.jpg', '.jpeg', '.png'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowed.includes(ext)) cb(null, true);
    else cb(new Error('Only .jpg, .jpeg, .png files are allowed!'));
  }
});

// Routes
const scholarshipsRouter = require('./routes/scholarships');
const newsletterRouter = require('./routes/newsletter');

app.use('/api/scholarships', scholarshipsRouter);
app.use('/api/newsletter', newsletterRouter);

// Serve static files from the frontend directory
app.use(express.static(path.join(__dirname, '../frontend')));

// Serve uploads statically
app.use('/uploads/scholarships', express.static(path.join(__dirname, '../uploads/scholarships')));

// Serve index.html for the root route
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/pages/index.html'));
});

// Serve other pages
app.get('/:page', (req, res) => {
    const page = req.params.page;
    const validPages = ['about', 'scholarships', 'services', 'contact'];
    
    if (validPages.includes(page)) {
        res.sendFile(path.join(__dirname, `../frontend/pages/${page}.html`));
    } else {
        res.status(404).sendFile(path.join(__dirname, '../frontend/pages/404.html'));
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', message: 'Server is running' });
});

// Email transporter (configure with your SMTP or use SendGrid/Mailgun)
const transporter = nodemailer.createTransport({
    service: 'gmail', // or your provider
    auth: {
        user: '<EMAIL>',
        pass: 'your_email_password'
    }
});

// Subscribe endpoint
app.post('/api/newsletter/subscribe', (req, res) => {
    const { email } = req.body;
    const subs = readJson(SUBSCRIBERS_FILE);
    if (!subs.includes(email)) {
        subs.push(email);
        writeJson(SUBSCRIBERS_FILE, subs);
    }
    res.json({ success: true });
});

// Update POST /api/scholarships to handle file upload
app.post('/api/scholarships', upload.single('thumbnail'), async (req, res) => {
  const scholarship = req.body;
  if (req.file) {
    scholarship.thumbnail = `/uploads/scholarships/${req.file.filename}`;
  } else {
    scholarship.thumbnail = '/assets/default-thumbnail.jpg';
  }
  const scholarships = readJson(SCHOLARSHIPS_FILE);
  scholarships.unshift(scholarship);
  writeJson(SCHOLARSHIPS_FILE, scholarships);

  // Send email to all subscribers
  const subs = readJson(SUBSCRIBERS_FILE);
  subs.forEach(email => {
    transporter.sendMail({
      from: '<EMAIL>',
      to: email,
      subject: `Nouvelle bourse: ${scholarship.title}`,
      text: `Une nouvelle bourse a été publiée: ${scholarship.title}\n\n${scholarship.description}\n\nVoir plus sur le site!`
    }, (err, info) => {
      if (err) console.error('Email error:', err);
    });
  });

  res.json({ success: true });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// Initialize server
const PORT = process.env.PORT || 3000;

async function startServer() {
    try {
        await createDirectories();
        await initializeDB();
        
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
        });
    } catch (error) {
        console.error('Error starting server:', error);
        process.exit(1);
    }
}

startServer(); 