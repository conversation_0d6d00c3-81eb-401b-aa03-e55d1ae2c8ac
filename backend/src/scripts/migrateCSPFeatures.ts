#!/usr/bin/env ts-node

/**
 * Migration script for Advanced CSP Features
 * Adds system configuration table and CSP-related settings
 */

import { query, closeDatabase, initializeDatabase } from '../config/database';

async function migrateCSPFeatures() {
  try {
    console.log('🔄 Starting Advanced CSP Features migration...');
    
    // Initialize database connection
    await initializeDatabase();

    // Create system configuration table
    console.log('Creating system_config table...');
    await query(`
      CREATE TABLE IF NOT EXISTS system_config (
        id SERIAL PRIMARY KEY,
        key VARCHAR(255) NOT NULL UNIQUE,
        value JSONB NOT NULL,
        description TEXT,
        updated_by INTEGER REFERENCES admins(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create indexes for system config table
    console.log('Creating indexes for system_config...');
    await query(`
      CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);
      CREATE INDEX IF NOT EXISTS idx_system_config_updated_by ON system_config(updated_by);
      CREATE INDEX IF NOT EXISTS idx_system_config_updated_at ON system_config(updated_at);
    `);

    // Insert default CSP configuration
    console.log('Setting up default CSP configuration...');
    const defaultCSPConfig = {
      enableNonce: true,
      strictMode: process.env.NODE_ENV === 'production',
      reportOnly: process.env.NODE_ENV !== 'production',
      allowedDomains: ['self'],
      trustedScriptSources: ['self'],
      trustedStyleSources: ['self', 'unsafe-inline'],
      allowInlineStyles: false,
      allowInlineScripts: false,
      allowEval: false,
      upgradeInsecureRequests: process.env.NODE_ENV === 'production',
      reportUri: '/api/security/csp-violation'
    };

    try {
      await query(`
        INSERT INTO system_config (key, value, description)
        VALUES ('csp_config', $1, 'Content Security Policy configuration')
      `, [JSON.stringify(defaultCSPConfig)]);
      console.log('✅ Default CSP configuration inserted');
    } catch (error) {
      console.log('CSP configuration already exists, skipping...');
    }

    // Insert default security settings
    console.log('Setting up default security settings...');
    const securitySettings = [
      {
        key: 'security_headers_enabled',
        value: { enabled: true },
        description: 'Enable additional security headers'
      },
      {
        key: 'rate_limiting_config',
        value: {
          enabled: true,
          adaptiveThresholds: true,
          strictMode: process.env.NODE_ENV === 'production'
        },
        description: 'Rate limiting configuration'
      },
      {
        key: 'request_signing_config',
        value: {
          enabled: true,
          requireSignature: false,
          maxTimestampSkew: 300000
        },
        description: 'Request signing configuration'
      },
      {
        key: 'anomaly_detection_config',
        value: {
          enabled: true,
          mlAnalysis: true,
          behavioralProfiling: true,
          adaptiveRiskThresholds: true
        },
        description: 'ML-based anomaly detection configuration'
      }
    ];

    for (const setting of securitySettings) {
      try {
        await query(`
          INSERT INTO system_config (key, value, description)
          VALUES ($1, $2, $3)
        `, [setting.key, JSON.stringify(setting.value), setting.description]);
        console.log(`✅ ${setting.key} configuration inserted`);
      } catch (error) {
        console.log(`${setting.key} configuration already exists, skipping...`);
      }
    }

    // Create CSP violation summary view
    console.log('Creating CSP violation summary view...');
    await query(`
      CREATE OR REPLACE VIEW csp_violation_summary AS
      SELECT
        DATE_TRUNC('day', timestamp) as date,
        COUNT(*) as total_violations,
        COUNT(DISTINCT ip) as unique_ips,
        COUNT(DISTINCT (details::jsonb->'violation'->>'violated-directive')) as unique_directives,
        AVG(risk_score) as avg_risk_score,
        COUNT(*) FILTER (WHERE risk_score > 70) as high_risk_violations,
        COUNT(*) FILTER (WHERE (details::jsonb->'suspiciousContent') IS NOT NULL) as potential_xss_attempts
      FROM security_events
      WHERE event_type = 'SUSPICIOUS_ACTIVITY'
        AND message LIKE '%CSP violation%'
        AND timestamp > NOW() - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', timestamp)
      ORDER BY date DESC;
    `);

    // Create security configuration view
    await query(`
      CREATE OR REPLACE VIEW security_configuration_view AS
      SELECT 
        sc.key,
        sc.value,
        sc.description,
        sc.updated_at,
        a.email as updated_by_email
      FROM system_config sc
      LEFT JOIN admins a ON a.id = sc.updated_by
      WHERE sc.key LIKE '%_config'
      ORDER BY sc.updated_at DESC;
    `);

    // Add CSP-related columns to security_events if they don't exist
    console.log('Enhancing security_events table for CSP tracking...');
    try {
      await query(`
        ALTER TABLE security_events 
        ADD COLUMN IF NOT EXISTS csp_violation_type VARCHAR(100),
        ADD COLUMN IF NOT EXISTS blocked_uri TEXT,
        ADD COLUMN IF NOT EXISTS violated_directive VARCHAR(100),
        ADD COLUMN IF NOT EXISTS source_file TEXT,
        ADD COLUMN IF NOT EXISTS line_number INTEGER,
        ADD COLUMN IF NOT EXISTS column_number INTEGER;
      `);

      // Create indexes for new CSP columns
      await query(`
        CREATE INDEX IF NOT EXISTS idx_security_events_csp_violation_type ON security_events(csp_violation_type);
        CREATE INDEX IF NOT EXISTS idx_security_events_violated_directive ON security_events(violated_directive);
        CREATE INDEX IF NOT EXISTS idx_security_events_blocked_uri ON security_events USING hash(blocked_uri);
      `);

      console.log('✅ Security events table enhanced for CSP tracking');
    } catch (error) {
      console.log('CSP columns already exist in security_events table');
    }

    // Create CSP analytics function
    console.log('Creating CSP analytics functions...');
    await query(`
      CREATE OR REPLACE FUNCTION get_csp_analytics(days_back INTEGER DEFAULT 7)
      RETURNS TABLE (
        violation_date DATE,
        total_violations BIGINT,
        unique_ips BIGINT,
        top_directive TEXT,
        avg_risk_score NUMERIC
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          DATE(se.timestamp) as violation_date,
          COUNT(*) as total_violations,
          COUNT(DISTINCT se.ip) as unique_ips,
          MODE() WITHIN GROUP (ORDER BY (se.details::jsonb->'violation'->>'violated-directive')) as top_directive,
          ROUND(AVG(se.risk_score), 2) as avg_risk_score
        FROM security_events se
        WHERE se.event_type = 'SUSPICIOUS_ACTIVITY' 
          AND se.message LIKE '%CSP violation%'
          AND se.timestamp > NOW() - INTERVAL '1 day' * days_back
        GROUP BY DATE(se.timestamp)
        ORDER BY violation_date DESC;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Insert sample CSP violation patterns for testing (if in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Inserting sample CSP violation patterns for development...');
      const sampleViolations = [
        {
          violatedDirective: 'script-src',
          blockedUri: 'inline',
          description: 'Inline script execution blocked'
        },
        {
          violatedDirective: 'style-src',
          blockedUri: 'unsafe-inline',
          description: 'Unsafe inline style blocked'
        },
        {
          violatedDirective: 'img-src',
          blockedUri: 'data:',
          description: 'Data URI image blocked'
        }
      ];

      for (const violation of sampleViolations) {
        try {
          await query(`
            INSERT INTO system_config (key, value, description)
            VALUES ($1, $2, $3)
          `, [
            `csp_violation_pattern_${violation.violatedDirective}`,
            JSON.stringify(violation),
            `Sample CSP violation pattern for ${violation.violatedDirective}`
          ]);
        } catch (error) {
          // Pattern already exists, skip
        }
      }
    }

    console.log('✅ Advanced CSP Features migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error migrating Advanced CSP Features:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateCSPFeatures()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default migrateCSPFeatures;
