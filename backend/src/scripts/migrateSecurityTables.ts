import { query, initializeDatabase, closeDatabase } from '../config/database';

/**
 * Migrate existing security tables to new enhanced schema
 */
async function migrateSecurityTables() {
  try {
    console.log('🔧 Migrating security tables to enhanced schema...');
    
    await initializeDatabase();

    // Check if security_events table exists and add missing columns
    console.log('Updating security_events table...');
    
    await query(`
      ALTER TABLE security_events 
      ADD COLUMN IF NOT EXISTS admin_id INTEGER,
      ADD COLUMN IF NOT EXISTS details JSONB,
      ADD COLUMN IF NOT EXISTS session_id VARCHAR(255),
      ADD COLUMN IF NOT EXISTS request_id VARCHAR(255),
      ADD COLUMN IF NOT EXISTS geolocation JSONB,
      ADD COLUMN IF NOT EXISTS device_fingerprint VARCHAR(255),
      ADD COLUMN IF NOT EXISTS risk_score INTEGER DEFAULT 0,
      ADD COLUMN IF NOT EXISTS resolved BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS resolved_by INTEGER,
      ADD COLUMN IF NOT EXISTS resolved_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS resolution_notes TEXT;
    `);

    // Create new security tables if they don't exist
    console.log('Creating new security tables...');

    // Login attempts table
    await query(`
      CREATE TABLE IF NOT EXISTS login_attempts (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        ip VARCHAR(45) NOT NULL,
        user_agent TEXT,
        success BOOLEAN NOT NULL,
        failure_reason VARCHAR(100),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        geolocation JSONB,
        device_fingerprint VARCHAR(255),
        session_id VARCHAR(255)
      );
    `);

    // Trusted devices table
    await query(`
      CREATE TABLE IF NOT EXISTS trusted_devices (
        id SERIAL PRIMARY KEY,
        user_id INTEGER,
        admin_id INTEGER,
        device_fingerprint VARCHAR(255) NOT NULL,
        device_name VARCHAR(255),
        device_type VARCHAR(50),
        browser VARCHAR(100),
        os VARCHAR(100),
        first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        trusted BOOLEAN DEFAULT FALSE,
        trusted_at TIMESTAMP,
        ip_addresses JSONB,
        is_active BOOLEAN DEFAULT TRUE
      );
    `);

    // Security alerts table
    await query(`
      CREATE TABLE IF NOT EXISTS security_alerts (
        id SERIAL PRIMARY KEY,
        alert_type VARCHAR(100) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        severity VARCHAR(20) NOT NULL,
        user_id INTEGER,
        admin_id INTEGER,
        ip VARCHAR(45),
        triggered_by_event_id INTEGER,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        acknowledged BOOLEAN DEFAULT FALSE,
        acknowledged_by INTEGER,
        acknowledged_at TIMESTAMP,
        resolved BOOLEAN DEFAULT FALSE,
        resolved_by INTEGER,
        resolved_at TIMESTAMP,
        resolution_notes TEXT,
        metadata JSONB
      );
    `);

    // Create indexes for new tables
    console.log('Creating indexes...');

    // Security events indexes
    await query(`
      CREATE INDEX IF NOT EXISTS idx_security_events_admin_id ON security_events(admin_id);
      CREATE INDEX IF NOT EXISTS idx_security_events_session_id ON security_events(session_id);
      CREATE INDEX IF NOT EXISTS idx_security_events_risk_score ON security_events(risk_score);
      CREATE INDEX IF NOT EXISTS idx_security_events_resolved ON security_events(resolved);
    `);

    // Login attempts indexes
    await query(`
      CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
      CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip);
      CREATE INDEX IF NOT EXISTS idx_login_attempts_timestamp ON login_attempts(timestamp);
      CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);
      CREATE INDEX IF NOT EXISTS idx_login_attempts_email_ip ON login_attempts(email, ip);
    `);

    // Trusted devices indexes
    await query(`
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id ON trusted_devices(user_id);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_admin_id ON trusted_devices(admin_id);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_fingerprint ON trusted_devices(device_fingerprint);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_trusted ON trusted_devices(trusted);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_active ON trusted_devices(is_active);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_last_seen ON trusted_devices(last_seen);
    `);

    // Security alerts indexes
    await query(`
      CREATE INDEX IF NOT EXISTS idx_security_alerts_type ON security_alerts(alert_type);
      CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);
      CREATE INDEX IF NOT EXISTS idx_security_alerts_user_id ON security_alerts(user_id);
      CREATE INDEX IF NOT EXISTS idx_security_alerts_admin_id ON security_alerts(admin_id);
      CREATE INDEX IF NOT EXISTS idx_security_alerts_timestamp ON security_alerts(timestamp);
      CREATE INDEX IF NOT EXISTS idx_security_alerts_acknowledged ON security_alerts(acknowledged);
      CREATE INDEX IF NOT EXISTS idx_security_alerts_resolved ON security_alerts(resolved);
    `);

    // Migrate existing data if needed
    console.log('Migrating existing data...');

    // Convert existing details column from TEXT to JSONB if needed
    await query(`
      UPDATE security_events 
      SET details = CASE 
        WHEN details IS NULL THEN '{}'::jsonb
        WHEN details::text = '' THEN '{}'::jsonb
        ELSE 
          CASE 
            WHEN details::text ~ '^\\{.*\\}$' THEN details::jsonb
            ELSE json_build_object('message', details)::jsonb
          END
      END
      WHERE details IS NOT NULL;
    `);

    console.log('✅ Security tables migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error migrating security tables:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// Run if called directly
if (require.main === module) {
  migrateSecurityTables()
    .then(() => {
      console.log('🔐 Security tables migration completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Security tables migration failed:', error);
      process.exit(1);
    });
}

export { migrateSecurityTables };
