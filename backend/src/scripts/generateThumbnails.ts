/**
 * Generate Thumbnails Script
 * 
 * This script generates thumbnails for existing scholarship images
 * following industry standards for image optimization.
 */

import fs from 'fs';
import path from 'path';
import { ImageService } from '../services/imageService';
import { query, initializeDatabase } from '../config/database';

interface ScholarshipImage {
  id: number;
  title: string;
  thumbnail: string;
}

async function generateThumbnailsForExistingImages() {
  console.log('🚀 Starting thumbnail generation for existing images...');
  
  try {
    // Initialize database and image service
    initializeDatabase();
    await ImageService.initialize();
    
    // Get all scholarships with thumbnails
    const result = await query<ScholarshipImage>(`
      SELECT id, title, thumbnail 
      FROM scholarships 
      WHERE thumbnail IS NOT NULL 
      AND thumbnail != ''
      AND thumbnail LIKE '/uploads/scholarships/%'
    `);
    
    const scholarships = result.rows;
    console.log(`📊 Found ${scholarships.length} scholarships with images to process`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const scholarship of scholarships) {
      try {
        console.log(`\n🔄 Processing: ${scholarship.title} (ID: ${scholarship.id})`);
        
        // Construct full file path
        const relativePath = scholarship.thumbnail.replace('/uploads/', '');
        const fullPath = path.join(__dirname, '../../uploads', relativePath);
        
        // Check if original file exists
        if (!fs.existsSync(fullPath)) {
          console.log(`❌ Original file not found: ${fullPath}`);
          errorCount++;
          continue;
        }
        
        // Validate the image
        const validation = await ImageService.validateImage(fullPath);
        if (!validation.isValid) {
          console.log(`❌ Invalid image: ${validation.error}`);
          errorCount++;
          continue;
        }
        
        console.log(`✅ Image validation passed: ${validation.metadata?.width}x${validation.metadata?.height} ${validation.metadata?.format}`);
        
        // Extract filename without extension for thumbnail naming
        const filename = path.basename(scholarship.thumbnail);
        const filenameWithoutExt = filename.replace(/\.[^/.]+$/, '');
        
        // Generate thumbnails
        const thumbnailResult = await ImageService.generateThumbnails(fullPath, filenameWithoutExt);
        
        if (thumbnailResult.success && thumbnailResult.thumbnails) {
          console.log(`✅ Generated thumbnails:`, Object.keys(thumbnailResult.thumbnails));
          successCount++;
        } else {
          console.log(`❌ Failed to generate thumbnails: ${thumbnailResult.error}`);
          errorCount++;
        }
        
      } catch (error) {
        console.error(`❌ Error processing ${scholarship.title}:`, error);
        errorCount++;
      }
    }
    
    console.log(`\n📈 Thumbnail generation completed!`);
    console.log(`✅ Successful: ${successCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log(`📊 Total processed: ${scholarships.length}`);
    
  } catch (error) {
    console.error('❌ Fatal error during thumbnail generation:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  generateThumbnailsForExistingImages()
    .then(() => {
      console.log('🎉 Thumbnail generation script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Thumbnail generation script failed:', error);
      process.exit(1);
    });
}

export { generateThumbnailsForExistingImages };
