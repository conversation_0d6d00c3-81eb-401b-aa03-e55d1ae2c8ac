import { PrismaClient } from '@prisma/client';
import sequelize from '../config/database';
import { Scholarship } from '../models/scholarship.model';

const prisma = new PrismaClient();

async function cleanupDuplicateData() {
  try {
    console.log('Starting duplicate data cleanup...');
    
    // Connect to databases
    await sequelize.authenticate();
    console.log('Sequelize database connection established successfully.');
    
    // Check for duplicate scholarships in Sequelize
    const sequelizeScholarships = await Scholarship.findAll();
    console.log(`Found ${sequelizeScholarships.length} scholarship(s) in Sequelize database.`);
    
    // Group scholarships by title to find duplicates
    const scholarshipsByTitle: Record<string, any[]> = {};
    sequelizeScholarships.forEach(scholarship => {
      const title = scholarship.title;
      if (!scholarshipsByTitle[title]) {
        scholarshipsByTitle[title] = [];
      }
      scholarshipsByTitle[title].push(scholarship);
    });
    
    // Find titles with multiple scholarships
    const duplicateTitles = Object.keys(scholarshipsByTitle).filter(title => 
      scholarshipsByTitle[title].length > 1
    );
    
    if (duplicateTitles.length > 0) {
      console.log(`Found ${duplicateTitles.length} scholarship title(s) with duplicates in Sequelize.`);
      
      // Process each duplicate title
      for (const title of duplicateTitles) {
        const duplicates = scholarshipsByTitle[title];
        console.log(`Title "${title}" has ${duplicates.length} duplicate entries.`);
        
        // Keep the newest scholarship and remove the rest
        duplicates.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        const scholarshipToKeep = duplicates[0];
        console.log(`Keeping scholarship with ID: ${scholarshipToKeep.id} (created: ${scholarshipToKeep.createdAt})`);
        
        // Remove other scholarships
        for (let i = 1; i < duplicates.length; i++) {
          const scholarshipToRemove = duplicates[i];
          console.log(`Removing duplicate scholarship with ID: ${scholarshipToRemove.id} (created: ${scholarshipToRemove.createdAt})`);
          await scholarshipToRemove.destroy();
        }
      }
      
      console.log('Duplicate scholarships removed from Sequelize database.');
    } else {
      console.log('No duplicate scholarships found in Sequelize database.');
    }
    
    // Check for duplicate scholarships in Prisma
    const prismaScholarships = await prisma.scholarship.findMany();
    console.log(`Found ${prismaScholarships.length} scholarship(s) in Prisma database.`);
    
    // Group scholarships by title to find duplicates
    const prismaScholarshipsByTitle: Record<string, any[]> = {};
    prismaScholarships.forEach(scholarship => {
      const title = scholarship.title;
      if (!prismaScholarshipsByTitle[title]) {
        prismaScholarshipsByTitle[title] = [];
      }
      prismaScholarshipsByTitle[title].push(scholarship);
    });
    
    // Find titles with multiple scholarships
    const prismaDuplicateTitles = Object.keys(prismaScholarshipsByTitle).filter(title => 
      prismaScholarshipsByTitle[title].length > 1
    );
    
    if (prismaDuplicateTitles.length > 0) {
      console.log(`Found ${prismaDuplicateTitles.length} scholarship title(s) with duplicates in Prisma.`);
      
      // Process each duplicate title
      for (const title of prismaDuplicateTitles) {
        const duplicates = prismaScholarshipsByTitle[title];
        console.log(`Title "${title}" has ${duplicates.length} duplicate entries.`);
        
        // Keep the newest scholarship and remove the rest
        duplicates.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        const scholarshipToKeep = duplicates[0];
        console.log(`Keeping scholarship with ID: ${scholarshipToKeep.id} (created: ${scholarshipToKeep.createdAt})`);
        
        // Remove other scholarships
        for (let i = 1; i < duplicates.length; i++) {
          const scholarshipToRemove = duplicates[i];
          console.log(`Removing duplicate scholarship with ID: ${scholarshipToRemove.id} (created: ${scholarshipToRemove.createdAt})`);
          await prisma.scholarship.delete({
            where: { id: scholarshipToRemove.id }
          });
        }
      }
      
      console.log('Duplicate scholarships removed from Prisma database.');
    } else {
      console.log('No duplicate scholarships found in Prisma database.');
    }
    
    // Check for duplicate messages in Prisma
    const messages = await prisma.message.findMany();
    console.log(`Found ${messages.length} message(s) in Prisma database.`);
    
    // Group messages by content to find duplicates
    const messagesByContent: Record<string, any[]> = {};
    messages.forEach(message => {
      const content = message.content;
      if (!messagesByContent[content]) {
        messagesByContent[content] = [];
      }
      messagesByContent[content].push(message);
    });
    
    // Find contents with multiple messages
    const duplicateContents = Object.keys(messagesByContent).filter(content => 
      messagesByContent[content].length > 1
    );
    
    if (duplicateContents.length > 0) {
      console.log(`Found ${duplicateContents.length} message content(s) with duplicates.`);
      
      // Process each duplicate content
      for (const content of duplicateContents) {
        const duplicates = messagesByContent[content];
        console.log(`Content "${content.substring(0, 30)}..." has ${duplicates.length} duplicate entries.`);
        
        // Keep the newest message and remove the rest
        duplicates.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        const messageToKeep = duplicates[0];
        console.log(`Keeping message with ID: ${messageToKeep.id} (created: ${messageToKeep.createdAt})`);
        
        // Remove other messages
        for (let i = 1; i < duplicates.length; i++) {
          const messageToRemove = duplicates[i];
          console.log(`Removing duplicate message with ID: ${messageToRemove.id} (created: ${messageToRemove.createdAt})`);
          await prisma.message.delete({
            where: { id: messageToRemove.id }
          });
        }
      }
      
      console.log('Duplicate messages removed from database.');
    } else {
      console.log('No duplicate messages found in database.');
    }
    
    console.log('Duplicate data cleanup completed successfully.');
  } catch (error) {
    console.error('Error cleaning up duplicate data:', error);
  } finally {
    await prisma.$disconnect();
    await sequelize.close();
  }
}

// Run the cleanup
cleanupDuplicateData();
