import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function fixAdminPrivileges() {
  try {
    console.log('Checking admin privileges...');
    
    // Get all admins
    const admins = await prisma.admin.findMany();
    console.log(`Found ${admins.length} admin(s) in the database.`);
    
    for (const admin of admins) {
      console.log(`Checking admin: ${admin.name} (${admin.email}), role: ${admin.role}`);
      
      // Check if privileges is a valid JSON string
      let privileges: string[] = [];
      let needsUpdate = false;
      
      if (typeof admin.privileges === 'string') {
        try {
          // Try to parse the privileges
          privileges = JSON.parse(admin.privileges);
          console.log(`  Current privileges: ${privileges.join(', ')}`);
        } catch (error) {
          console.log(`  Invalid privileges format: ${admin.privileges}`);
          needsUpdate = true;
          
          // Set default privileges based on role
          if (admin.isMainAdmin || admin.role === 'super_admin') {
            privileges = ['all'];
          } else if (admin.role === 'admin') {
            privileges = ['manage_scholarships', 'manage_messages', 'view_analytics'];
          } else {
            privileges = ['manage_scholarships', 'view_analytics'];
          }
        }
      } else if (Array.isArray(admin.privileges)) {
        privileges = admin.privileges;
        console.log(`  Current privileges: ${privileges.join(', ')}`);
      } else {
        console.log('  No privileges found, setting defaults');
        needsUpdate = true;
        
        // Set default privileges based on role
        if (admin.isMainAdmin || admin.role === 'super_admin') {
          privileges = ['all'];
        } else if (admin.role === 'admin') {
          privileges = ['manage_scholarships', 'manage_messages', 'view_analytics'];
        } else {
          privileges = ['manage_scholarships', 'view_analytics'];
        }
      }
      
      // Check if password is properly hashed
      let passwordNeedsUpdate = false;
      if (!admin.password.startsWith('$2a$') && !admin.password.startsWith('$2b$')) {
        console.log('  Password is not properly hashed. Will update.');
        passwordNeedsUpdate = true;
      }
      
      // Update admin if needed
      if (needsUpdate || passwordNeedsUpdate) {
        const updateData: any = {};
        
        if (needsUpdate) {
          updateData.privileges = JSON.stringify(privileges);
          console.log(`  Updating privileges to: ${privileges.join(', ')}`);
        }
        
        if (passwordNeedsUpdate) {
          // Use the current password or default to 'admin123' if it's the main admin
          const passwordToHash = admin.isMainAdmin ? 'admin123' : admin.password;
          updateData.password = await bcrypt.hash(passwordToHash, 10);
          console.log('  Updating password with proper hashing');
        }
        
        await prisma.admin.update({
          where: { id: admin.id },
          data: updateData
        });
        
        console.log('  Admin updated successfully');
      } else {
        console.log('  No updates needed');
      }
    }
    
    console.log('Admin privileges check completed.');
  } catch (error) {
    console.error('Error fixing admin privileges:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
fixAdminPrivileges();
