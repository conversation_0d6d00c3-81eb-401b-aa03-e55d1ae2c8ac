import sequelize from '../config/database';
import Admin from '../models/Admin';
import User from '../models/User';
import { Scholarship } from '../models/scholarship.model';

async function checkDatabase() {
  try {
    // Ensure database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check Admin table
    const admins = await Admin.findAll();
    console.log(`Found ${admins.length} admin(s):`);
    admins.forEach(admin => {
      console.log(`- ${admin.name} (${admin.email}), isMainAdmin: ${admin.isMainAdmin}`);
    });

    // Check User table
    const users = await User.findAll();
    console.log(`\nFound ${users.length} user(s):`);
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}), role: ${user.role}`);
    });

    // Check Scholarship table
    const scholarships = await Scholarship.findAll();
    console.log(`\nFound ${scholarships.length} scholarship(s):`);
    scholarships.forEach(scholarship => {
      console.log(`- ${scholarship.title}, createdBy: ${scholarship.createdBy}`);
    });

  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await sequelize.close();
  }
}

checkDatabase();
