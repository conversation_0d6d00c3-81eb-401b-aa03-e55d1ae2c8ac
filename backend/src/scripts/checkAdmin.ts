import bcrypt from 'bcryptjs';
import { Admin } from '../models/Admin';
import { initializeDatabase, closeDatabase } from '../config/database';

async function checkAdmin() {
  try {
    // Initialize database connection
    await initializeDatabase();

    console.log('Checking admin accounts...');

    // Get all admins
    const admins = await Admin.findAll();

    if (admins.length === 0) {
      console.log('No admin accounts found in database');
      return;
    }

    console.log(`Found ${admins.length} admin account(s):`);
    
    for (const admin of admins) {
      console.log('\n--- Admin Account ---');
      console.log('ID:', admin.id);
      console.log('Name:', admin.name);
      console.log('Email:', admin.email);
      console.log('Role:', admin.role);
      console.log('Is Main Admin:', admin.isMainAdmin);
      console.log('Failed Login Attempts:', admin.failedLoginAttempts);
      console.log('Lock Until:', admin.lockUntil);
      console.log('Two Factor Enabled:', admin.twoFactorEnabled);
      console.log('Created At:', admin.createdAt);
      
      // Test password against common passwords
      const testPasswords = ['admin123', 'Zw1@x$y{l8pomVF6', 'password123'];
      
      for (const testPassword of testPasswords) {
        if (admin.password) {
          const isMatch = await bcrypt.compare(testPassword, admin.password);
          if (isMatch) {
            console.log(`✅ Password matches: ${testPassword}`);
            break;
          }
        }
      }
    }

  } catch (error) {
    console.error('Error checking admin accounts:', error);
  } finally {
    await closeDatabase();
  }
}

checkAdmin();
