#!/usr/bin/env ts-node

/**
 * Migration script for ML Behavioral Patterns System
 * Adds behavioral patterns table for machine learning anomaly detection
 */

import { query, closeDatabase, initializeDatabase } from '../config/database';

async function migrateBehavioralPatterns() {
  try {
    console.log('🔄 Starting ML Behavioral Patterns migration...');
    
    // Initialize database connection
    await initializeDatabase();

    // Create behavioral patterns table
    console.log('Creating admin_behavioral_patterns table...');
    await query(`
      CREATE TABLE IF NOT EXISTS admin_behavioral_patterns (
        id SERIAL PRIMARY KEY,
        admin_id INTEGER NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
        login_hour INTEGER NOT NULL, -- 0-23
        login_day INTEGER NOT NULL, -- 0-6 (Sunday=0)
        country VARCHAR(100),
        device_type VARCHAR(50),
        browser VARCHAR(100),
        success_count INTEGER DEFAULT 0,
        total_count INTEGER DEFAULT 0,
        confidence_score DECIMAL(3,2) DEFAULT 0.5,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(admin_id, login_hour, login_day, country, device_type, browser)
      );
    `);

    // Create indexes for better performance
    console.log('Creating indexes for behavioral patterns...');
    await query(`
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_admin_id ON admin_behavioral_patterns(admin_id);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_hour ON admin_behavioral_patterns(login_hour);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_day ON admin_behavioral_patterns(login_day);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_country ON admin_behavioral_patterns(country);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_device ON admin_behavioral_patterns(device_type);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_browser ON admin_behavioral_patterns(browser);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_confidence ON admin_behavioral_patterns(confidence_score);
      CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_updated ON admin_behavioral_patterns(last_updated);
    `);

    // Populate initial behavioral patterns from existing login data
    console.log('Populating initial behavioral patterns from login history...');
    
    const loginData = await query(`
      SELECT
        a.id as admin_id,
        EXTRACT(HOUR FROM la.timestamp) as login_hour,
        EXTRACT(DOW FROM la.timestamp) as login_day,
        COALESCE(la.geolocation->>'country', 'unknown') as country,
        CASE
          WHEN LOWER(la.user_agent) LIKE '%mobile%' OR LOWER(la.user_agent) LIKE '%android%' OR LOWER(la.user_agent) LIKE '%iphone%' THEN 'mobile'
          WHEN LOWER(la.user_agent) LIKE '%tablet%' OR LOWER(la.user_agent) LIKE '%ipad%' THEN 'tablet'
          ELSE 'desktop'
        END as device_type,
        CASE
          WHEN LOWER(la.user_agent) LIKE '%chrome%' THEN 'chrome'
          WHEN LOWER(la.user_agent) LIKE '%firefox%' THEN 'firefox'
          WHEN LOWER(la.user_agent) LIKE '%safari%' THEN 'safari'
          WHEN LOWER(la.user_agent) LIKE '%edge%' THEN 'edge'
          ELSE 'unknown'
        END as browser,
        la.success,
        COUNT(*) as occurrence_count
      FROM login_attempts la
      INNER JOIN admins a ON a.email = la.email
      WHERE la.timestamp > NOW() - INTERVAL '90 days'
      GROUP BY
        a.id,
        EXTRACT(HOUR FROM la.timestamp),
        EXTRACT(DOW FROM la.timestamp),
        COALESCE(la.geolocation->>'country', 'unknown'),
        CASE
          WHEN LOWER(la.user_agent) LIKE '%mobile%' OR LOWER(la.user_agent) LIKE '%android%' OR LOWER(la.user_agent) LIKE '%iphone%' THEN 'mobile'
          WHEN LOWER(la.user_agent) LIKE '%tablet%' OR LOWER(la.user_agent) LIKE '%ipad%' THEN 'tablet'
          ELSE 'desktop'
        END,
        CASE
          WHEN LOWER(la.user_agent) LIKE '%chrome%' THEN 'chrome'
          WHEN LOWER(la.user_agent) LIKE '%firefox%' THEN 'firefox'
          WHEN LOWER(la.user_agent) LIKE '%safari%' THEN 'safari'
          WHEN LOWER(la.user_agent) LIKE '%edge%' THEN 'edge'
          ELSE 'unknown'
        END,
        la.success
      ORDER BY a.id, occurrence_count DESC
    `);

    console.log(`Found ${loginData.rows.length} behavioral patterns to process`);

    // Group by pattern key and aggregate success/total counts
    const patternMap = new Map();
    
    for (const row of loginData.rows) {
      const key = `${row.admin_id}-${row.login_hour}-${row.login_day}-${row.country}-${row.device_type}-${row.browser}`;
      
      if (!patternMap.has(key)) {
        patternMap.set(key, {
          admin_id: row.admin_id,
          login_hour: parseInt(row.login_hour),
          login_day: parseInt(row.login_day),
          country: row.country,
          device_type: row.device_type,
          browser: row.browser,
          success_count: 0,
          total_count: 0
        });
      }
      
      const pattern = patternMap.get(key);
      const count = parseInt(row.occurrence_count);
      
      if (row.success) {
        pattern.success_count += count;
      }
      pattern.total_count += count;
    }

    // Insert aggregated patterns
    let insertedCount = 0;
    for (const pattern of patternMap.values()) {
      // Calculate confidence score based on success rate and frequency
      const successRate = pattern.total_count > 0 ? pattern.success_count / pattern.total_count : 0;
      const frequencyScore = Math.min(1, pattern.total_count / 10); // Normalize frequency
      const confidenceScore = Math.round((successRate * 0.7 + frequencyScore * 0.3) * 100) / 100;

      try {
        await query(`
          INSERT INTO admin_behavioral_patterns (
            admin_id, login_hour, login_day, country, device_type, browser,
            success_count, total_count, confidence_score
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          ON CONFLICT (admin_id, login_hour, login_day, country, device_type, browser)
          DO UPDATE SET 
            success_count = EXCLUDED.success_count,
            total_count = EXCLUDED.total_count,
            confidence_score = EXCLUDED.confidence_score,
            last_updated = CURRENT_TIMESTAMP
        `, [
          pattern.admin_id,
          pattern.login_hour,
          pattern.login_day,
          pattern.country,
          pattern.device_type,
          pattern.browser,
          pattern.success_count,
          pattern.total_count,
          confidenceScore
        ]);
        
        insertedCount++;
      } catch (error) {
        console.error(`Error inserting pattern for admin ${pattern.admin_id}:`, error);
      }
    }

    console.log(`✅ Inserted/updated ${insertedCount} behavioral patterns`);

    // Create a summary view for easy querying
    console.log('Creating behavioral summary view...');
    await query(`
      CREATE OR REPLACE VIEW admin_behavioral_summary AS
      SELECT 
        admin_id,
        COUNT(*) as total_patterns,
        AVG(confidence_score) as avg_confidence,
        SUM(total_count) as total_logins,
        SUM(success_count) as successful_logins,
        ROUND(SUM(success_count)::decimal / NULLIF(SUM(total_count), 0) * 100, 2) as success_rate_percent,
        MAX(last_updated) as last_pattern_update
      FROM admin_behavioral_patterns
      GROUP BY admin_id;
    `);

    console.log('✅ ML Behavioral Patterns migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error migrating ML Behavioral Patterns:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateBehavioralPatterns()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default migrateBehavioralPatterns;
