import PasswordResetService from '../services/passwordResetService';
import { initializeDatabase, closeDatabase } from '../config/database';

async function init() {
  try {
    console.log('Initializing password reset service...');
    await initializeDatabase();
    await PasswordResetService.initializeTable();
    console.log('✅ Password reset service initialized');
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await closeDatabase();
  }
}

init();
