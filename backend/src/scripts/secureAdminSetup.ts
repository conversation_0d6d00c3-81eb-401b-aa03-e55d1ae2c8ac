import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { query, initializeDatabase, closeDatabase } from '../config/database';
import { validatePasswordStrength, generateSecurePassword, addPasswordToHistory } from '../utils/passwordPolicy';
import { logAuthEvent, AuthEventType, SecurityLevel } from '../utils/authLogger';

interface AdminSetupOptions {
  email?: string;
  name?: string;
  password?: string;
  forcePasswordChange?: boolean;
  generatePassword?: boolean;
}

/**
 * ENTERPRISE-GRADE SECURE ADMIN SETUP
 * Replaces all weak password scripts with secure password generation
 */
export class SecureAdminSetup {
  
  /**
   * Create or update main admin with secure password
   */
  static async setupMainAdmin(options: AdminSetupOptions = {}): Promise<{
    success: boolean;
    message: string;
    credentials?: { email: string; password: string; };
    mustChangePassword: boolean;
  }> {
    try {
      const adminEmail = options.email || process.env.ADMIN_EMAIL || '<EMAIL>';
      const adminName = options.name || process.env.ADMIN_NAME || 'Main Administrator';
      
      // Check if main admin already exists
      const existingAdmin = await query(
        'SELECT * FROM admins WHERE email = $1 OR is_main_admin = TRUE',
        [adminEmail]
      );

      let password: string;
      let mustChangePassword = true;

      if (options.password) {
        // Validate provided password
        const validation = validatePasswordStrength(options.password);
        if (!validation.valid) {
          return {
            success: false,
            message: `Password validation failed: ${validation.message}`,
            mustChangePassword: false
          };
        }
        password = options.password;
        mustChangePassword = options.forcePasswordChange !== false;
      } else if (options.generatePassword !== false) {
        // Generate secure password
        password = generateSecurePassword(16);
        mustChangePassword = true;
      } else {
        return {
          success: false,
          message: 'No password provided and password generation disabled',
          mustChangePassword: false
        };
      }

      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 12); // Increased rounds for better security
      const now = new Date();
      const passwordExpiresAt = new Date(now.getTime() + (90 * 24 * 60 * 60 * 1000)); // 90 days

      if (existingAdmin.rows.length > 0) {
        // Update existing admin
        const admin = existingAdmin.rows[0];
        
        // Check if password is being reused
        const isReused = await bcrypt.compare(password, admin.password);
        if (isReused && !options.generatePassword) {
          return {
            success: false,
            message: 'Cannot reuse the current password',
            mustChangePassword: false
          };
        }

        await query(`
          UPDATE admins 
          SET password = $1, 
              name = $2,
              role = 'super_admin',
              privileges = $3,
              is_main_admin = TRUE,
              password_updated_at = CURRENT_TIMESTAMP,
              password_expires_at = $4,
              must_change_password = $5,
              failed_login_attempts = 0,
              lock_until = NULL,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $6
        `, [
          hashedPassword,
          adminName,
          JSON.stringify(['all']),
          passwordExpiresAt,
          mustChangePassword,
          admin.id
        ]);

        // Add to password history
        await addPasswordToHistory(admin.id, hashedPassword, true);

        // Log security event
        await logAuthEvent(
          AuthEventType.PASSWORD_CHANGED,
          'Main admin password updated securely',
          {
            adminId: admin.id,
            email: adminEmail,
            passwordGenerated: options.generatePassword !== false,
            mustChangePassword
          },
          SecurityLevel.INFO
        );

        console.log('✅ Main admin updated successfully');
        
      } else {
        // Create new main admin
        const result = await query(`
          INSERT INTO admins (
            name, email, password, role, privileges, is_main_admin,
            password_updated_at, password_expires_at, must_change_password,
            failed_login_attempts, two_factor_enabled, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id
        `, [
          adminName,
          adminEmail,
          hashedPassword,
          'super_admin',
          JSON.stringify(['all']),
          true,
          now,
          passwordExpiresAt,
          mustChangePassword,
          0,
          false
        ]);

        const adminId = result.rows[0].id;

        // Add to password history
        await addPasswordToHistory(adminId, hashedPassword, true);

        // Log security event
        await logAuthEvent(
          AuthEventType.ADMIN_CREATED,
          'Main admin created securely',
          {
            adminId,
            email: adminEmail,
            passwordGenerated: options.generatePassword !== false,
            mustChangePassword
          },
          SecurityLevel.INFO
        );

        console.log('✅ Main admin created successfully');
      }

      // Return credentials only if password was generated
      const credentials = options.generatePassword !== false ? {
        email: adminEmail,
        password: password
      } : undefined;

      return {
        success: true,
        message: 'Main admin setup completed successfully',
        credentials,
        mustChangePassword
      };

    } catch (error) {
      console.error('❌ Error setting up main admin:', error);
      
      // Log security event
      await logAuthEvent(
        AuthEventType.ADMIN_SETUP_FAILED,
        'Main admin setup failed',
        { error: (error as Error).message },
        SecurityLevel.ERROR
      );

      return {
        success: false,
        message: `Setup failed: ${(error as Error).message}`,
        mustChangePassword: false
      };
    }
  }

  /**
   * Create additional admin with secure password
   */
  static async createAdmin(
    email: string,
    name: string,
    role: 'admin' | 'super_admin' = 'admin',
    options: { generatePassword?: boolean; password?: string } = {}
  ): Promise<{
    success: boolean;
    message: string;
    credentials?: { email: string; password: string; };
  }> {
    try {
      // Check if admin already exists
      const existingAdmin = await query('SELECT id FROM admins WHERE email = $1', [email]);
      
      if (existingAdmin.rows.length > 0) {
        return {
          success: false,
          message: 'Admin already exists with this email'
        };
      }

      let password: string;

      if (options.password) {
        // Validate provided password
        const validation = validatePasswordStrength(options.password);
        if (!validation.valid) {
          return {
            success: false,
            message: `Password validation failed: ${validation.message}`
          };
        }
        password = options.password;
      } else {
        // Generate secure password
        password = generateSecurePassword(16);
      }

      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 12);
      const now = new Date();
      const passwordExpiresAt = new Date(now.getTime() + (90 * 24 * 60 * 60 * 1000));

      // Determine privileges based on role
      const privileges = role === 'super_admin' ? ['all'] : ['manage_scholarships', 'manage_messages'];

      // Create admin
      const result = await query(`
        INSERT INTO admins (
          name, email, password, role, privileges, is_main_admin,
          password_updated_at, password_expires_at, must_change_password,
          failed_login_attempts, two_factor_enabled, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
        name,
        email,
        hashedPassword,
        role,
        JSON.stringify(privileges),
        false, // Only main admin can be is_main_admin
        now,
        passwordExpiresAt,
        true, // Always require password change for new admins
        0,
        false
      ]);

      const adminId = result.rows[0].id;

      // Add to password history
      await addPasswordToHistory(adminId, hashedPassword, true);

      // Log security event
      await logAuthEvent(
        AuthEventType.ADMIN_CREATED,
        `Admin created: ${role}`,
        {
          adminId,
          email,
          role,
          passwordGenerated: !options.password
        },
        SecurityLevel.INFO
      );

      console.log(`✅ Admin created successfully: ${email}`);

      return {
        success: true,
        message: 'Admin created successfully',
        credentials: {
          email,
          password
        }
      };

    } catch (error) {
      console.error('❌ Error creating admin:', error);
      
      // Log security event
      await logAuthEvent(
        AuthEventType.ADMIN_CREATION_FAILED,
        'Admin creation failed',
        { email, error: (error as Error).message },
        SecurityLevel.ERROR
      );

      return {
        success: false,
        message: `Admin creation failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Reset admin password securely
   */
  static async resetAdminPassword(
    email: string,
    options: { generatePassword?: boolean; password?: string } = {}
  ): Promise<{
    success: boolean;
    message: string;
    credentials?: { email: string; password: string; };
  }> {
    try {
      // Find admin
      const adminResult = await query('SELECT * FROM admins WHERE email = $1', [email]);
      
      if (adminResult.rows.length === 0) {
        return {
          success: false,
          message: 'Admin not found'
        };
      }

      const admin = adminResult.rows[0];
      let password: string;

      if (options.password) {
        // Validate provided password
        const validation = validatePasswordStrength(options.password);
        if (!validation.valid) {
          return {
            success: false,
            message: `Password validation failed: ${validation.message}`
          };
        }
        password = options.password;
      } else {
        // Generate secure password
        password = generateSecurePassword(16);
      }

      // Check if password is being reused
      const isReused = await bcrypt.compare(password, admin.password);
      if (isReused) {
        return {
          success: false,
          message: 'Cannot reuse the current password'
        };
      }

      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 12);
      const passwordExpiresAt = new Date(Date.now() + (90 * 24 * 60 * 60 * 1000));

      // Update admin password
      await query(`
        UPDATE admins 
        SET password = $1,
            password_updated_at = CURRENT_TIMESTAMP,
            password_expires_at = $2,
            must_change_password = TRUE,
            failed_login_attempts = 0,
            lock_until = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [hashedPassword, passwordExpiresAt, admin.id]);

      // Add to password history
      await addPasswordToHistory(admin.id, hashedPassword, true);

      // Log security event
      await logAuthEvent(
        AuthEventType.PASSWORD_RESET,
        'Admin password reset securely',
        {
          adminId: admin.id,
          email,
          passwordGenerated: !options.password
        },
        SecurityLevel.WARNING
      );

      console.log(`✅ Admin password reset successfully: ${email}`);

      return {
        success: true,
        message: 'Admin password reset successfully',
        credentials: {
          email,
          password
        }
      };

    } catch (error) {
      console.error('❌ Error resetting admin password:', error);
      
      // Log security event
      await logAuthEvent(
        AuthEventType.PASSWORD_RESET_FAILED,
        'Admin password reset failed',
        { email, error: (error as Error).message },
        SecurityLevel.ERROR
      );

      return {
        success: false,
        message: `Password reset failed: ${(error as Error).message}`
      };
    }
  }
}

// CLI interface for running the script directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];

  // Initialize database connection
  const initAndRun = async () => {
    try {
      await initializeDatabase();

      switch (command) {
        case 'setup-main':
          const result = await SecureAdminSetup.setupMainAdmin({ generatePassword: true });

          console.log('\n🔐 SECURE ADMIN SETUP RESULT:');
          console.log('Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
          console.log('Message:', result.message);

          if (result.credentials) {
            console.log('\n🔑 LOGIN CREDENTIALS:');
            console.log('Email:', result.credentials.email);
            console.log('Password:', result.credentials.password);
            console.log('\n⚠️  IMPORTANT: Save these credentials securely and change the password after first login!');
          }

          await closeDatabase();
          process.exit(result.success ? 0 : 1);
          break;

        case 'create-admin':
          const email = args[1];
          const name = args[2] || 'Admin User';
          const role = (args[3] as 'admin' | 'super_admin') || 'admin';

          if (!email) {
            console.error('❌ Email is required');
            console.log('Usage: npm run secure-admin create-admin <email> [name] [role]');
            await closeDatabase();
            process.exit(1);
          }

          const createResult = await SecureAdminSetup.createAdmin(email, name, role, { generatePassword: true });

          console.log('\n🔐 ADMIN CREATION RESULT:');
          console.log('Status:', createResult.success ? '✅ SUCCESS' : '❌ FAILED');
          console.log('Message:', createResult.message);

          if (createResult.credentials) {
            console.log('\n🔑 LOGIN CREDENTIALS:');
            console.log('Email:', createResult.credentials.email);
            console.log('Password:', createResult.credentials.password);
            console.log('\n⚠️  IMPORTANT: Admin must change password on first login!');
          }

          await closeDatabase();
          process.exit(createResult.success ? 0 : 1);
          break;

        case 'reset-password':
          const resetEmail = args[1];

          if (!resetEmail) {
            console.error('❌ Email is required');
            console.log('Usage: npm run secure-admin reset-password <email>');
            await closeDatabase();
            process.exit(1);
          }

          const resetResult = await SecureAdminSetup.resetAdminPassword(resetEmail, { generatePassword: true });

          console.log('\n🔐 PASSWORD RESET RESULT:');
          console.log('Status:', resetResult.success ? '✅ SUCCESS' : '❌ FAILED');
          console.log('Message:', resetResult.message);

          if (resetResult.credentials) {
            console.log('\n🔑 NEW LOGIN CREDENTIALS:');
            console.log('Email:', resetResult.credentials.email);
            console.log('Password:', resetResult.credentials.password);
            console.log('\n⚠️  IMPORTANT: Admin must change password on first login!');
          }

          await closeDatabase();
          process.exit(resetResult.success ? 0 : 1);
          break;

        default:
          console.log('🔐 SECURE ADMIN SETUP TOOL');
          console.log('');
          console.log('Available commands:');
          console.log('  setup-main                    - Setup/update main admin with secure password');
          console.log('  create-admin <email> [name] [role] - Create new admin with secure password');
          console.log('  reset-password <email>       - Reset admin password securely');
          console.log('');
          console.log('Examples:');
          console.log('  npm run secure-admin setup-main');
          console.log('  npm run secure-admin create-admin <EMAIL> "John Doe" admin');
          console.log('  npm run secure-admin reset-password <EMAIL>');
          await closeDatabase();
          break;
      }
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      await closeDatabase();
      process.exit(1);
    }
  };

  initAndRun();
}
