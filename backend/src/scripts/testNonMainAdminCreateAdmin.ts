import fetch from 'node-fetch';

async function testNonMainAdminCreateAdmin() {
  try {
    console.log('Testing non-main admin creating a new admin...');

    // First, login as non-main admin
    const loginResponse = await fetch('http://localhost:5000/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123',
      }),
    });

    if (!loginResponse.ok) {
      console.log('Login failed:', loginResponse.status);
      return;
    }

    const loginData = await loginResponse.json() as { token: string, admin: any };
    console.log('Login successful, got token');

    // Now try to create a new admin
    const createResponse = await fetch('http://localhost:5000/api/admin/create', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test Admin',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin',
      }),
    });

    console.log('Create admin response status:', createResponse.status);

    try {
      const data = await createResponse.json();
      console.log('Response data:', JSON.stringify(data, null, 2));
    } catch (e) {
      console.log('Could not parse response as JSON');
    }

    if (createResponse.ok) {
      console.log('Admin creation successful (this should not happen!)');
    } else {
      console.log('Admin creation failed (expected behavior)');
    }
  } catch (error) {
    console.error('Error testing admin creation:', error);
  }
}

testNonMainAdminCreateAdmin();
