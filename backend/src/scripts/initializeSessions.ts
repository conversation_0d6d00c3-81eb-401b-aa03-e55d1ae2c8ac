import { initializeDatabase, closeDatabase } from '../config/database';
import SessionManager from '../services/sessionManager';

async function initializeSessions() {
  try {
    console.log('Initializing session management system...');
    
    // Initialize database connection
    await initializeDatabase();
    
    // Create sessions table
    await SessionManager.initializeSessionsTable();
    
    console.log('✅ Sessions table created successfully');
    
    // Get current session stats
    const stats = await SessionManager.getSessionStats();
    console.log('📊 Session Statistics:');
    console.log(`  - Active sessions: ${stats.totalActive}`);
    console.log(`  - Expired sessions: ${stats.totalExpired}`);
    console.log(`  - Invalidated sessions: ${stats.totalInvalidated}`);
    
    console.log('✅ Session management system initialized successfully');
    
  } catch (error) {
    console.error('❌ Error initializing session management:', error);
  } finally {
    await closeDatabase();
  }
}

initializeSessions();
