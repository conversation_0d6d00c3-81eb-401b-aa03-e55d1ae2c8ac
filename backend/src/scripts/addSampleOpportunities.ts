/**
 * Add Sample Opportunities Data
 * This script adds professional sample opportunities for testing
 */

import { query, initializeDatabase } from '../config/database';

const sampleOpportunities = [
  {
    title: 'Stage Marketing Digital - L\'Oréal',
    description: 'L\'Oréal recherche un stagiaire en marketing digital pour rejoindre son équipe dynamique à Paris. Ce stage de 6 mois vous permettra de développer vos compétences en stratégie digitale, gestion de campagnes et analyse de données.\n\n**Profil recherché :**\n- Étudiant en Master Marketing/Communication\n- Maîtrise des outils digitaux (Google Analytics, Facebook Ads)\n- Créativité et esprit d\'analyse\n- Anglais courant\n\n**Ce que nous offrons :**\n- Encadrement par des experts du secteur\n- Formation aux dernières tendances marketing\n- Indemnité de stage attractive\n- Possibilité d\'embauche',
    type: 'internship',
    organization: 'L\'Oréal',
    location: 'Paris, France',
    is_remote: false,
    deadline: '2025-03-15 23:59:59',
    start_date: '2025-06-01 09:00:00',
    end_date: '2025-11-30 18:00:00',
    application_link: 'https://careers.loreal.com/stages',
    requirements: 'Master en Marketing, Communication ou domaine connexe. Expérience en marketing digital souhaitée.',
    benefits: 'Indemnité de stage, formation, encadrement professionnel, possibilité d\'embauche',
    is_active: true,
    tags: '["marketing", "digital", "stage", "Paris", "cosmétique"]',
    contact_email: '<EMAIL>',
    website: 'https://careers.loreal.com'
  },
  {
    title: 'Formation Intelligence Artificielle - Microsoft',
    description: 'Microsoft propose une formation intensive de 3 mois en Intelligence Artificielle et Machine Learning. Cette formation s\'adresse aux développeurs et ingénieurs souhaitant se spécialiser dans l\'IA.\n\n**Programme :**\n- Fondamentaux de l\'IA et ML\n- Deep Learning avec TensorFlow\n- Azure AI Services\n- Projets pratiques\n\n**Prérequis :**\n- Diplôme en informatique ou équivalent\n- Expérience en programmation (Python, C#)\n- Bases en mathématiques/statistiques\n\n**Avantages :**\n- Formation gratuite\n- Certification Microsoft\n- Mentorat par des experts\n- Opportunités d\'emploi',
    type: 'training',
    organization: 'Microsoft',
    location: 'Lyon, France',
    is_remote: true,
    deadline: '2025-02-28 23:59:59',
    start_date: '2025-04-15 09:00:00',
    end_date: '2025-07-15 17:00:00',
    application_link: 'https://docs.microsoft.com/learn/certifications',
    requirements: 'Diplôme en informatique, expérience en programmation, bases en mathématiques',
    benefits: 'Formation gratuite, certification, mentorat, opportunités d\'emploi',
    is_active: true,
    tags: '["IA", "machine learning", "formation", "Microsoft", "certification"]',
    contact_email: '<EMAIL>',
    website: 'https://docs.microsoft.com/learn'
  },
  {
    title: 'Conférence Internationale sur l\'Innovation - Web Summit',
    description: 'Le Web Summit 2025 est la plus grande conférence technologique au monde. Rejoignez plus de 70 000 participants pour découvrir les dernières innovations et tendances technologiques.\n\n**Au programme :**\n- Conférences de leaders technologiques\n- Ateliers pratiques\n- Networking avec des startups\n- Exposition d\'innovations\n\n**Participants :**\n- Entrepreneurs et startups\n- Investisseurs et VCs\n- Développeurs et ingénieurs\n- Étudiants et chercheurs\n\n**Avantages :**\n- Accès à toutes les conférences\n- Networking premium\n- Certificat de participation\n- Opportunités de stage/emploi',
    type: 'conference',
    organization: 'Web Summit',
    location: 'Lisbonne, Portugal',
    is_remote: false,
    deadline: '2025-09-30 23:59:59',
    start_date: '2025-11-04 08:00:00',
    end_date: '2025-11-07 19:00:00',
    application_link: 'https://websummit.com/register',
    requirements: 'Intérêt pour la technologie et l\'innovation. Ouvert à tous les niveaux.',
    benefits: 'Networking, apprentissage, certificat, opportunités professionnelles',
    is_active: true,
    tags: '["conférence", "technologie", "innovation", "networking", "startup"]',
    contact_email: '<EMAIL>',
    website: 'https://websummit.com'
  },
  {
    title: 'Atelier Entrepreneuriat - Station F',
    description: 'Station F organise un atelier intensif d\'une semaine sur l\'entrepreneuriat et la création de startup. Apprenez les bases pour lancer votre propre entreprise.\n\n**Contenu de l\'atelier :**\n- Business model et validation d\'idée\n- Stratégies de financement\n- Marketing et acquisition clients\n- Aspects juridiques et fiscaux\n\n**Public cible :**\n- Étudiants et jeunes diplômés\n- Porteurs de projets\n- Entrepreneurs en herbe\n\n**Bénéfices :**\n- Formation pratique\n- Mentorat personnalisé\n- Accès au réseau Station F\n- Possibilité d\'incubation',
    type: 'workshop',
    organization: 'Station F',
    location: 'Paris, France',
    is_remote: false,
    deadline: '2025-04-20 23:59:59',
    start_date: '2025-05-12 09:00:00',
    end_date: '2025-05-16 18:00:00',
    application_link: 'https://stationf.co/workshops',
    requirements: 'Motivation entrepreneuriale, projet d\'entreprise (optionnel)',
    benefits: 'Formation, mentorat, réseau, possibilité d\'incubation',
    is_active: true,
    tags: '["entrepreneuriat", "startup", "atelier", "Paris", "incubation"]',
    contact_email: '<EMAIL>',
    website: 'https://stationf.co'
  },
  {
    title: 'Concours Innovation Étudiante - Schneider Electric',
    description: 'Schneider Electric lance son concours annuel d\'innovation pour les étudiants. Proposez des solutions durables pour l\'énergie et l\'automatisation.\n\n**Thèmes du concours :**\n- Efficacité énergétique\n- Énergies renouvelables\n- Smart cities\n- Industrie 4.0\n\n**Critères d\'évaluation :**\n- Innovation et originalité\n- Faisabilité technique\n- Impact environnemental\n- Potentiel commercial\n\n**Prix :**\n- 1er prix : 10 000€ + stage\n- 2ème prix : 5 000€\n- 3ème prix : 2 500€\n- Mentorat pour tous les finalistes',
    type: 'competition',
    organization: 'Schneider Electric',
    location: 'France (National)',
    is_remote: true,
    deadline: '2025-05-31 23:59:59',
    start_date: '2025-02-01 00:00:00',
    end_date: '2025-06-30 23:59:59',
    application_link: 'https://schneider-electric.com/concours-innovation',
    requirements: 'Étudiant en école d\'ingénieur ou université, projet innovant dans l\'énergie',
    benefits: 'Prix en argent, stage, mentorat, reconnaissance professionnelle',
    is_active: true,
    tags: '["concours", "innovation", "énergie", "développement durable", "prix"]',
    contact_email: '<EMAIL>',
    website: 'https://schneider-electric.com'
  }
];

async function addSampleOpportunities() {
  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('🚀 Adding sample opportunities...');
    
    for (const opportunity of sampleOpportunities) {
      await query(`
        INSERT INTO opportunities (
          title, description, type, organization, location, is_remote, deadline,
          start_date, end_date, application_link, requirements, benefits,
          is_active, tags, contact_email, website
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      `, [
        opportunity.title,
        opportunity.description,
        opportunity.type,
        opportunity.organization,
        opportunity.location,
        opportunity.is_remote,
        opportunity.deadline,
        opportunity.start_date,
        opportunity.end_date,
        opportunity.application_link,
        opportunity.requirements,
        opportunity.benefits,
        opportunity.is_active,
        opportunity.tags,
        opportunity.contact_email,
        opportunity.website
      ]);
      
      console.log(`✅ Added: ${opportunity.title}`);
    }
    
    console.log('🎉 Sample opportunities added successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding sample opportunities:', error);
    process.exit(1);
  }
}

// Run the script
addSampleOpportunities();
