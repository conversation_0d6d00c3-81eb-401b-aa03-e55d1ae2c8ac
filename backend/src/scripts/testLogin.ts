import fetch from 'node-fetch';

async function testLogin() {
  try {
    console.log('Testing admin login API...');
    
    const response = await fetch('http://localhost:5000/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123',
      }),
    });
    
    console.log('Response status:', response.status);
    
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('Login successful!');
    } else {
      console.log('Login failed!');
    }
  } catch (error) {
    console.error('Error testing login:', error);
  }
}

testLogin();
