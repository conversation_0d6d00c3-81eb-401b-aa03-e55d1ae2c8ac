import { query, initializeDatabase } from '../config/database';

/**
 * Fix database schema issues
 * - Add missing admin_id column to login_attempts table
 * - Fix any other schema inconsistencies
 */
async function fixDatabaseSchema() {
  try {
    console.log('🔧 Starting database schema fixes...');

    // Initialize database connection
    console.log('📝 Initializing database connection...');
    await initializeDatabase();
    console.log('✅ Database connection initialized');

    // 1. Add admin_id column to login_attempts table if it doesn't exist
    console.log('📝 Adding admin_id column to login_attempts table...');
    await query(`
      ALTER TABLE login_attempts 
      ADD COLUMN IF NOT EXISTS admin_id INTEGER REFERENCES admins(id);
    `);
    console.log('✅ admin_id column added successfully');

    // 2. Create index on admin_id for better performance
    console.log('📝 Creating index on admin_id...');
    await query(`
      CREATE INDEX IF NOT EXISTS idx_login_attempts_admin_id 
      ON login_attempts(admin_id);
    `);
    console.log('✅ Index created successfully');

    // 3. Update existing login_attempts records to link to admin if possible
    console.log('📝 Updating existing login_attempts records...');
    await query(`
      UPDATE login_attempts 
      SET admin_id = (
        SELECT id FROM admins 
        WHERE email = login_attempts.email 
        LIMIT 1
      )
      WHERE admin_id IS NULL 
      AND email IN (SELECT email FROM admins);
    `);
    console.log('✅ Existing records updated successfully');

    // 4. Verify the schema fix
    console.log('📝 Verifying schema fix...');
    const result = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'login_attempts' 
      AND column_name = 'admin_id';
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ Schema verification successful:', result.rows[0]);
    } else {
      console.log('❌ Schema verification failed - admin_id column not found');
    }

    console.log('🎉 Database schema fixes completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
    throw error;
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixDatabaseSchema()
    .then(() => {
      console.log('✅ Database schema fix completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database schema fix failed:', error);
      process.exit(1);
    });
}

export default fixDatabaseSchema;
