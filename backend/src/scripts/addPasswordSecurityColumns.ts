import { query, initializeDatabase, closeDatabase } from '../config/database';

/**
 * Add password security columns to existing database
 */
async function addPasswordSecurityColumns() {
  try {
    console.log('🔧 Adding password security columns to database...');
    
    await initializeDatabase();

    // Add missing columns to admins table
    console.log('Adding columns to admins table...');
    
    await query(`
      ALTER TABLE admins 
      ADD COLUMN IF NOT EXISTS password_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      ADD COLUMN IF NOT EXISTS password_expires_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS must_change_password BOOLEAN DEFAULT FALSE;
    `);

    // Add missing columns to users table (if not already present)
    console.log('Adding columns to users table...');
    
    await query(`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS password_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      ADD COLUMN IF NOT EXISTS password_expires_at TIMESTAMP,
      ADD COLUMN IF NOT EXISTS must_change_password BOOLEAN DEFAULT FALSE;
    `);

    // Update password history table to support both users and admins
    console.log('Updating password history table...');

    // Drop existing foreign key constraint if it exists
    await query(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'password_history_user_id_fkey'
          AND table_name = 'password_history'
        ) THEN
          ALTER TABLE password_history DROP CONSTRAINT password_history_user_id_fkey;
        END IF;
      END $$;
    `);

    // Add table_name column if it doesn't exist
    await query(`
      ALTER TABLE password_history
      ADD COLUMN IF NOT EXISTS table_name VARCHAR(20) DEFAULT 'users';
    `);

    // Add constraint if it doesn't exist
    await query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'chk_table_name'
          AND table_name = 'password_history'
        ) THEN
          ALTER TABLE password_history
          ADD CONSTRAINT chk_table_name CHECK (table_name IN ('users', 'admins'));
        END IF;
      END $$;
    `);

    // Add indexes for password history
    await query(`
      CREATE INDEX IF NOT EXISTS idx_password_history_user_table ON password_history(user_id, table_name);
      CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history(created_at);
    `);

    // Update existing admins to have password_updated_at set
    console.log('Updating existing admin password timestamps...');
    
    await query(`
      UPDATE admins 
      SET password_updated_at = COALESCE(updated_at, created_at, CURRENT_TIMESTAMP)
      WHERE password_updated_at IS NULL;
    `);

    // Update existing users to have password_updated_at set
    console.log('Updating existing user password timestamps...');
    
    await query(`
      UPDATE users 
      SET password_updated_at = COALESCE(updated_at, created_at, CURRENT_TIMESTAMP)
      WHERE password_updated_at IS NULL;
    `);

    console.log('✅ Password security columns added successfully!');
    
  } catch (error) {
    console.error('❌ Error adding password security columns:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// Run if called directly
if (require.main === module) {
  addPasswordSecurityColumns()
    .then(() => {
      console.log('🔐 Database security enhancement completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Database security enhancement failed:', error);
      process.exit(1);
    });
}

export { addPasswordSecurityColumns };
