import { initializeDatabase, closeDatabase, query } from '../config/database';

async function initializePasswordHistory() {
  try {
    console.log('Initializing password history system...');
    
    // Initialize database connection
    await initializeDatabase();
    
    // Create password history table
    await query(`
      CREATE TABLE IF NOT EXISTS admin_password_history (
        id SERIAL PRIMARY KEY,
        admin_id INTEGER NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_admin_password_history_admin_id ON admin_password_history(admin_id);
      CREATE INDEX IF NOT EXISTS idx_admin_password_history_created_at ON admin_password_history(created_at);
    `);
    
    console.log('✅ Password history table created successfully');
    
    // Check current password history count
    const result = await query(`
      SELECT COUNT(*) as total_entries
      FROM admin_password_history
    `);
    
    console.log(`📊 Password History Statistics:`);
    console.log(`  - Total password history entries: ${result.rows[0].total_entries}`);
    
    console.log('✅ Password history system initialized successfully');
    
  } catch (error) {
    console.error('❌ Error initializing password history:', error);
  } finally {
    await closeDatabase();
  }
}

initializePasswordHistory();
