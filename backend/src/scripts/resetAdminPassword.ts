import bcrypt from 'bcryptjs';
import { Admin } from '../models/Admin';
import { initializeDatabase, closeDatabase } from '../config/database';

async function resetAdminPassword() {
  try {
    // Initialize database connection
    await initializeDatabase();

    const email = '<EMAIL>';
    const newPassword = 'Zw1@x$y{l8pomVF6'; // Strong password

    console.log('Resetting admin password...');

    // Find admin by email
    const admin = await Admin.findByEmail(email);

    if (!admin) {
      console.log('Admin not found with email:', email);
      return;
    }

    console.log('Found admin:', {
      id: admin.id,
      name: admin.name,
      email: admin.email,
      role: admin.role,
      isMainAdmin: admin.isMainAdmin
    });

    // Update admin password and reset failed attempts
    await Admin.update(admin.id!, {
      password: newPassword, // The update method will hash it automatically
      failedLoginAttempts: 0,
      lockUntil: undefined
    });

    console.log('✅ Admin password reset successfully!');
    console.log('New credentials:');
    console.log('Email:', email);
    console.log('Password:', newPassword);
    console.log('\nYou can now login with these credentials.');

  } catch (error) {
    console.error('Error resetting admin password:', error);
  } finally {
    await closeDatabase();
  }
}

resetAdminPassword();
