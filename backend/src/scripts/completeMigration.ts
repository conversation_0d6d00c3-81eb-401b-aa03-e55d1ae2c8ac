/**
 * Complete Migration Script
 * 
 * This script orchestrates the entire migration process from Sequelize to Prisma.
 * It runs all the individual scripts in the correct order with proper error handling.
 */

import fs from 'fs';
import path from 'path';
import { runFullMigration } from './runFullMigration';
import { updateControllers } from './updateControllers';
import { removeSequelize } from './removeSequelize';

// Paths
const BACKEND_DIR = path.join(__dirname, '../..');
const LOG_DIR = path.join(BACKEND_DIR, 'logs');

// Log file
const LOG_FILE = path.join(LOG_DIR, `complete-migration-${new Date().toISOString().replace(/:/g, '-')}.log`);

/**
 * Log message to console and file
 */
function log(message: string) {
  const timestampedMessage = `[${new Date().toISOString()}] ${message}`;
  console.log(timestampedMessage);
  
  // Create logs directory if it doesn't exist
  if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
  }
  
  fs.appendFileSync(LOG_FILE, timestampedMessage + '\n');
}

/**
 * Run the complete migration process
 */
async function completeMigration() {
  log('Starting complete migration from Sequelize to Prisma');
  
  try {
    // Step 1: Run data migration
    log('STEP 1: Running data migration...');
    await runFullMigration();
    log('Data migration completed successfully.');
    
    // Step 2: Update controllers and routes
    log('STEP 2: Updating controllers and routes...');
    await updateControllers();
    log('Controllers and routes updated successfully.');
    
    // Step 3: Remove Sequelize dependencies
    log('STEP 3: Removing Sequelize dependencies...');
    await removeSequelize();
    log('Sequelize dependencies removed successfully.');
    
    log('Complete migration process finished successfully!');
    log('The application now uses Prisma exclusively for database access.');
    
    return true;
  } catch (error) {
    log(`ERROR: Migration failed: ${error}`);
    log('Please check the individual log files for more details.');
    return false;
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  completeMigration()
    .then((success) => {
      if (success) {
        log('Migration completed successfully.');
        process.exit(0);
      } else {
        log('Migration failed.');
        process.exit(1);
      }
    })
    .catch((error) => {
      log(`Unhandled error in migration script: ${error}`);
      process.exit(1);
    });
}

export { completeMigration };
