import bcrypt from 'bcryptjs';
import Admin from '../models/Admin';
import sequelize from '../config/database';

async function createAdmin() {
  try {
    // Ensure database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Sync the model with the database
    await sequelize.sync();
    console.log('Database models synchronized successfully.');

    const email = '<EMAIL>';
    const password = 'admin123'; // You should change this in production

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ where: { email } });

    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user
    const admin = await Admin.create({
      email,
      password: hashedPassword,
      name: 'Admin User',
      role: 'admin',
      privileges: ['all'],
      isMainAdmin: true
    });

    console.log('Admin user created successfully:', {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      isMainAdmin: admin.isMainAdmin
    });
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await sequelize.close();
  }
}

createAdmin();
