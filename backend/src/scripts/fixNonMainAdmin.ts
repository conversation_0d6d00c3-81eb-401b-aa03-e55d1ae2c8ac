import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function fixNonMainAdmin() {
  try {
    console.log('Fixing non-main admin accounts...');
    
    // Find all non-main admin accounts
    const nonMainAdmins = await prisma.admin.findMany({
      where: { isMainAdmin: false }
    });
    
    console.log(`Found ${nonMainAdmins.length} non-main admin account(s).`);
    
    for (const admin of nonMainAdmins) {
      console.log(`Processing admin: ${admin.name} (${admin.email})`);
      
      // Reset password to a simple one for testing
      const hashedPassword = await bcrypt.hash('password123', 10);
      
      // Update admin with proper role and privileges
      await prisma.admin.update({
        where: { id: admin.id },
        data: {
          password: hashedPassword,
          role: 'admin', // Ensure role is set to 'admin'
          privileges: JSON.stringify(['all']), // Give full privileges for testing
        }
      });
      
      console.log(`Updated admin ${admin.name} with new password and proper role/privileges.`);
      console.log(`Login credentials: ${admin.email} / password123`);
    }
    
    // Verify the updates
    const updatedAdmins = await prisma.admin.findMany({
      where: { isMainAdmin: false }
    });
    
    console.log('\nUpdated non-main admin accounts:');
    for (const admin of updatedAdmins) {
      console.log(`- ${admin.name} (${admin.email}), role: ${admin.role}, privileges: ${admin.privileges}`);
    }
    
    console.log('\nNon-main admin accounts fixed successfully.');
    
  } catch (error) {
    console.error('Error fixing non-main admin accounts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixNonMainAdmin();
