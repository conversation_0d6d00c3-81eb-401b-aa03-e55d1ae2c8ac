#!/usr/bin/env ts-node

/**
 * Migration script for Device Approval System
 * Adds device approval workflow tables and fields
 */

import { query, closeDatabase, initializeDatabase } from '../config/database';

async function migrateDeviceApproval() {
  try {
    console.log('🔄 Starting Device Approval System migration...');

    // Initialize database connection
    await initializeDatabase();

    // Add new columns to trusted_devices table
    console.log('Adding new columns to trusted_devices table...');
    await query(`
      ALTER TABLE trusted_devices 
      ADD COLUMN IF NOT EXISTS trusted_by INTEGER,
      ADD COLUMN IF NOT EXISTS approval_status VARCHAR(20) DEFAULT 'pending',
      ADD COLUMN IF NOT EXISTS approval_requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      ADD COLUMN IF NOT EXISTS approval_notes TEXT,
      ADD COLUMN IF NOT EXISTS risk_score INTEGER DEFAULT 0;
    `);

    // Update existing trusted devices to have 'auto_approved' status
    console.log('Updating existing trusted devices...');
    await query(`
      UPDATE trusted_devices 
      SET approval_status = CASE 
        WHEN trusted = true THEN 'approved'
        ELSE 'pending'
      END
      WHERE approval_status IS NULL OR approval_status = 'pending';
    `);

    // Create device approval requests table
    console.log('Creating device_approval_requests table...');
    await query(`
      CREATE TABLE IF NOT EXISTS device_approval_requests (
        id SERIAL PRIMARY KEY,
        device_id INTEGER NOT NULL REFERENCES trusted_devices(id) ON DELETE CASCADE,
        admin_id INTEGER NOT NULL,
        request_type VARCHAR(20) NOT NULL,
        requested_by INTEGER NOT NULL,
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_by INTEGER,
        approved_at TIMESTAMP,
        status VARCHAR(20) DEFAULT 'pending',
        reason TEXT,
        approval_notes TEXT,
        metadata JSONB
      );
    `);

    // Create indexes for better performance
    console.log('Creating indexes...');
    await query(`
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_approval_status ON trusted_devices(approval_status);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_risk_score ON trusted_devices(risk_score);
      CREATE INDEX IF NOT EXISTS idx_trusted_devices_trusted_by ON trusted_devices(trusted_by);
    `);

    await query(`
      CREATE INDEX IF NOT EXISTS idx_device_approval_device_id ON device_approval_requests(device_id);
      CREATE INDEX IF NOT EXISTS idx_device_approval_admin_id ON device_approval_requests(admin_id);
      CREATE INDEX IF NOT EXISTS idx_device_approval_status ON device_approval_requests(status);
      CREATE INDEX IF NOT EXISTS idx_device_approval_requested_by ON device_approval_requests(requested_by);
      CREATE INDEX IF NOT EXISTS idx_device_approval_approved_by ON device_approval_requests(approved_by);
      CREATE INDEX IF NOT EXISTS idx_device_approval_requested_at ON device_approval_requests(requested_at);
    `);

    // Calculate risk scores for existing devices
    console.log('Calculating risk scores for existing devices...');
    const devices = await query(`
      SELECT id, trusted, ip_addresses, last_seen, device_type, first_seen
      FROM trusted_devices 
      WHERE risk_score = 0
    `);

    for (const device of devices.rows) {
      let riskScore = 0;

      // Base risk for untrusted devices
      if (!device.trusted) {
        riskScore += 30;
      }

      // Risk based on number of IP addresses used
      const ipCount = device.ip_addresses ? device.ip_addresses.length : 0;
      if (ipCount > 5) {
        riskScore += 20;
      } else if (ipCount > 2) {
        riskScore += 10;
      }

      // Risk based on last seen (inactive devices are riskier)
      const daysSinceLastSeen = Math.floor(
        (Date.now() - new Date(device.last_seen).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceLastSeen > 30) {
        riskScore += 25;
      } else if (daysSinceLastSeen > 7) {
        riskScore += 10;
      }

      // Risk based on device type (mobile devices might be riskier)
      if (device.device_type === 'mobile') {
        riskScore += 5;
      }

      // Risk based on device age (very new devices are riskier)
      const daysSinceFirstSeen = Math.floor(
        (Date.now() - new Date(device.first_seen).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceFirstSeen < 1) {
        riskScore += 15;
      } else if (daysSinceFirstSeen < 7) {
        riskScore += 5;
      }

      const finalRiskScore = Math.min(100, riskScore);

      await query(`
        UPDATE trusted_devices 
        SET risk_score = $1 
        WHERE id = $2
      `, [finalRiskScore, device.id]);
    }

    console.log(`✅ Updated risk scores for ${devices.rows.length} devices`);

    // Add foreign key constraints if they don't exist
    console.log('Adding foreign key constraints...');
    try {
      await query(`
        ALTER TABLE trusted_devices 
        ADD CONSTRAINT fk_trusted_devices_trusted_by 
        FOREIGN KEY (trusted_by) REFERENCES admins(id) ON DELETE SET NULL;
      `);
    } catch (error) {
      // Constraint might already exist
      console.log('Foreign key constraint already exists or failed to add:', (error as Error).message);
    }

    try {
      await query(`
        ALTER TABLE device_approval_requests 
        ADD CONSTRAINT fk_device_approval_requested_by 
        FOREIGN KEY (requested_by) REFERENCES admins(id) ON DELETE CASCADE;
      `);
    } catch (error) {
      // Constraint might already exist
      console.log('Foreign key constraint already exists or failed to add:', (error as Error).message);
    }

    try {
      await query(`
        ALTER TABLE device_approval_requests 
        ADD CONSTRAINT fk_device_approval_approved_by 
        FOREIGN KEY (approved_by) REFERENCES admins(id) ON DELETE SET NULL;
      `);
    } catch (error) {
      // Constraint might already exist
      console.log('Foreign key constraint already exists or failed to add:', (error as Error).message);
    }

    console.log('✅ Device Approval System migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error migrating Device Approval System:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateDeviceApproval()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default migrateDeviceApproval;
