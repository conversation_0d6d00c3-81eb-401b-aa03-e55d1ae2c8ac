import { query } from '../config/database';

async function addNewTables() {
  try {
    console.log('🚀 Adding new tables for guides and opportunities...');
    
    // Create guides table
    await query(`
      CREATE TABLE IF NOT EXISTS guides (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        category VARCHAR(50) NOT NULL CHECK (category IN ('application', 'documents', 'preparation', 'tips')),
        slug VARCHAR(255) NOT NULL UNIQUE,
        excerpt TEXT,
        thumbnail VARCHAR(500),
        is_published BOOLEAN DEFAULT true,
        read_time INTEGER,
        tags JSONB,
        created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
        created_by_admin INTEGER REFERENCES admins(id) ON DELETE SET NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    
    console.log('✅ Guides table created');
    
    // Create opportunities table
    await query(`
      CREATE TABLE IF NOT EXISTS opportunities (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        type VARCHAR(50) NOT NULL CHECK (type IN ('internship', 'training', 'conference', 'workshop', 'competition')),
        organization VARCHAR(255) NOT NULL,
        location VARCHAR(255) NOT NULL,
        is_remote BOOLEAN DEFAULT false,
        deadline TIMESTAMP WITH TIME ZONE NOT NULL,
        start_date TIMESTAMP WITH TIME ZONE,
        end_date TIMESTAMP WITH TIME ZONE,
        application_link VARCHAR(500),
        requirements TEXT,
        benefits TEXT,
        thumbnail VARCHAR(500),
        is_active BOOLEAN DEFAULT true,
        tags JSONB,
        contact_email VARCHAR(255),
        website VARCHAR(500),
        created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
        created_by_admin INTEGER REFERENCES admins(id) ON DELETE SET NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    
    console.log('✅ Opportunities table created');
    
    // Add sample data
    await query(`
      INSERT INTO guides (title, content, category, slug, excerpt, is_published, read_time, tags) VALUES
      ('Comment rédiger un CV efficace', 
      'Un CV efficace est la clé pour décrocher une bourse d''études. Voici les éléments essentiels à inclure...

## Structure recommandée

1. **Informations personnelles**
   - Nom complet
   - Adresse email professionnelle
   - Numéro de téléphone
   - Adresse (ville, pays)

2. **Profil professionnel**
   - Résumé en 2-3 lignes de votre parcours
   - Objectifs académiques et professionnels

3. **Formation académique**
   - Diplômes obtenus (du plus récent au plus ancien)
   - Mentions et distinctions
   - Projets de recherche

## Conseils pratiques

- Limitez votre CV à 1-2 pages maximum
- Utilisez une police lisible (Arial, Calibri)
- Adaptez votre CV à chaque candidature
- Vérifiez l''orthographe et la grammaire',
      'documents', 
      'comment-rediger-cv-efficace',
      'Découvrez comment créer un CV qui vous démarquera dans vos candidatures de bourses d''études.',
      true,
      8,
      '["CV", "candidature", "documents", "conseils"]'
      ) ON CONFLICT (slug) DO NOTHING;
    `);
    
    await query(`
      INSERT INTO opportunities (title, description, type, organization, location, is_remote, deadline, start_date, application_link, is_active, tags) VALUES
      ('Stage en Recherche - Institut Pasteur',
      'L''Institut Pasteur propose des stages de recherche de 3 à 6 mois dans ses laboratoires. Une excellente opportunité pour les étudiants en biologie, médecine et sciences connexes.',
      'internship',
      'Institut Pasteur',
      'Paris, France',
      false,
      '2025-03-15 23:59:59',
      '2025-06-01 09:00:00',
      'https://www.pasteur.fr/stages',
      true,
      '["recherche", "biologie", "médecine", "Paris"]'
      );
    `);
    
    // Test the tables
    const guidesCount = await query('SELECT COUNT(*) FROM guides');
    const opportunitiesCount = await query('SELECT COUNT(*) FROM opportunities');
    
    console.log(`📚 Guides table: ${guidesCount.rows[0].count} records`);
    console.log(`🚀 Opportunities table: ${opportunitiesCount.rows[0].count} records`);
    
    console.log('🎉 All tables created successfully!');
    
  } catch (error: any) {
    console.error('❌ Error:', error.message);
  }
}

addNewTables().then(() => process.exit(0));
