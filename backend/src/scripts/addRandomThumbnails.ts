/**
 * <PERSON><PERSON><PERSON> to add random thumbnails to existing scholarships
 * This is for testing purposes - in production, thumbnails would be uploaded by admins
 */

import { query, initializeDatabase } from '../config/database';

async function addRandomThumbnails() {
  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('🖼️  Adding random thumbnails to scholarships...');
    
    // Array of existing thumbnail URLs from the uploads directory
    const sampleThumbnails = [
      '/uploads/scholarships/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f.jpg',
      '/uploads/scholarships/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747153473628-********************************.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155362785-593265238e8dca860e8dda80d3687e78.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155363167-ad5c2c73ec2ac68e3826da9943f37e99.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155363584-397c869d4a014f79872a05b53aafceda.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155364203-1d48316c61e6a2817e0002804662030b.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155364780-0618a52e33b74edbddf7288509e4a75b.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155365204-70e7321d0c91f1c925576e033dda3055.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155384927-4208396baa696e31b0a63fd5a54e22cc.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155471980-e85d9a5e4a8350ee6f6498494cd78650.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155472805-91757b1668771c832dc0d52a190c4c01.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155473447-10a341d430ccfb80f8c582efece74dd0.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155473782-31983b7455685672b2935c8bfd55702e.jpg',
      '/uploads/scholarships/DAAD_Scholarship_174-1747155474098-dfa90322449405504b998bf772181303.jpg',
      '/uploads/scholarships/Fulbright_Scholarshi-1747155910558-8a6b759e47be75de9f9c3750e16be3b0.jpg',
      '/uploads/scholarships/Scholarship1-1747182475777-16d40efbb80929b18b3e7edc80ecc8e2.png',
      '/uploads/scholarships/Scholarship2-1747182476272-eb2346fe0edf91f18ed89f74c729f139.png'
    ];
    
    // Get all scholarships that don't have thumbnails
    const scholarshipsResult = await query(`
      SELECT id, title, country 
      FROM scholarships 
      WHERE thumbnail IS NULL OR thumbnail = '' OR thumbnail = 'null'
    `);
    
    const scholarships = scholarshipsResult.rows;
    console.log(`📚 Found ${scholarships.length} scholarships without thumbnails`);
    
    if (scholarships.length === 0) {
      console.log('✅ All scholarships already have thumbnails!');
      return;
    }
    
    // Update each scholarship with a random thumbnail
    for (let i = 0; i < scholarships.length; i++) {
      const scholarship = scholarships[i];
      const randomThumbnail = sampleThumbnails[i % sampleThumbnails.length];
      
      await query(`
        UPDATE scholarships 
        SET thumbnail = $1, updated_at = NOW()
        WHERE id = $2
      `, [randomThumbnail, scholarship.id]);
      
      console.log(`✅ Updated scholarship "${scholarship.title}" with thumbnail: ${randomThumbnail}`);
    }
    
    // Verify the updates
    const updatedResult = await query(`
      SELECT COUNT(*) as count 
      FROM scholarships 
      WHERE thumbnail IS NOT NULL AND thumbnail != '' AND thumbnail != 'null'
    `);
    
    console.log(`🎉 Successfully updated ${scholarships.length} scholarships!`);
    console.log(`📊 Total scholarships with thumbnails: ${updatedResult.rows[0].count}`);
    
    // Show sample of updated scholarships
    const sampleResult = await query(`
      SELECT id, title, thumbnail, country
      FROM scholarships 
      WHERE thumbnail IS NOT NULL 
      LIMIT 5
    `);
    
    console.log('\n📋 Sample updated scholarships:');
    sampleResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. "${row.title}" (${row.country}) - ${row.thumbnail}`);
    });
    
  } catch (error: any) {
    console.error('❌ Error adding thumbnails:', error.message);
  }
}

addRandomThumbnails().then(() => {
  console.log('🏁 Thumbnail update script completed');
  process.exit(0);
});
