/**
 * Guide Controller
 * 
 * Handles all guide-related operations with industry-standard practices
 */

import { Request, Response } from 'express';
import { Guide, GuideData } from '../models/Guide';
import { sendSuccess, sendError } from '../utils/response.util';
import { validateGuideData } from '../utils/validation.util';

/**
 * Get all guides with filtering and pagination
 */
export const getAllGuides = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      published,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      limit: Number(limit),
      offset,
      category: category as string,
      isPublished: published === 'true' ? true : published === 'false' ? false : undefined,
      orderBy: orderBy as 'created_at' | 'title' | 'read_time',
      orderDirection: orderDirection as 'ASC' | 'DESC'
    };

    const guides = await Guide.findAll(options);
    
    sendSuccess(res, 'Guides retrieved successfully', {
      guides,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: guides.length
      }
    });
  } catch (error) {
    console.error('Error fetching guides:', error);
    sendError(res, 'Failed to fetch guides', error);
  }
};

/**
 * Get guide by ID
 */
export const getGuideById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid guide ID', null, 400);
      return;
    }

    const guide = await Guide.findById(Number(id));
    
    if (!guide) {
      sendError(res, 'Guide not found', null, 404);
      return;
    }

    sendSuccess(res, 'Guide retrieved successfully', guide);
  } catch (error) {
    console.error('Error fetching guide:', error);
    sendError(res, 'Failed to fetch guide', error);
  }
};

/**
 * Get guide by slug
 */
export const getGuideBySlug = async (req: Request, res: Response): Promise<void> => {
  try {
    const { slug } = req.params;
    
    if (!slug) {
      sendError(res, 'Slug is required', null, 400);
      return;
    }

    const guide = await Guide.findBySlug(slug);
    
    if (!guide) {
      sendError(res, 'Guide not found', null, 404);
      return;
    }

    sendSuccess(res, 'Guide retrieved successfully', guide);
  } catch (error) {
    console.error('Error fetching guide by slug:', error);
    sendError(res, 'Failed to fetch guide', error);
  }
};

/**
 * Get guides by category
 */
export const getGuidesByCategory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { category } = req.params;
    const { limit } = req.query;
    
    if (!category) {
      sendError(res, 'Category is required', null, 400);
      return;
    }

    const validCategories = ['application', 'documents', 'preparation', 'tips'];
    if (!validCategories.includes(category)) {
      sendError(res, 'Invalid category', null, 400);
      return;
    }

    const guides = await Guide.findByCategory(category, limit ? Number(limit) : undefined);
    
    sendSuccess(res, 'Guides retrieved successfully', guides);
  } catch (error) {
    console.error('Error fetching guides by category:', error);
    sendError(res, 'Failed to fetch guides', error);
  }
};

/**
 * Search guides
 */
export const searchGuides = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q, category, page = 1, limit = 10 } = req.query;
    
    if (!q || typeof q !== 'string') {
      sendError(res, 'Search query is required', null, 400);
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      category: category as string,
      limit: Number(limit),
      offset
    };

    const guides = await Guide.search(q, options);
    
    sendSuccess(res, 'Search completed successfully', {
      guides,
      query: q,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: guides.length
      }
    });
  } catch (error) {
    console.error('Error searching guides:', error);
    sendError(res, 'Failed to search guides', error);
  }
};

/**
 * Create new guide (Admin only)
 */
export const createGuide = async (req: Request, res: Response): Promise<void> => {
  try {
    const guideData: Omit<GuideData, 'id' | 'createdAt' | 'updatedAt'> = req.body;
    
    // Validate guide data
    const validation = validateGuideData(guideData);
    if (!validation.isValid) {
      sendError(res, 'Validation failed', validation.errors, 400);
      return;
    }

    // Add admin info
    guideData.createdByAdmin = (req as any).admin?.id || null;

    // Generate slug if not provided
    if (!guideData.slug) {
      guideData.slug = guideData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');
    }

    const guide = await Guide.create(guideData);
    
    sendSuccess(res, 'Guide created successfully', guide, 201);
  } catch (error) {
    console.error('Error creating guide:', error);
    sendError(res, 'Failed to create guide', error);
  }
};

/**
 * Update guide (Admin only)
 */
export const updateGuide = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData: Partial<GuideData> = req.body;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid guide ID', null, 400);
      return;
    }

    // Check if guide exists
    const existingGuide = await Guide.findById(Number(id));
    if (!existingGuide) {
      sendError(res, 'Guide not found', null, 404);
      return;
    }

    // Validate update data
    const validation = validateGuideData(updateData, true);
    if (!validation.isValid) {
      sendError(res, 'Validation failed', validation.errors, 400);
      return;
    }

    const guide = await Guide.update(Number(id), updateData);
    
    if (!guide) {
      sendError(res, 'Failed to update guide', null, 500);
      return;
    }

    sendSuccess(res, 'Guide updated successfully', guide);
  } catch (error) {
    console.error('Error updating guide:', error);
    sendError(res, 'Failed to update guide', error);
  }
};

/**
 * Delete guide (Admin only)
 */
export const deleteGuide = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid guide ID', null, 400);
      return;
    }

    // Check if guide exists
    const existingGuide = await Guide.findById(Number(id));
    if (!existingGuide) {
      sendError(res, 'Guide not found', null, 404);
      return;
    }

    const deleted = await Guide.delete(Number(id));
    
    if (!deleted) {
      sendError(res, 'Failed to delete guide', null, 500);
      return;
    }

    sendSuccess(res, 'Guide deleted successfully', null);
  } catch (error) {
    console.error('Error deleting guide:', error);
    sendError(res, 'Failed to delete guide', error);
  }
};
