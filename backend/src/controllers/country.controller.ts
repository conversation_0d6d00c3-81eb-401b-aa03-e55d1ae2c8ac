/**
 * Country Controller
 * 
 * Handles country-related operations and scholarship filtering by country
 */

import { Request, Response } from 'express';
import { Scholarship } from '../models/Scholarship';
import { sendSuccess, sendError, sendPaginatedSuccess } from '../utils/apiResponse';
import { query } from '../config/database';
import dateUtils from '../utils/dateUtils';

/**
 * Get all countries with scholarship counts
 */
export const getAllCountries = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get distinct countries from scholarships with counts
    const result = await Scholarship.getCountriesWithCounts();
    
    sendSuccess(res, result, 'Countries retrieved successfully');
  } catch (error) {
    console.error('Error fetching countries:', error);
    sendError(res, 'Failed to fetch countries', error);
  }
};

/**
 * Get scholarships by country
 */
export const getScholarshipsByCountry = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { country } = req.params;
    const {
      page = '1',
      limit = '12',
      level,
      isOpen,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    if (!country) {
      return sendError(res, 'Country parameter is required', null, 400);
    }

    const decodedCountry = decodeURIComponent(country);
    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const offset = (pageNumber - 1) * limitNumber;

    // Build WHERE clause
    let whereClause = 'WHERE country = $1';
    const params: any[] = [decodedCountry];
    let paramIndex = 2;

    if (level) {
      whereClause += ` AND level = $${paramIndex}`;
      params.push(level);
      paramIndex++;
    }

    if (isOpen !== undefined) {
      whereClause += ` AND is_open = $${paramIndex}`;
      params.push(isOpen === 'true');
      paramIndex++;
    }

    // Validate sortBy field
    const validSortFields = ['created_at', 'title', 'deadline', 'level'];
    const sortField = validSortFields.includes(sortBy as string) ? sortBy as string : 'created_at';
    const order = (sortOrder as string)?.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM scholarships
      ${whereClause}
    `, params);

    const totalCount = parseInt(countResult.rows[0].total);

    // Get scholarships
    const result = await query(`
      SELECT
        id, title, description, country, level, deadline, is_open,
        thumbnail, funding_source, created_at, updated_at
      FROM scholarships
      ${whereClause}
      ORDER BY ${sortField} ${order}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...params, limitNumber, offset]);

    // Process scholarships with computed fields
    const scholarships = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      country: row.country,
      level: row.level,
      deadline: row.deadline,
      isOpen: row.is_open,
      thumbnail: row.thumbnail,
      fundingSource: row.funding_source,
      createdAt: row.created_at?.toISOString(),
      updatedAt: row.updated_at?.toISOString(),
      // Add computed fields for frontend
      isExpired: dateUtils.isDatePast(row.deadline),
      daysRemaining: dateUtils.getDaysRemaining(row.deadline),
      formattedDeadline: dateUtils.formatDate(row.deadline)
    }));

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limitNumber);

    return sendPaginatedSuccess(
      res,
      scholarships,
      {
        page: pageNumber,
        limit: limitNumber,
        total: totalCount,
        totalPages
      },
      'Scholarships retrieved successfully'
    );
  } catch (error) {
    console.error('Error fetching scholarships by country:', error);
    return sendError(res, 'Failed to fetch scholarships', error);
  }
};

/**
 * Get country statistics
 */
export const getCountryStatistics = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { country } = req.params;

    if (!country) {
      return sendError(res, 'Country parameter is required', null, 400);
    }

    const decodedCountry = decodeURIComponent(country);

    // Use direct SQL queries for better performance
    const statsResult = await query(`
      SELECT
        COUNT(*) as total_scholarships,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_scholarships,
        COUNT(CASE WHEN is_open = false THEN 1 END) as closed_scholarships
      FROM scholarships
      WHERE country = $1
    `, [decodedCountry]);

    const levelStatsResult = await query(`
      SELECT
        level,
        COUNT(*) as count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count
      FROM scholarships
      WHERE country = $1 AND level IS NOT NULL AND level != ''
      GROUP BY level
      ORDER BY count DESC
    `, [decodedCountry]);

    const stats = statsResult.rows[0];
    const levelStats = levelStatsResult.rows.map(row => ({
      level: row.level,
      count: parseInt(row.count),
      openCount: parseInt(row.open_count)
    }));

    const statistics = {
      country: decodedCountry,
      totalScholarships: parseInt(stats.total_scholarships),
      openScholarships: parseInt(stats.open_scholarships),
      closedScholarships: parseInt(stats.closed_scholarships),
      scholarshipsByLevel: levelStats
    };

    return sendSuccess(res, statistics, 'Country statistics retrieved successfully');
  } catch (error) {
    console.error('Error fetching country statistics:', error);
    return sendError(res, 'Failed to fetch country statistics', error);
  }
};

/**
 * Search countries
 */
export const searchCountries = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { q } = req.query;

    if (!q || typeof q !== 'string') {
      return sendError(res, 'Search query is required', null, 400);
    }

    const result = await query(`
      SELECT
        country,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count
      FROM scholarships
      WHERE country ILIKE $1 AND country IS NOT NULL AND country != ''
      GROUP BY country
      ORDER BY total_count DESC
      LIMIT 20
    `, [`%${q}%`]);

    const countries = result.rows.map(row => ({
      name: row.country,
      totalCount: parseInt(row.total_count),
      openCount: parseInt(row.open_count),
      slug: row.country.toLowerCase().replace(/\s+/g, '-')
    }));

    return sendSuccess(res, countries, 'Country search completed successfully');
  } catch (error) {
    console.error('Error searching countries:', error);
    return sendError(res, 'Failed to search countries', error);
  }
};

/**
 * Get all countries with enhanced data for sidebar - Industry Standard Implementation
 */
export const getAllCountriesForSidebar = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { limit = '20', excludeCountry } = req.query;
    const limitNumber = Math.min(parseInt(limit as string, 10), 50);

    let whereClause = 'WHERE country IS NOT NULL AND country != \'\'';
    const params: any[] = [];

    if (excludeCountry) {
      whereClause += ' AND country != $1';
      params.push(decodeURIComponent(excludeCountry as string));
    }

    const result = await query(`
      SELECT
        country,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count,
        MAX(created_at) as latest_scholarship
      FROM scholarships
      ${whereClause}
      GROUP BY country
      ORDER BY total_count DESC, latest_scholarship DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);

    const countries = result.rows.map(row => ({
      name: row.country,
      totalCount: parseInt(row.total_count),
      openCount: parseInt(row.open_count),
      latestScholarship: row.latest_scholarship,
      slug: row.country.toLowerCase().replace(/\s+/g, '-')
    }));

    return sendSuccess(res, countries, 'Countries for sidebar retrieved successfully');
  } catch (error) {
    console.error('Error fetching countries for sidebar:', error);
    return sendError(res, 'Failed to fetch countries for sidebar', error);
  }
};
