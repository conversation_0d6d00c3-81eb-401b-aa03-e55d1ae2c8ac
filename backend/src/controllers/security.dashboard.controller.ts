import { Request, Response } from 'express';
import { query } from '../config/database';
import { SecurityMonitor } from '../utils/securityMonitor';
import { GeolocationService } from '../utils/geolocation';
import { sendSuccess, sendError, sendNotFound } from '../utils/apiResponse';

/**
 * ENTERPRISE-GRADE SECURITY DASHBOARD CONTROLLER
 * Provides real-time security monitoring and analytics
 */

/**
 * Get security dashboard overview
 */
export const getSecurityDashboard = async (req: Request, res: Response) => {
  try {
    const timeframe = (req.query.timeframe as string) || 'day';
    
    // Get security metrics
    const metrics = await SecurityMonitor.getSecurityMetrics(timeframe as any);
    
    // Get recent security events
    const recentEvents = await query(`
      SELECT id, event_type, message, severity, ip, timestamp, 
             COALESCE(admin_id, user_id) as user_id,
             email, risk_score, resolved
      FROM security_events 
      WHERE timestamp > NOW() - INTERVAL '24 hours'
      ORDER BY timestamp DESC 
      LIMIT 20
    `);

    // Get active security alerts
    const activeAlerts = await query(`
      SELECT id, alert_type, title, description, severity, 
             timestamp, acknowledged, resolved,
             COALESCE(admin_id, user_id) as user_id, ip
      FROM security_alerts 
      WHERE resolved = false
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          ELSE 4 
        END,
        timestamp DESC
      LIMIT 10
    `);

    // Get login statistics
    const loginStats = await query(`
      SELECT 
        DATE_TRUNC('hour', timestamp) as hour,
        COUNT(*) as total_attempts,
        COUNT(*) FILTER (WHERE success = true) as successful_logins,
        COUNT(*) FILTER (WHERE success = false) as failed_logins,
        COUNT(DISTINCT ip) as unique_ips
      FROM login_attempts 
      WHERE timestamp > NOW() - INTERVAL '24 hours'
      GROUP BY hour
      ORDER BY hour DESC
    `);

    // Get top risk IPs
    const riskIPs = await query(`
      SELECT ip, COUNT(*) as event_count, 
             AVG(risk_score) as avg_risk_score,
             MAX(timestamp) as last_seen
      FROM security_events 
      WHERE timestamp > NOW() - INTERVAL '24 hours'
        AND risk_score > 0
      GROUP BY ip
      ORDER BY avg_risk_score DESC, event_count DESC
      LIMIT 10
    `);

    // Get device statistics
    const deviceStats = await query(`
      SELECT device_type, COUNT(*) as count,
             COUNT(*) FILTER (WHERE trusted = true) as trusted_count
      FROM trusted_devices 
      WHERE last_seen > NOW() - INTERVAL '7 days'
      GROUP BY device_type
    `);

    return sendSuccess(res, {
      overview: metrics,
      recentEvents: recentEvents.rows,
      activeAlerts: activeAlerts.rows,
      loginStatistics: loginStats.rows,
      riskIPs: riskIPs.rows,
      deviceStatistics: deviceStats.rows,
      timestamp: new Date().toISOString()
    }, 'Security dashboard data retrieved successfully');

  } catch (error) {
    console.error('Security dashboard error:', error);
    return sendError(res, 'Failed to retrieve security dashboard data', error);
  }
};

/**
 * Get detailed security events with filtering
 */
export const getSecurityEvents = async (req: Request, res: Response) => {
  try {
    const {
      page = '1',
      limit = '50',
      severity,
      eventType,
      ip,
      email,
      startDate,
      endDate,
      resolved
    } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = Math.min(parseInt(limit as string, 10), 100); // Max 100 per page
    const offset = (pageNumber - 1) * limitNumber;

    // Build WHERE clause
    const conditions: string[] = [];
    const params: any[] = [];
    let paramCount = 1;

    if (severity) {
      conditions.push(`severity = $${paramCount}`);
      params.push(severity);
      paramCount++;
    }

    if (eventType) {
      conditions.push(`event_type = $${paramCount}`);
      params.push(eventType);
      paramCount++;
    }

    if (ip) {
      conditions.push(`ip = $${paramCount}`);
      params.push(ip);
      paramCount++;
    }

    if (email) {
      conditions.push(`email ILIKE $${paramCount}`);
      params.push(`%${email}%`);
      paramCount++;
    }

    if (startDate) {
      conditions.push(`timestamp >= $${paramCount}`);
      params.push(startDate);
      paramCount++;
    }

    if (endDate) {
      conditions.push(`timestamp <= $${paramCount}`);
      params.push(endDate);
      paramCount++;
    }

    if (resolved !== undefined) {
      conditions.push(`resolved = $${paramCount}`);
      params.push(resolved === 'true');
      paramCount++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total FROM security_events ${whereClause}
    `, params);

    const totalCount = parseInt(countResult.rows[0].total);

    // Get events
    const eventsResult = await query(`
      SELECT id, event_type, message, user_id, admin_id, email, ip, 
             user_agent, timestamp, details, severity, session_id,
             geolocation, device_fingerprint, risk_score, resolved,
             resolved_by, resolved_at, resolution_notes
      FROM security_events 
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...params, limitNumber, offset]);

    const totalPages = Math.ceil(totalCount / limitNumber);

    return sendSuccess(res, {
      events: eventsResult.rows,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        total: totalCount,
        totalPages
      }
    }, 'Security events retrieved successfully');

  } catch (error) {
    console.error('Get security events error:', error);
    return sendError(res, 'Failed to retrieve security events', error);
  }
};

/**
 * Get security alerts with filtering
 */
export const getSecurityAlerts = async (req: Request, res: Response) => {
  try {
    const {
      page = '1',
      limit = '20',
      severity,
      alertType,
      acknowledged,
      resolved
    } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = Math.min(parseInt(limit as string, 10), 100);
    const offset = (pageNumber - 1) * limitNumber;

    // Build WHERE clause
    const conditions: string[] = [];
    const params: any[] = [];
    let paramCount = 1;

    if (severity) {
      conditions.push(`severity = $${paramCount}`);
      params.push(severity);
      paramCount++;
    }

    if (alertType) {
      conditions.push(`alert_type = $${paramCount}`);
      params.push(alertType);
      paramCount++;
    }

    if (acknowledged !== undefined) {
      conditions.push(`acknowledged = $${paramCount}`);
      params.push(acknowledged === 'true');
      paramCount++;
    }

    if (resolved !== undefined) {
      conditions.push(`resolved = $${paramCount}`);
      params.push(resolved === 'true');
      paramCount++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countResult = await query(`
      SELECT COUNT(*) as total FROM security_alerts ${whereClause}
    `, params);

    const totalCount = parseInt(countResult.rows[0].total);

    // Get alerts
    const alertsResult = await query(`
      SELECT id, alert_type, title, description, severity, user_id, admin_id,
             ip, triggered_by_event_id, timestamp, acknowledged, acknowledged_by,
             acknowledged_at, resolved, resolved_by, resolved_at, resolution_notes, metadata
      FROM security_alerts 
      ${whereClause}
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          ELSE 4 
        END,
        timestamp DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, [...params, limitNumber, offset]);

    const totalPages = Math.ceil(totalCount / limitNumber);

    return sendSuccess(res, {
      alerts: alertsResult.rows,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        total: totalCount,
        totalPages
      }
    }, 'Security alerts retrieved successfully');

  } catch (error) {
    console.error('Get security alerts error:', error);
    return sendError(res, 'Failed to retrieve security alerts', error);
  }
};

/**
 * Acknowledge security alert
 */
export const acknowledgeAlert = async (req: Request, res: Response) => {
  try {
    const alertId = parseInt(req.params.id);
    const adminId = req.user?.id;

    if (!adminId) {
      return sendError(res, 'Authentication required', null, 401);
    }

    const result = await query(`
      UPDATE security_alerts 
      SET acknowledged = true, 
          acknowledged_by = $1, 
          acknowledged_at = CURRENT_TIMESTAMP
      WHERE id = $2 AND acknowledged = false
      RETURNING *
    `, [adminId, alertId]);

    if (result.rows.length === 0) {
      return sendNotFound(res, 'Alert not found or already acknowledged');
    }

    return sendSuccess(res, result.rows[0], 'Alert acknowledged successfully');

  } catch (error) {
    console.error('Acknowledge alert error:', error);
    return sendError(res, 'Failed to acknowledge alert', error);
  }
};

/**
 * Resolve security alert
 */
export const resolveAlert = async (req: Request, res: Response) => {
  try {
    const alertId = parseInt(req.params.id);
    const { resolutionNotes } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return sendError(res, 'Authentication required', null, 401);
    }

    const result = await query(`
      UPDATE security_alerts 
      SET resolved = true, 
          resolved_by = $1, 
          resolved_at = CURRENT_TIMESTAMP,
          resolution_notes = $2
      WHERE id = $3 AND resolved = false
      RETURNING *
    `, [adminId, resolutionNotes, alertId]);

    if (result.rows.length === 0) {
      return sendNotFound(res, 'Alert not found or already resolved');
    }

    return sendSuccess(res, result.rows[0], 'Alert resolved successfully');

  } catch (error) {
    console.error('Resolve alert error:', error);
    return sendError(res, 'Failed to resolve alert', error);
  }
};

/**
 * Get IP geolocation and risk assessment
 */
export const getIPAnalysis = async (req: Request, res: Response) => {
  try {
    const { ip } = req.params;

    if (!ip) {
      return sendError(res, 'IP address is required', null, 400);
    }

    const [geolocation, riskScore] = await Promise.all([
      GeolocationService.getGeolocation(ip),
      GeolocationService.assessIPRisk(ip)
    ]);

    // Get recent activity for this IP
    const recentActivity = await query(`
      SELECT event_type, COUNT(*) as count, MAX(timestamp) as last_seen
      FROM security_events 
      WHERE ip = $1 AND timestamp > NOW() - INTERVAL '7 days'
      GROUP BY event_type
      ORDER BY count DESC
    `, [ip]);

    return sendSuccess(res, {
      ip,
      geolocation,
      riskScore,
      recentActivity: recentActivity.rows
    }, 'IP analysis completed successfully');

  } catch (error) {
    console.error('IP analysis error:', error);
    return sendError(res, 'Failed to analyze IP address', error);
  }
};
