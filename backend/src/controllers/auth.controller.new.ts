import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import jwt, { SignOptions } from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Admin } from '../models/Admin';
import { rateLimit } from 'express-rate-limit';
import { query } from '../config/database';
import { logAuthEvent, AuthEventType, SecurityLevel } from '../utils/authLogger';
import {
  validatePasswordStrength,
  isPasswordInHistory,
  addPasswordToHistory,
  hasPasswordExpired,
  getDaysUntilPasswordExpires
} from '../utils/passwordPolicy';
import { SecurityMonitor, RiskLevel } from '../utils/securityMonitor';
import { GeolocationService } from '../utils/geolocation';
import { MLAnomalyDetectionEngine } from '../utils/mlAnomalyDetection';
import SessionManager from '../services/sessionManager';
import PasswordResetService from '../services/passwordResetService';
// // import { JWTUtils, JWT_CONFIG } from '../config/jwt.config';

// Extend Request interface to include user and sessionID
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: number;
        email: string;
        role: string;
        isMainAdmin: boolean;
      };
      sessionID?: string;
    }
  }
}

// PROFESSIONAL-GRADE RATE LIMITING
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Only 3 attempts per window (stricter)
  message: {
    success: false,
    message: 'Too many login attempts from this IP',
    error: 'Account temporarily locked. Try again after 15 minutes or contact support.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests
  skipSuccessfulRequests: true,
  // Custom key generator for IP-based limiting
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  // Custom handler for rate limit exceeded
  handler: (req, res) => {
    console.warn(`Rate limit exceeded for IP: ${req.ip} at ${new Date().toISOString()}`);
    res.status(429).json({
      success: false,
      message: 'Too many login attempts from this IP address',
      error: 'Your IP has been temporarily blocked due to suspicious activity. Please try again later or contact support.',
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: Math.ceil(15 * 60) // 15 minutes in seconds
    });
  }
});

// Aggressive rate limiting for repeated failures
export const aggressiveLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 attempts per hour across all endpoints
  message: {
    success: false,
    message: 'Suspicious activity detected',
    error: 'Your IP has been blocked due to suspicious activity. Contact support if this is an error.',
    code: 'SUSPICIOUS_ACTIVITY'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Admin login with HTTP-only cookies
 */
export const adminLogin = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;
    const ip = req.ip || 'unknown';
    const userAgent = req.get('User-Agent') || '';
    const sessionId = req.sessionID || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // ENTERPRISE-GRADE SECURITY MONITORING

    // Generate device fingerprint
    const deviceFingerprint = SecurityMonitor.generateDeviceFingerprint(req);
    const deviceInfo = SecurityMonitor.parseDeviceInfo(userAgent);

    // Get geolocation data
    const geolocation = await GeolocationService.getGeolocation(ip);
    const ipRiskScore = await GeolocationService.assessIPRisk(ip);

    // Find admin by email
    const admin = await Admin.findByEmail(email);
    if (!admin) {
      // Log failed login attempt for non-existent user
      await SecurityMonitor.logLoginAttempt({
        email,
        ip,
        userAgent,
        success: false,
        failureReason: 'USER_NOT_FOUND',
        geolocation,
        deviceFingerprint,
        sessionId
      });

      // Log security event
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.LOGIN_FAILED,
        message: 'Login attempt for non-existent admin account',
        email,
        ip,
        userAgent,
        severity: SecurityLevel.WARNING,
        sessionId,
        geolocation,
        deviceFingerprint,
        riskScore: ipRiskScore + 20, // Higher risk for non-existent accounts
        details: { reason: 'USER_NOT_FOUND' }
      });

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if account is locked
    if (admin.lockUntil && admin.lockUntil > new Date()) {
      return res.status(423).json({
        success: false,
        message: 'Account is locked',
        error: 'Please try again later or contact support'
      });
    }

    // Verify password
    if (!admin.password) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      // PROFESSIONAL PROGRESSIVE ACCOUNT LOCKOUT
      const newAttempts = (admin.failedLoginAttempts || 0) + 1;
      let lockUntil: Date | undefined;

      // Progressive lockout periods
      if (newAttempts >= 3 && newAttempts < 5) {
        lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
      } else if (newAttempts >= 5 && newAttempts < 10) {
        lockUntil = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
      } else if (newAttempts >= 10) {
        lockUntil = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
      }

      await Admin.updateFailedLoginAttempts(admin.id!, newAttempts, lockUntil);

      // Log failed login attempt
      await SecurityMonitor.logLoginAttempt({
        email,
        ip,
        userAgent,
        success: false,
        failureReason: 'INVALID_PASSWORD',
        geolocation,
        deviceFingerprint,
        sessionId
      });

      // Log comprehensive security event
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.LOGIN_FAILED,
        message: `Failed login attempt ${newAttempts} - Invalid password`,
        adminId: admin.id,
        email,
        ip,
        userAgent,
        severity: newAttempts >= 5 ? SecurityLevel.WARNING : SecurityLevel.INFO,
        sessionId,
        geolocation,
        deviceFingerprint,
        riskScore: ipRiskScore + (newAttempts * 5), // Increase risk with attempts
        details: {
          reason: 'INVALID_PASSWORD',
          attempts: newAttempts,
          locked: !!lockUntil,
          lockUntil: lockUntil?.toISOString()
        }
      });

      // Generic error message to prevent user enumeration
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        code: 'AUTHENTICATION_FAILED'
      });
    }

    // ENTERPRISE-GRADE PASSWORD SECURITY CHECKS

    // Check if password has expired
    const passwordExpired = await hasPasswordExpired(admin.id!, true);
    if (passwordExpired) {
      await logAuthEvent(
        AuthEventType.PASSWORD_EXPIRED,
        'Login blocked - password expired',
        { adminId: admin.id, email },
        SecurityLevel.WARNING
      );

      return res.status(403).json({
        success: false,
        message: 'Password has expired',
        error: 'Please reset your password to continue',
        code: 'PASSWORD_EXPIRED',
        requirePasswordReset: true
      });
    }

    // Check if password change is required
    if (admin.mustChangePassword) {
      await logAuthEvent(
        AuthEventType.PASSWORD_CHANGE_REQUIRED,
        'Login blocked - password change required',
        { adminId: admin.id, email },
        SecurityLevel.INFO
      );

      return res.status(403).json({
        success: false,
        message: 'Password change required',
        error: 'You must change your password before continuing',
        code: 'PASSWORD_CHANGE_REQUIRED',
        requirePasswordChange: true
      });
    }

    // Check days until password expires (warn if < 7 days)
    const daysUntilExpiry = await getDaysUntilPasswordExpires(admin.id!, true);
    const passwordExpiryWarning = daysUntilExpiry !== null && daysUntilExpiry <= 7 && daysUntilExpiry > 0;

    // ENTERPRISE-GRADE TRAVEL ANALYSIS & DEVICE TRACKING

    // Get previous login location for travel analysis
    let travelAnalysis = null;
    let travelRiskScore = 0;

    try {
      const previousLoginResult = await query(`
        SELECT geolocation, timestamp FROM security_events
        WHERE admin_id = $1 AND event_type = $2
        ORDER BY timestamp DESC
        LIMIT 1 OFFSET 1
      `, [admin.id, AuthEventType.LOGIN_SUCCESS]);

      if (previousLoginResult.rows.length > 0 && geolocation) {
        const previousLogin = previousLoginResult.rows[0];
        const previousGeolocation = previousLogin.geolocation;
        const timeDiff = (new Date().getTime() - new Date(previousLogin.timestamp).getTime()) / (1000 * 60); // minutes

        if (previousGeolocation && previousGeolocation.lat && previousGeolocation.lon) {
          travelAnalysis = GeolocationService.analyzeTravelPattern(
            previousGeolocation,
            geolocation,
            timeDiff
          );

          travelRiskScore = travelAnalysis.riskScore;

          // Create alert for impossible travel
          if (travelAnalysis.isImpossibleTravel) {
            await SecurityMonitor.createSecurityAlert({
              alertType: 'IMPOSSIBLE_TRAVEL' as any,
              title: 'Impossible Travel Detected',
              description: `Admin ${email} logged in from ${geolocation.city}, ${geolocation.country} but was previously in ${previousGeolocation.city}, ${previousGeolocation.country} ${Math.round(timeDiff)} minutes ago. Required speed: ${Math.round(travelAnalysis.maxPossibleSpeed || 0)} km/h`,
              severity: 'high',
              adminId: admin.id,
              ip,
              metadata: { travelAnalysis }
            });
          }
        }
      }
    } catch (travelError) {
      console.error('Error analyzing travel patterns:', travelError);
    }

    // Track device
    await SecurityMonitor.trackDevice(
      undefined, // userId
      admin.id,   // adminId
      deviceInfo,
      ip,
      false // not automatically trusted
    );

    // ADVANCED ML-BASED ANOMALY DETECTION (temporarily disabled due to database issues)
    let mlAnomalyScore: {
      totalScore: number;
      riskLevel: 'low' | 'medium' | 'high' | 'critical';
      confidence: number;
      timeAnomaly: number;
      locationAnomaly: number;
      deviceAnomaly: number;
      frequencyAnomaly: number;
      behaviorAnomaly: number;
    } = {
      totalScore: 0,
      riskLevel: 'low',
      confidence: 0,
      timeAnomaly: 0,
      locationAnomaly: 0,
      deviceAnomaly: 0,
      frequencyAnomaly: 0,
      behaviorAnomaly: 0
    };
    try {
      mlAnomalyScore = await MLAnomalyDetectionEngine.analyzeLoginAttempt(
        admin.id!,
        {
          timestamp: new Date(),
          ipAddress: ip,
          country: geolocation?.country || 'unknown',
          deviceType: deviceInfo.type,
          browser: deviceInfo.browser || 'unknown',
          userAgent
        }
      );
    } catch (mlError) {
      console.error('ML Anomaly Detection error (continuing without it):', mlError);
      // Keep the default values set above
    }

    // Calculate total risk score with ML anomaly detection
    const totalRiskScore = Math.min(100, Math.round(
      (ipRiskScore * 0.3) +
      (travelRiskScore * 0.3) +
      (mlAnomalyScore.totalScore * 0.4) // Give ML score higher weight
    ));

    // Log successful login attempt
    await SecurityMonitor.logLoginAttempt({
      email,
      ip,
      userAgent,
      success: true,
      geolocation,
      deviceFingerprint,
      sessionId
    });

    // Reset failed login attempts and update last login
    await Admin.updateLastLogin(admin.id!);

    // Log comprehensive successful login event
    await SecurityMonitor.logSecurityEvent({
      eventType: AuthEventType.LOGIN_SUCCESS,
      message: 'Admin login successful',
      adminId: admin.id,
      email,
      ip,
      userAgent,
      severity: totalRiskScore > RiskLevel.MEDIUM ? SecurityLevel.WARNING : SecurityLevel.INFO,
      sessionId,
      geolocation,
      deviceFingerprint,
      riskScore: totalRiskScore,
      details: {
        passwordExpiryWarning,
        daysUntilExpiry,
        travelAnalysis,
        deviceInfo,
        ipRiskScore,
        travelRiskScore,
        mlAnomalyAnalysis: {
          totalScore: mlAnomalyScore.totalScore,
          riskLevel: mlAnomalyScore.riskLevel,
          confidence: mlAnomalyScore.confidence,
          timeAnomaly: mlAnomalyScore.timeAnomaly,
          locationAnomaly: mlAnomalyScore.locationAnomaly,
          deviceAnomaly: mlAnomalyScore.deviceAnomaly,
          frequencyAnomaly: mlAnomalyScore.frequencyAnomaly,
          behaviorAnomaly: mlAnomalyScore.behaviorAnomaly
        }
      }
    });

    // PROFESSIONAL JWT TOKEN GENERATION with security
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
    const jwtOptions: SignOptions = {
      expiresIn: '2h', // Fixed string value
      issuer: process.env.JWT_ISSUER || 'mabourse-admin-portal',
      audience: process.env.JWT_AUDIENCE || 'mabourse-admin-users'
    };

    // Generate unique JWT ID for session tracking
    const jti = `${admin.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
    const clientUserAgent = req.get('User-Agent')?.substring(0, 100) || 'unknown';

    const token = jwt.sign(
      {
        id: admin.id,
        email: admin.email,
        role: admin.role,
        isMainAdmin: admin.isMainAdmin,
        // Security additions
        jti, // Unique token ID for session tracking
        ip: clientIp, // IP binding for additional security
        userAgent: clientUserAgent // User agent binding (truncated)
      },
      jwtSecret,
      jwtOptions
    );

    // CREATE SESSION RECORD for professional session management
    try {
      await SessionManager.createSession(
        admin.id!,
        jti,
        clientIp,
        clientUserAgent
      );
      console.log(`✅ Session created for admin ${admin.email} with JTI: ${jti}`);
    } catch (sessionError) {
      console.error('⚠️ Failed to create session record:', sessionError);
      // Continue with login even if session creation fails
    }

    // PROFESSIONAL-GRADE SECURE COOKIE CONFIGURATION
    res.cookie('auth_token', token, {
      httpOnly: true, // Prevents XSS attacks
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax', // CSRF protection - lax for development
      maxAge: 2 * 60 * 60 * 1000, // 2 hours (matches JWT expiration)
      path: '/', // Cookie path
      domain: process.env.NODE_ENV === 'production' ? process.env.COOKIE_DOMAIN : undefined,
      // Additional security flags
      priority: 'high' // High priority cookie
    });

    // Log successful login for security monitoring
    console.log(`Successful admin login: ${admin.email} from IP: ${req.ip} at ${new Date().toISOString()}`);

    // Parse privileges
    const privileges = typeof admin.privileges === 'string' 
      ? JSON.parse(admin.privileges) 
      : admin.privileges;

    // Return success response with security information
    const response: any = {
      success: true,
      message: 'Login successful',
      data: {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin,
          privileges,
          twoFactorEnabled: admin.twoFactorEnabled || false
        }
      }
    };

    // Add password expiry warning if applicable
    if (passwordExpiryWarning && daysUntilExpiry !== null) {
      response.warning = {
        type: 'PASSWORD_EXPIRY',
        message: `Your password will expire in ${daysUntilExpiry} day${daysUntilExpiry === 1 ? '' : 's'}`,
        daysRemaining: daysUntilExpiry,
        action: 'Please change your password soon to avoid account lockout'
      };
    }

    res.json(response);

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Admin logout
 */
export const adminLogout = async (req: Request, res: Response) => {
  try {
    // Get token to extract JTI for session invalidation
    const token = req.cookies.auth_token;

    if (token) {
      try {
        const decoded = jwt.decode(token) as any;
        if (decoded && decoded.jti) {
          // Invalidate the session
          await SessionManager.invalidateSession(decoded.jti);
          console.log(`✅ Session invalidated for JTI: ${decoded.jti}`);
        }
      } catch (tokenError) {
        console.error('Error decoding token for logout:', tokenError);
        // Continue with logout even if session invalidation fails
      }
    }

    // Clear the authentication cookie
    res.clearCookie('auth_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      path: '/'
    });

    // Log successful logout
    const adminEmail = (req as any).user?.email || 'unknown';
    console.log(`Successful admin logout: ${adminEmail} from IP: ${req.ip} at ${new Date().toISOString()}`);

    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Admin logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Get current admin profile
 */
export const getCurrentAdmin = async (req: Request, res: Response) => {
  try {
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const admin = await Admin.findById(adminId);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    // Parse privileges
    const privileges = typeof admin.privileges === 'string' 
      ? JSON.parse(admin.privileges) 
      : admin.privileges;

    res.json({
      success: true,
      message: 'Admin profile retrieved successfully',
      data: {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin,
          privileges,
          twoFactorEnabled: admin.twoFactorEnabled || false,
          lastLogin: admin.lastLogin,
          createdAt: admin.createdAt,
          updatedAt: admin.updatedAt
        }
      }
    });

  } catch (error) {
    console.error('Get admin profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve admin profile',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Change admin password with enterprise-grade security
 */
export const changePassword = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }

    // Get admin from database
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, admin.password!);
    if (!isCurrentPasswordValid) {
      await logAuthEvent(
        AuthEventType.PASSWORD_CHANGE_FAILED,
        'Password change failed - invalid current password',
        { adminId, email: admin.email },
        SecurityLevel.WARNING
      );

      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Validate new password strength
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.valid) {
      return res.status(400).json({
        success: false,
        message: 'New password does not meet security requirements',
        error: passwordValidation.message,
        suggestions: passwordValidation.suggestions,
        score: passwordValidation.score
      });
    }

    // Check if new password is in history
    const isPasswordReused = await isPasswordInHistory(adminId, newPassword, true);
    if (isPasswordReused) {
      return res.status(400).json({
        success: false,
        message: 'Cannot reuse a recent password',
        error: 'Please choose a password you have not used recently'
      });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);
    const passwordExpiresAt = new Date(Date.now() + (90 * 24 * 60 * 60 * 1000)); // 90 days

    // Update admin password
    await Admin.update(adminId, {
      password: hashedNewPassword,
      passwordUpdatedAt: new Date(),
      passwordExpiresAt,
      mustChangePassword: false,
      failedLoginAttempts: 0,
      lockUntil: undefined
    });

    // Add to password history
    await addPasswordToHistory(adminId, hashedNewPassword, true);

    // Log security event
    await logAuthEvent(
      AuthEventType.PASSWORD_CHANGED,
      'Admin password changed successfully',
      {
        adminId,
        email: admin.email,
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        passwordScore: passwordValidation.score
      },
      SecurityLevel.INFO
    );

    res.json({
      success: true,
      message: 'Password changed successfully',
      data: {
        passwordScore: passwordValidation.score,
        expiresAt: passwordExpiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Password change error:', error);

    // Log security event
    await logAuthEvent(
      AuthEventType.PASSWORD_CHANGE_FAILED,
      'Password change failed - system error',
      {
        adminId: req.user?.id,
        error: (error as Error).message
      },
      SecurityLevel.ERROR
    );

    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Verify authentication status
 */
export const verifyAuth = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }

    res.json({
      success: true,
      message: 'Authentication verified',
      data: {
        user: req.user
      }
    });

  } catch (error) {
    console.error('Verify auth error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication verification failed',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Request password reset
 */
export const requestPasswordReset = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email } = req.body;
    const ip = req.ip || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    const result = await PasswordResetService.generateResetToken(email, ip, userAgent);

    // Always return success for security (don't reveal if email exists)
    res.json({
      success: true,
      message: result.message
    });

    // TODO: Send email with reset link containing result.tokenId
    // Email should contain: https://yourdomain.com/reset-password?token=${result.tokenId}

  } catch (error) {
    console.error('Password reset request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process password reset request',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Reset password with token
 */
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { token, newPassword } = req.body;

    // Parse token (format: tokenId:actualToken)
    const [tokenId, actualToken] = token.split(':');
    if (!tokenId || !actualToken) {
      return res.status(400).json({
        success: false,
        message: 'Invalid token format'
      });
    }

    const result = await PasswordResetService.resetPassword(tokenId, actualToken, newPassword);

    res.status(result.success ? 200 : 400).json(result);

  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset password',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};
