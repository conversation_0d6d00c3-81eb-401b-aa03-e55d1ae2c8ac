import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { Admin } from '../models/Admin';
import {
  generateSecret,
  generateQRCode,
  verifyToken,
  generateBackupCodes,
  verifyBackupCode
} from '../utils/twoFactor';
import {
  sendSuccess,
  sendError,
  sendNotFound,
  sendValidationError,
  sendUnauthorized
} from '../utils/apiResponse';
import { SecurityMonitor } from '../utils/securityMonitor';
import { AuthEventType, SecurityLevel } from '../utils/authLogger';

// Initialize 2FA setup
export const initializeTwoFactor = async (req: Request, res: Response) => {
  try {
    const adminId = req.user?.id;
    if (!adminId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return sendNotFound(res, 'Admin not found');
    }

    // Check if 2FA is already enabled
    if (admin.twoFactorEnabled) {
      return sendError(res, '2FA is already enabled', null, 400);
    }

    // Generate a new secret
    const secret = generateSecret(admin.email);

    // Generate QR code
    const qrCode = await generateQRCode(secret, admin.email);

    // Save temporary secret
    await Admin.update(adminId, { twoFactorTempSecret: secret });

    // Log security event
    await SecurityMonitor.logSecurityEvent({
      eventType: AuthEventType.TWO_FACTOR_SETUP_INITIATED,
      message: '2FA setup initiated',
      adminId,
      email: admin.email,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      severity: SecurityLevel.INFO,
      sessionId: req.sessionID,
      details: { action: 'INITIALIZE_2FA' }
    });

    return sendSuccess(res, {
      qrCode,
      secret // Include secret for manual entry if QR code doesn't work
    }, '2FA initialization successful');
  } catch (error) {
    console.error('Initialize 2FA error:', error);
    return sendError(res, 'Failed to initialize 2FA', error);
  }
};

// Verify and enable 2FA
export const verifyAndEnableTwoFactor = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { token } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return sendNotFound(res, 'Admin not found');
    }

    // Check if 2FA is already enabled
    if (admin.twoFactorEnabled) {
      return sendError(res, '2FA is already enabled', null, 400);
    }

    // Check if temporary secret exists
    if (!admin.twoFactorTempSecret) {
      return sendError(res, '2FA setup not initialized', null, 400);
    }

    // Verify token
    const isValid = verifyToken(token, admin.twoFactorTempSecret);
    if (!isValid) {
      // Log failed verification
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.TWO_FACTOR_VERIFICATION_FAILED,
        message: '2FA verification failed during setup',
        adminId,
        email: admin.email,
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.WARNING,
        sessionId: req.sessionID,
        details: { action: 'VERIFY_2FA_SETUP', reason: 'INVALID_TOKEN' }
      });

      return sendError(res, 'Invalid token', null, 400);
    }

    // Generate backup codes
    const backupCodes = generateBackupCodes();

    // Enable 2FA
    await Admin.update(adminId, {
      twoFactorSecret: admin.twoFactorTempSecret,
      twoFactorEnabled: true,
      twoFactorTempSecret: undefined,
      twoFactorBackupCodes: backupCodes
    });

    // Log successful 2FA enablement
    await SecurityMonitor.logSecurityEvent({
      eventType: AuthEventType.TWO_FACTOR_ENABLED,
      message: '2FA successfully enabled',
      adminId,
      email: admin.email,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      severity: SecurityLevel.INFO,
      sessionId: req.sessionID,
      details: { action: 'ENABLE_2FA', backupCodesGenerated: backupCodes.length }
    });

    return sendSuccess(res, {
      backupCodes
    }, '2FA enabled successfully');
  } catch (error) {
    console.error('Verify and enable 2FA error:', error);
    return sendError(res, 'Failed to enable 2FA', error);
  }
};

// Verify 2FA token during login
export const verifyTwoFactorToken = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { adminId, token, isBackupCode = false } = req.body;

    // Get admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return sendNotFound(res, 'Admin not found');
    }

    // Check if 2FA is enabled
    if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
      return sendError(res, '2FA is not enabled for this account', null, 400);
    }

    let isValid = false;
    let updatedBackupCodes = null;

    if (isBackupCode) {
      // Verify backup code
      if (!admin.twoFactorBackupCodes || admin.twoFactorBackupCodes.length === 0) {
        return sendError(res, 'No backup codes available', null, 400);
      }

      const backupCodes = admin.twoFactorBackupCodes;
      const result = verifyBackupCode(token, backupCodes);
      isValid = result.valid;
      updatedBackupCodes = result.remainingCodes;

      // Update backup codes if valid
      if (isValid) {
        await Admin.update(adminId, {
          twoFactorBackupCodes: updatedBackupCodes
        });
      }
    } else {
      // Verify TOTP token
      isValid = verifyToken(token, admin.twoFactorSecret);
    }

    if (!isValid) {
      // Log failed verification
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.TWO_FACTOR_VERIFICATION_FAILED,
        message: `2FA verification failed during login (${isBackupCode ? 'backup code' : 'TOTP'})`,
        adminId,
        email: admin.email,
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.WARNING,
        sessionId: req.sessionID,
        details: {
          action: 'VERIFY_2FA_LOGIN',
          reason: 'INVALID_TOKEN',
          tokenType: isBackupCode ? 'BACKUP_CODE' : 'TOTP'
        }
      });

      return sendError(res, 'Invalid token', null, 400);
    }

    // Log successful verification
    await SecurityMonitor.logSecurityEvent({
      eventType: AuthEventType.TWO_FACTOR_VERIFIED,
      message: `2FA verification successful (${isBackupCode ? 'backup code' : 'TOTP'})`,
      adminId,
      email: admin.email,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      severity: SecurityLevel.INFO,
      sessionId: req.sessionID,
      details: {
        action: 'VERIFY_2FA_LOGIN',
        tokenType: isBackupCode ? 'BACKUP_CODE' : 'TOTP',
        remainingBackupCodes: updatedBackupCodes?.length || null
      }
    });

    return sendSuccess(res, {
      verified: true,
      remainingBackupCodes: updatedBackupCodes?.length || null
    }, '2FA verification successful');
  } catch (error) {
    console.error('Verify 2FA token error:', error);
    return sendError(res, 'Failed to verify 2FA token', error);
  }
};

// Disable 2FA
export const disableTwoFactor = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { token } = req.body;
    const adminId = req.user?.id;

    if (!adminId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return sendNotFound(res, 'Admin not found');
    }

    // Check if 2FA is enabled
    if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
      return sendError(res, '2FA is not enabled for this account', null, 400);
    }

    // Verify token
    const isValid = verifyToken(token, admin.twoFactorSecret);
    if (!isValid) {
      // Log failed verification
      await SecurityMonitor.logSecurityEvent({
        eventType: AuthEventType.TWO_FACTOR_VERIFICATION_FAILED,
        message: '2FA verification failed during disable attempt',
        adminId,
        email: admin.email,
        ip: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        severity: SecurityLevel.WARNING,
        sessionId: req.sessionID,
        details: { action: 'DISABLE_2FA', reason: 'INVALID_TOKEN' }
      });

      return sendError(res, 'Invalid token', null, 400);
    }

    // Disable 2FA
    await Admin.update(adminId, {
      twoFactorSecret: undefined,
      twoFactorEnabled: false,
      twoFactorTempSecret: undefined,
      twoFactorBackupCodes: []
    });

    // Log 2FA disabled
    await SecurityMonitor.logSecurityEvent({
      eventType: AuthEventType.TWO_FACTOR_DISABLED,
      message: '2FA disabled',
      adminId,
      email: admin.email,
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      severity: SecurityLevel.WARNING,
      sessionId: req.sessionID,
      details: { action: 'DISABLE_2FA' }
    });

    return sendSuccess(res, null, '2FA disabled successfully');
  } catch (error) {
    console.error('Disable 2FA error:', error);
    return sendError(res, 'Failed to disable 2FA', error);
  }
};

// Get 2FA status
export const getTwoFactorStatus = async (req: Request, res: Response) => {
  try {
    const adminId = req.user?.id;
    if (!adminId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return sendNotFound(res, 'Admin not found');
    }

    const backupCodesCount = admin.twoFactorBackupCodes
      ? admin.twoFactorBackupCodes.length
      : 0;

    return sendSuccess(res, {
      enabled: admin.twoFactorEnabled,
      backupCodesCount
    }, '2FA status retrieved successfully');
  } catch (error) {
    console.error('Get 2FA status error:', error);
    return sendError(res, 'Failed to get 2FA status', error);
  }
};
