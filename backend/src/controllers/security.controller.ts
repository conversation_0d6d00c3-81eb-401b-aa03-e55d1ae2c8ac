/**
 * Security Controller
 * 
 * Handles security-related operations such as fetching security events,
 * managing security settings, and detecting anomalies.
 */

import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { sendSuccess, sendError, sendValidationError } from '../utils/apiResponse';
import authLogger, { AuthEventType, SecurityLevel } from '../utils/authLogger';
import { detectAnomalies } from '../utils/anomalyDetection';

const prisma = new PrismaClient();

/**
 * Get security events with optional filtering
 */
export const getSecurityEvents = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors);
    }

    // Parse query parameters
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const eventType = req.query.eventType as string | undefined;
    const severity = req.query.severity as string | undefined;
    const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
    const email = req.query.email as string | undefined;
    const ip = req.query.ip as string | undefined;

    // Build where clause
    const where: any = {};
    
    // Date range filter
    if (startDate && endDate) {
      where.timestamp = {
        gte: startDate,
        lte: endDate,
      };
    } else if (startDate) {
      where.timestamp = {
        gte: startDate,
      };
    } else if (endDate) {
      where.timestamp = {
        lte: endDate,
      };
    }
    
    // Other filters
    if (eventType) where.eventType = eventType;
    if (severity) where.severity = severity;
    if (userId) where.userId = userId;
    if (email) where.email = { contains: email };
    if (ip) where.ip = ip;

    // Fetch events from database
    const events = await prisma.securityEvent.findMany({
      where,
      orderBy: {
        timestamp: 'desc',
      },
      take: 1000, // Limit to 1000 events for performance
    });

    // Calculate statistics
    const stats = {
      totalEvents: events.length,
      successfulLogins: events.filter(e => e.eventType === AuthEventType.LOGIN_SUCCESS).length,
      failedLogins: events.filter(e => e.eventType === AuthEventType.LOGIN_FAILURE).length,
      suspiciousActivities: events.filter(e => e.eventType === AuthEventType.SUSPICIOUS_ACTIVITY).length,
      accountLockouts: events.filter(e => e.eventType === AuthEventType.ACCOUNT_LOCKED).length,
      twoFactorSuccesses: events.filter(e => e.eventType === AuthEventType.TWO_FACTOR_SUCCESS).length,
      twoFactorFailures: events.filter(e => e.eventType === AuthEventType.TWO_FACTOR_FAILURE).length,
    };

    // Check for anomalies
    const anomalies = detectAnomalies(events);

    // Return events and stats
    return sendSuccess(
      res,
      {
        events,
        stats,
        anomalies,
      },
      'Security events retrieved successfully'
    );
  } catch (error) {
    console.error('Error fetching security events:', error);
    return sendError(res, 'Failed to fetch security events', error);
  }
};

/**
 * Get security settings
 */
export const getSecuritySettings = async (req: Request, res: Response) => {
  try {
    // Fetch security settings from database
    const settings = await prisma.securitySettings.findFirst();

    // Return settings
    return sendSuccess(
      res,
      { settings },
      'Security settings retrieved successfully'
    );
  } catch (error) {
    console.error('Error fetching security settings:', error);
    return sendError(res, 'Failed to fetch security settings', error);
  }
};

/**
 * Update security settings
 */
export const updateSecuritySettings = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors);
    }

    const {
      maxLoginAttempts,
      lockoutDuration,
      passwordExpiryDays,
      requireStrongPasswords,
      twoFactorEnabled,
      minPasswordLength,
    } = req.body;

    // Update settings in database
    const settings = await prisma.securitySettings.upsert({
      where: { id: 1 }, // Assuming there's only one settings record
      update: {
        maxLoginAttempts,
        lockoutDuration,
        passwordExpiryDays,
        requireStrongPasswords,
        twoFactorEnabled,
        minPasswordLength,
      },
      create: {
        maxLoginAttempts,
        lockoutDuration,
        passwordExpiryDays,
        requireStrongPasswords,
        twoFactorEnabled,
        minPasswordLength,
      },
    });

    // Log the settings update
    authLogger.logAuthEvent(
      AuthEventType.SECURITY_SETTINGS_UPDATED,
      'Security settings updated',
      {
        adminId: req.user?.id,
        email: req.user?.email,
        settings,
      },
      SecurityLevel.INFO
    );

    // Return updated settings
    return sendSuccess(
      res,
      { settings },
      'Security settings updated successfully'
    );
  } catch (error) {
    console.error('Error updating security settings:', error);
    return sendError(res, 'Failed to update security settings', error);
  }
};

export default {
  getSecurityEvents,
  getSecuritySettings,
  updateSecuritySettings,
};
