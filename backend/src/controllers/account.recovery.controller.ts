/**
 * Account Recovery Controller
 * 
 * Handles advanced account recovery operations beyond simple password reset,
 * including multi-factor recovery options and account unlock.
 */

import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { sendEmail } from '../utils/email';
import {
  sendSuccess,
  sendError,
  sendNotFound,
  sendValidationError,
  sendUnauthorized
} from '../utils/apiResponse';
import authLogger, {
  AuthEventType,
  SecurityLevel,
} from '../utils/authLogger';
import passwordPolicy from '../utils/passwordPolicy';
import { generateQRCode } from '../utils/twoFactorAuth';
import { Admin } from '../models/Admin';
import { User } from '../models/User';

/**
 * Request account recovery
 * Initiates the account recovery process by sending a recovery token
 */
export const requestRecovery = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors);
    }

    const { email, accountType } = req.body;

    // Find the account based on account type
    const account = accountType === 'admin'
      ? await Admin.findByEmail(email)
      : await User.findByEmail(email);

    // Always return success even if account doesn't exist (security best practice)
    if (!account) {
      // Log the attempt but don't reveal that the account doesn't exist
      authLogger.logAuthEvent(
        AuthEventType.RECOVERY_REQUEST,
        `Recovery requested for non-existent account: ${email}`,
        { email, accountType, ip: req.ip },
        SecurityLevel.INFO
      );
      
      return sendSuccess(
        res,
        null,
        'If your account exists, recovery instructions have been sent to your email'
      );
    }

    // Generate recovery token
    const recoveryToken = crypto.randomBytes(32).toString('hex');
    const recoveryTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Update account with recovery token
    await model.update({
      where: { id: account.id },
      data: {
        resetPasswordToken: recoveryToken,
        resetPasswordExpires: recoveryTokenExpiry,
      },
    });

    // Send recovery email
    const recoveryLink = `${process.env.FRONTEND_URL}/account-recovery/${accountType}/${recoveryToken}`;
    
    await sendEmail({
      to: email,
      subject: 'Account Recovery - MaBourse',
      html: `
        <h1>Account Recovery</h1>
        <p>You requested to recover your account. Click the link below to continue:</p>
        <a href="${recoveryLink}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Recover Account</a>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this, please ignore this email or contact support if you're concerned.</p>
      `,
    });

    // Log the recovery request
    authLogger.logAuthEvent(
      AuthEventType.RECOVERY_REQUEST,
      `Recovery requested for account: ${email}`,
      { accountId: account.id, email, accountType, ip: req.ip },
      SecurityLevel.INFO
    );

    return sendSuccess(
      res,
      null,
      'Recovery instructions have been sent to your email'
    );
  } catch (error) {
    console.error('Error requesting account recovery:', error);
    return sendError(res, 'Failed to request account recovery', error);
  }
};

/**
 * Verify recovery token
 * Checks if the recovery token is valid and returns account info
 */
export const verifyRecoveryToken = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors);
    }

    const { token, accountType } = req.params;

    // Find account with the token based on account type
    // Note: This is a simplified implementation - token validation would need to be added to models
    const account = accountType === 'admin'
      ? await Admin.findByEmail('') // Placeholder - would need token lookup method
      : await User.findByEmail(''); // Placeholder - would need token lookup method

    if (!account) {
      return sendError(
        res,
        'Recovery token is invalid or has expired',
        null,
        401
      );
    }

    // Log the token verification
    authLogger.logAuthEvent(
      AuthEventType.RECOVERY_TOKEN_VERIFIED,
      `Recovery token verified for account: ${account.email}`,
      { accountId: account.id, email: account.email, accountType, ip: req.ip },
      SecurityLevel.INFO
    );

    return sendSuccess(
      res,
      {
        email: account.email,
        name: account.name,
        hasTwoFactor: account.twoFactorEnabled,
      },
      'Recovery token is valid'
    );
  } catch (error) {
    console.error('Error verifying recovery token:', error);
    return sendError(res, 'Failed to verify recovery token', error);
  }
};

/**
 * Reset password with recovery token
 * Resets the password using a valid recovery token
 */
export const resetPassword = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors);
    }

    const { token, accountType } = req.params;
    const { password } = req.body;

    // Find account with the token based on account type
    // Note: This is a simplified implementation - token validation would need to be added to models
    const account = accountType === 'admin'
      ? await Admin.findByEmail('') // Placeholder - would need token lookup method
      : await User.findByEmail(''); // Placeholder - would need token lookup method

    if (!account) {
      return sendError(
        res,
        'Recovery token is invalid or has expired',
        null,
        401
      );
    }

    // Validate password strength
    const passwordValidation = passwordPolicy.validatePasswordStrength(password);
    if (!passwordValidation.valid) {
      return sendError(
        res,
        passwordValidation.message || 'Password does not meet security requirements',
        null,
        400
      );
    }

    // Check if password is in history
    const isInHistory = await passwordPolicy.isPasswordInHistory(account.id, password);
    if (isInHistory) {
      return sendError(
        res,
        'Password has been used recently. Please choose a different password.',
        null,
        400
      );
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Calculate password expiry date
    const now = new Date();
    const expiryDays = 90; // Default to 90 days
    const expiryDate = new Date(now);
    expiryDate.setDate(expiryDate.getDate() + expiryDays);

    // Update account with new password
    await model.update({
      where: { id: account.id },
      data: {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpires: null,
        failedLoginAttempts: 0,
        lockUntil: null,
        passwordUpdatedAt: now,
        passwordExpiresAt: expiryDate,
        mustChangePassword: false,
      },
    });

    // Add password to history
    await passwordPolicy.addPasswordToHistory(account.id, hashedPassword);

    // Log the password reset
    authLogger.logAuthEvent(
      AuthEventType.PASSWORD_RESET_SUCCESS,
      `Password reset successful for account: ${account.email}`,
      { accountId: account.id, email: account.email, accountType, ip: req.ip },
      SecurityLevel.INFO
    );

    // Send confirmation email
    await sendEmail({
      to: account.email,
      subject: 'Password Reset Confirmation - MaBourse',
      html: `
        <h1>Password Reset Confirmation</h1>
        <p>Your password has been successfully reset.</p>
        <p>If you didn't make this change, please contact support immediately.</p>
      `,
    });

    return sendSuccess(
      res,
      null,
      'Password has been reset successfully'
    );
  } catch (error) {
    console.error('Error resetting password:', error);
    return sendError(res, 'Failed to reset password', error);
  }
};

/**
 * Unlock account
 * Unlocks a locked account using a recovery token
 */
export const unlockAccount = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors);
    }

    const { token, accountType } = req.params;

    // Find account with the token based on account type
    // Note: This is a simplified implementation - token validation would need to be added to models
    const account = accountType === 'admin'
      ? await Admin.findByEmail('') // Placeholder - would need token lookup method
      : await User.findByEmail(''); // Placeholder - would need token lookup method

    if (!account) {
      return sendError(
        res,
        'Recovery token is invalid or has expired',
        null,
        401
      );
    }

    // Update account to unlock it
    await model.update({
      where: { id: account.id },
      data: {
        failedLoginAttempts: 0,
        lockUntil: null,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      },
    });

    // Log the account unlock
    authLogger.logAuthEvent(
      AuthEventType.ACCOUNT_UNLOCKED,
      `Account unlocked: ${account.email}`,
      { accountId: account.id, email: account.email, accountType, ip: req.ip },
      SecurityLevel.INFO
    );

    // Send confirmation email
    await sendEmail({
      to: account.email,
      subject: 'Account Unlocked - MaBourse',
      html: `
        <h1>Account Unlocked</h1>
        <p>Your account has been successfully unlocked.</p>
        <p>You can now log in with your credentials.</p>
        <p>If you didn't request this, please contact support immediately.</p>
      `,
    });

    return sendSuccess(
      res,
      null,
      'Account has been unlocked successfully'
    );
  } catch (error) {
    console.error('Error unlocking account:', error);
    return sendError(res, 'Failed to unlock account', error);
  }
};

export default {
  requestRecovery,
  verifyRecoveryToken,
  resetPassword,
  unlockAccount,
};
