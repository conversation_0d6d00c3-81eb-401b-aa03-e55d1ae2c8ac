import { Request, Response } from 'express';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import { sendEmail, generatePasswordResetEmail } from '../utils/email';
import { Admin } from '../models/Admin';

// Request password reset - TEMPORARILY DISABLED (needs password reset fields in Admin model)
export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Find admin by email
    const admin = await Admin.findByEmail(email);

    if (!admin) {
      // For security reasons, don't reveal that the email doesn't exist
      return res.status(200).json({
        message: 'If your email is registered, you will receive a password reset link'
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const hashedToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    // Save reset token to database - TEMPORARILY DISABLED
    // TODO: Add resetPasswordToken and resetPasswordExpires fields to Admin model
    // await Admin.update(admin.id!, {
    //   resetPasswordToken: hashedToken,
    //   resetPasswordExpires: new Date(Date.now() + 60 * 60 * 1000)
    // });

    // Create reset URL
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/admin/reset-password/${resetToken}`;

    // In development, just log the reset URL instead of sending an email
    if (process.env.NODE_ENV !== 'production') {
      console.log('==== DEVELOPMENT MODE ====');
      console.log('Password reset URL:', resetUrl);
      console.log('==========================');
    } else {
      // Generate email content
      const { text, html } = generatePasswordResetEmail(admin.name, resetUrl);

      // Send email
      await sendEmail({
        to: admin.email,
        subject: 'MaBourse Admin Password Reset',
        text,
        html
      });
    }

    res.status(200).json({
      message: 'If your email is registered, you will receive a password reset link'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Reset password with token
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      return res.status(400).json({ message: 'Token and password are required' });
    }

    // Hash the token
    const hashedToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    // Find admin with valid reset token - TEMPORARILY DISABLED
    // TODO: Implement token validation in Admin model
    const admin = null; // await Admin.findByResetToken(hashedToken);

    if (!admin) {
      return res.status(400).json({ message: 'Invalid or expired reset token' });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update admin with new password and clear reset token - TEMPORARILY DISABLED
    // TODO: Implement password update with token clearing in Admin model
    // await Admin.update(admin.id!, {
    //   password: hashedPassword,
    //   resetPasswordToken: null,
    //   resetPasswordExpires: null,
    //   failedLoginAttempts: 0,
    //   lockUntil: null
    // });

    res.status(200).json({ message: 'Password has been reset successfully' });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Validate reset token (used to check if token is valid before showing reset form)
export const validateResetToken = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }

    // Hash the token
    const hashedToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    // Find admin with valid reset token - TEMPORARILY DISABLED
    // TODO: Implement token validation in Admin model
    const admin = null; // await Admin.findByResetToken(hashedToken);

    if (!admin) {
      return res.status(400).json({ valid: false, message: 'Invalid or expired reset token' });
    }

    res.status(200).json({ valid: true, message: 'Token is valid' });
  } catch (error) {
    console.error('Validate reset token error:', error);
    res.status(500).json({ valid: false, message: 'Server error' });
  }
};
