/**
 * Contact Settings Controller
 * 
 * Production-grade controller for managing contact information and social media links
 * with proper validation, security, and error handling.
 */

import { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { ContactSettingsModel } from '../models/ContactSettings';
import { sendSuccess, sendError, sendValidationError } from '../utils/apiResponse';

/**
 * Get all contact settings (Admin only)
 */
export const getAllContactSettings = async (req: Request, res: Response) => {
  try {
    const settings = await ContactSettingsModel.getAll();
    return sendSuccess(res, settings, 'Contact settings retrieved successfully');
  } catch (error) {
    console.error('Get all contact settings error:', error);
    return sendError(res, 'Failed to retrieve contact settings', error);
  }
};

/**
 * Get contact settings grouped by category (Admin only)
 */
export const getContactSettingsGrouped = async (req: Request, res: Response) => {
  try {
    const settings = await ContactSettingsModel.getAllGrouped();
    return sendSuccess(res, settings, 'Contact settings retrieved successfully');
  } catch (error) {
    console.error('Get grouped contact settings error:', error);
    return sendError(res, 'Failed to retrieve contact settings', error);
  }
};

/**
 * Get public contact settings (Public endpoint)
 */
export const getPublicContactSettings = async (req: Request, res: Response) => {
  try {
    const settings = await ContactSettingsModel.getPublicSettings();
    
    // Set cache headers for better performance
    res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour cache
    
    return sendSuccess(res, settings, 'Public contact settings retrieved successfully');
  } catch (error) {
    console.error('Get public contact settings error:', error);
    return sendError(res, 'Failed to retrieve contact settings', error);
  }
};

/**
 * Get contact settings by category (Admin only)
 */
export const getContactSettingsByCategory = async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    
    if (!['general', 'social', 'contact', 'address'].includes(category)) {
      return sendError(res, 'Invalid category', null, 400);
    }

    const settings = await ContactSettingsModel.getByCategory(category);
    return sendSuccess(res, settings, `${category} contact settings retrieved successfully`);
  } catch (error) {
    console.error('Get contact settings by category error:', error);
    return sendError(res, 'Failed to retrieve contact settings', error);
  }
};

/**
 * Create a new contact setting (Admin only)
 */
export const createContactSetting = async (req: Request, res: Response) => {
  try {
    // Validate input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { settingKey, settingValue, settingType, category, displayOrder, isActive } = req.body;
    const adminId = (req as any).admin?.id;

    // Check if setting key already exists
    const existingSetting = await ContactSettingsModel.getByKey(settingKey);
    if (existingSetting) {
      return sendError(res, 'Setting key already exists', null, 409);
    }

    const newSetting = await ContactSettingsModel.create({
      settingKey,
      settingValue,
      settingType,
      category,
      displayOrder,
      isActive,
      createdByAdmin: adminId
    });

    return sendSuccess(res, newSetting, 'Contact setting created successfully', 201);
  } catch (error) {
    console.error('Create contact setting error:', error);
    return sendError(res, 'Failed to create contact setting', error);
  }
};

/**
 * Update a contact setting (Admin only)
 */
export const updateContactSetting = async (req: Request, res: Response) => {
  try {
    // Validate input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { id } = req.params;
    const { settingValue, settingType, category, displayOrder, isActive } = req.body;
    const adminId = (req as any).admin?.id;

    const updatedSetting = await ContactSettingsModel.update(parseInt(id), {
      settingValue,
      settingType,
      category,
      displayOrder,
      isActive,
      updatedByAdmin: adminId
    });

    if (!updatedSetting) {
      return sendError(res, 'Contact setting not found', null, 404);
    }

    return sendSuccess(res, updatedSetting, 'Contact setting updated successfully');
  } catch (error) {
    console.error('Update contact setting error:', error);
    return sendError(res, 'Failed to update contact setting', error);
  }
};

/**
 * Delete a contact setting (Admin only)
 */
export const deleteContactSetting = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const adminId = (req as any).admin?.id;

    const deleted = await ContactSettingsModel.delete(parseInt(id), adminId);

    if (!deleted) {
      return sendError(res, 'Contact setting not found', null, 404);
    }

    return sendSuccess(res, null, 'Contact setting deleted successfully');
  } catch (error) {
    console.error('Delete contact setting error:', error);
    return sendError(res, 'Failed to delete contact setting', error);
  }
};

/**
 * Bulk update contact settings (Admin only)
 */
export const bulkUpdateContactSettings = async (req: Request, res: Response) => {
  try {
    // Validate input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { updates } = req.body;
    const adminId = (req as any).admin?.id;

    if (!Array.isArray(updates) || updates.length === 0) {
      return sendError(res, 'Updates array is required and cannot be empty', null, 400);
    }

    const updatedSettings = await ContactSettingsModel.bulkUpdate(updates, adminId);

    return sendSuccess(res, updatedSettings, 'Contact settings updated successfully');
  } catch (error) {
    console.error('Bulk update contact settings error:', error);
    return sendError(res, 'Failed to update contact settings', error);
  }
};

/**
 * Validation rules for contact settings
 */
export const validateContactSetting = [
  body('settingKey')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Setting key must be between 1 and 100 characters')
    .matches(/^[a-z_]+$/)
    .withMessage('Setting key must contain only lowercase letters and underscores'),
  
  body('settingValue')
    .notEmpty()
    .withMessage('Setting value is required')
    .isLength({ max: 1000 })
    .withMessage('Setting value cannot exceed 1000 characters'),
  
  body('settingType')
    .optional()
    .isIn(['text', 'email', 'phone', 'url', 'address'])
    .withMessage('Setting type must be one of: text, email, phone, url, address'),
  
  body('category')
    .optional()
    .isIn(['general', 'social', 'contact', 'address'])
    .withMessage('Category must be one of: general, social, contact, address'),
  
  body('displayOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Display order must be a non-negative integer'),
  
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
];

/**
 * Validation rules for bulk update
 */
export const validateBulkUpdate = [
  body('updates')
    .isArray({ min: 1 })
    .withMessage('Updates must be a non-empty array'),
  
  body('updates.*.id')
    .isInt({ min: 1 })
    .withMessage('Each update must have a valid ID'),
  
  body('updates.*.settingValue')
    .notEmpty()
    .withMessage('Each update must have a setting value')
    .isLength({ max: 1000 })
    .withMessage('Setting value cannot exceed 1000 characters')
];
