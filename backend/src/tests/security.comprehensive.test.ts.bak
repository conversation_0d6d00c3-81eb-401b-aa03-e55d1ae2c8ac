/**
 * Comprehensive Security Testing Suite
 * Tests all Phase 4 enterprise-grade security features
 */

import request from 'supertest';
import express from 'express';
import { query, initializeDatabase, closeDatabase } from '../config/database';
import { MLAnomalyDetectionEngine } from '../utils/mlAnomalyDetection';
import { RequestSigningService } from '../middleware/requestSigning';
import crypto from 'crypto';

describe('🔒 Phase 4 Security Features - Comprehensive Testing', () => {
  let authToken: string;
  let adminId: number;
  let apiKey: { keyId: string; secretKey: string };

  beforeAll(async () => {
    await initializeDatabase();
    
    // Create test admin and get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });
    
    if (loginResponse.status === 200) {
      authToken = loginResponse.headers['set-cookie'][0];
      adminId = loginResponse.body.data.admin.id;
    }

    // Generate API key for request signing tests
    if (adminId) {
      apiKey = await RequestSigningService.generateAPIKey(
        adminId,
        'Test API Key',
        ['read:scholarships', 'write:scholarships']
      );
    }
  });

  afterAll(async () => {
    await closeDatabase();
  });

  describe('🔐 Two-Factor Authentication (2FA)', () => {
    test('should initialize 2FA setup', async () => {
      const response = await request(app)
        .post('/api/2fa/initialize')
        .set('Cookie', authToken)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.qrCode).toBeDefined();
      expect(response.body.data.secret).toBeDefined();
    });

    test('should get 2FA status', async () => {
      const response = await request(app)
        .get('/api/2fa/status')
        .set('Cookie', authToken)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('enabled');
      expect(response.body.data).toHaveProperty('backupCodesCount');
    });

    test('should reject invalid 2FA tokens', async () => {
      const response = await request(app)
        .post('/api/2fa/verify')
        .send({
          adminId: adminId,
          token: '000000'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('2FA is not enabled');
    });
  });

  describe('🛡️ Device Trust Management', () => {
    test('should list admin devices', async () => {
      const response = await request(app)
        .get('/api/devices')
        .set('Cookie', authToken)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toBeDefined();
    });

    test('should handle device trust operations', async () => {
      // First get devices
      const devicesResponse = await request(app)
        .get('/api/devices')
        .set('Cookie', authToken);

      if (devicesResponse.body.data.length > 0) {
        const deviceId = devicesResponse.body.data[0].id;

        // Test trust device
        const trustResponse = await request(app)
          .put(`/api/devices/${deviceId}/trust`)
          .set('Cookie', authToken);

        expect([200, 400]).toContain(trustResponse.status);
      }
    });

    test('should calculate device risk scores', async () => {
      const devicesResponse = await request(app)
        .get('/api/devices')
        .set('Cookie', authToken);

      if (devicesResponse.body.data.length > 0) {
        const device = devicesResponse.body.data[0];
        expect(device.riskScore).toBeDefined();
        expect(typeof device.riskScore).toBe('number');
        expect(device.riskScore).toBeGreaterThanOrEqual(0);
        expect(device.riskScore).toBeLessThanOrEqual(100);
      }
    });
  });

  describe('🧠 ML-based Anomaly Detection', () => {
    test('should analyze login attempts for anomalies', async () => {
      const loginData = {
        timestamp: new Date(),
        ipAddress: '*************',
        country: 'US',
        deviceType: 'desktop',
        browser: 'chrome',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      };

      const anomalyScore = await MLAnomalyDetectionEngine.analyzeLoginAttempt(
        adminId,
        loginData
      );

      expect(anomalyScore).toBeDefined();
      expect(anomalyScore.totalScore).toBeGreaterThanOrEqual(0);
      expect(anomalyScore.totalScore).toBeLessThanOrEqual(100);
      expect(anomalyScore.riskLevel).toMatch(/^(low|medium|high|critical)$/);
      expect(anomalyScore.confidence).toBeGreaterThan(0);
      expect(anomalyScore.confidence).toBeLessThanOrEqual(1);
    });

    test('should detect time-based anomalies', async () => {
      const timestamp = new Date();
      timestamp.setHours(3); // 3 AM - unusual hour

      const profile = await MLAnomalyDetectionEngine.getBehavioralProfile(adminId);
      const timeAnomaly = await MLAnomalyDetectionEngine.calculateTimeAnomaly(timestamp, profile);

      expect(timeAnomaly).toBeGreaterThanOrEqual(0);
      expect(timeAnomaly).toBeLessThanOrEqual(100);
    });

    test('should detect location-based anomalies', async () => {
      const profile = await MLAnomalyDetectionEngine.getBehavioralProfile(adminId);
      const locationAnomaly = await MLAnomalyDetectionEngine.calculateLocationAnomaly('CN', profile);

      expect(locationAnomaly).toBeGreaterThanOrEqual(0);
      expect(locationAnomaly).toBeLessThanOrEqual(100);
    });
  });

  describe('🔑 API Security Hardening', () => {
    test('should handle request signing verification', async () => {
      if (!apiKey) {
        console.log('Skipping request signing test - no API key available');
        return;
      }

      const timestamp = Date.now().toString();
      const nonce = crypto.randomBytes(16).toString('hex');
      const method = 'GET';
      const url = '/api/scholarships';
      const body = '';

      const signature = RequestSigningService.generateSignature(
        method,
        url,
        body,
        timestamp,
        nonce,
        apiKey.secretKey
      );

      const response = await request(app)
        .get('/api/scholarships')
        .set('X-Signature', signature)
        .set('X-Timestamp', timestamp)
        .set('X-Nonce', nonce)
        .set('X-Key-ID', apiKey.keyId);

      // Should either succeed or fail gracefully
      expect([200, 401, 404]).toContain(response.status);
    });

    test('should reject requests with invalid signatures', async () => {
      if (!apiKey) {
        console.log('Skipping invalid signature test - no API key available');
        return;
      }

      const response = await request(app)
        .get('/api/scholarships')
        .set('X-Signature', 'invalid-signature')
        .set('X-Timestamp', Date.now().toString())
        .set('X-Nonce', crypto.randomBytes(16).toString('hex'))
        .set('X-Key-ID', apiKey.keyId)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('signature verification failed');
    });

    test('should enforce adaptive rate limiting', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const requests = Array(15).fill(null).map(() =>
        request(app)
          .get('/api/health')
          .set('X-Forwarded-For', '*************')
      );

      const responses = await Promise.all(requests);
      
      // At least some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);

      // Check rate limit headers
      const lastResponse = responses[responses.length - 1];
      if (lastResponse.status === 429) {
        expect(lastResponse.headers['x-ratelimit-limit']).toBeDefined();
        expect(lastResponse.headers['x-ratelimit-remaining']).toBeDefined();
        expect(lastResponse.headers['x-ratelimit-reset']).toBeDefined();
      }
    });
  });

  describe('🛡️ Advanced CSP Policies', () => {
    test('should apply CSP headers to responses', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      // Check for CSP headers
      expect(response.headers['content-security-policy-report-only']).toBeDefined();
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['referrer-policy']).toBe('strict-origin-when-cross-origin');
      expect(response.headers['permissions-policy']).toBeDefined();
    });

    test('should generate unique nonces for each request', async () => {
      const response1 = await request(app).get('/api/health');
      const response2 = await request(app).get('/api/health');

      const csp1 = response1.headers['content-security-policy-report-only'];
      const csp2 = response2.headers['content-security-policy-report-only'];

      // Extract nonces from CSP headers
      const nonce1Match = csp1.match(/nonce-([A-Za-z0-9+/=]+)/);
      const nonce2Match = csp2.match(/nonce-([A-Za-z0-9+/=]+)/);

      if (nonce1Match && nonce2Match) {
        expect(nonce1Match[1]).not.toBe(nonce2Match[1]);
      }
    });

    test('should handle CSP violation reports', async () => {
      const violationReport = {
        'document-uri': 'http://localhost:3000/test',
        'referrer': '',
        'violated-directive': 'script-src',
        'effective-directive': 'script-src',
        'original-policy': "default-src 'self'",
        'blocked-uri': 'inline',
        'status-code': 200
      };

      const response = await request(app)
        .post('/api/security/csp-violation')
        .send(violationReport)
        .expect(204);

      expect(response.status).toBe(204);
    });
  });

  describe('📊 Security Analytics and Monitoring', () => {
    test('should provide security dashboard data', async () => {
      const response = await request(app)
        .get('/api/security/dashboard')
        .set('Cookie', authToken);

      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
      }
    });

    test('should track security events', async () => {
      const response = await request(app)
        .get('/api/security/events')
        .set('Cookie', authToken);

      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeInstanceOf(Array);
      }
    });

    test('should provide CSP violation analytics', async () => {
      const response = await request(app)
        .get('/api/security/csp-analytics')
        .set('Cookie', authToken);

      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
        expect(response.body.data.summary).toBeDefined();
      }
    });
  });

  describe('🔍 Penetration Testing Simulations', () => {
    test('should resist SQL injection attempts', async () => {
      const maliciousInputs = [
        "'; DROP TABLE admins; --",
        "1' OR '1'='1",
        "admin'; DELETE FROM scholarships; --",
        "1' UNION SELECT * FROM admins --"
      ];

      for (const input of maliciousInputs) {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: input,
            password: 'test'
          });

        // Should not cause server errors or expose data
        expect([400, 401, 422]).toContain(response.status);
        expect(response.body.success).toBe(false);
      }
    });

    test('should resist XSS attempts', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        'javascript:alert("XSS")',
        '<img src="x" onerror="alert(\'XSS\')">',
        '"><script>alert("XSS")</script>'
      ];

      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: payload,
            password: 'test'
          });

        // Should sanitize input and not execute scripts
        expect([400, 401, 422]).toContain(response.status);
        if (response.body.message) {
          expect(response.body.message).not.toContain('<script>');
          expect(response.body.message).not.toContain('javascript:');
        }
      }
    });

    test('should resist brute force attacks', async () => {
      const attempts = Array(10).fill(null).map((_, i) =>
        request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: `wrong-password-${i}`
          })
      );

      const responses = await Promise.all(attempts);
      
      // Should implement progressive delays or account lockout
      const lastResponses = responses.slice(-3);
      const hasRateLimit = lastResponses.some(r => r.status === 429);
      const hasAccountLock = lastResponses.some(r => 
        r.body.message && r.body.message.includes('locked')
      );

      expect(hasRateLimit || hasAccountLock).toBe(true);
    });

    test('should resist CSRF attacks', async () => {
      // Attempt request without proper CSRF token
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Cookie', authToken)
        .set('Origin', 'http://malicious-site.com');

      // Should either require CSRF token or validate origin
      expect([200, 403, 401]).toContain(response.status);
    });
  });
});
