-- Contact Settings Table
-- This table stores all contact information and social media links
-- that can be managed by administrators

CREATE TABLE IF NOT EXISTS contact_settings (
  id SERIAL PRIMARY KEY,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT NOT NULL,
  setting_type VARCHAR(50) NOT NULL DEFAULT 'text',
  category VARCHAR(50) NOT NULL DEFAULT 'general',
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by_admin INTEGER,
  updated_by_admin INTEGER
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_settings_key ON contact_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_contact_settings_category ON contact_settings(category);
CREATE INDEX IF NOT EXISTS idx_contact_settings_active ON contact_settings(is_active);
