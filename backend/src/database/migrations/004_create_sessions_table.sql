-- Migration: Create admin sessions table for professional session management
-- Version: 004
-- Description: Add session tracking for enhanced security and concurrent session management

-- Create admin_sessions table
CREATE TABLE IF NOT EXISTS admin_sessions (
    id VARCHAR(255) PRIMARY KEY,
    admin_id INTEGER NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
    jti VARCHAR(255) NOT NULL UNIQUE,
    ip VARCHAR(45) NOT NULL,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_id ON admin_sessions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_jti ON admin_sessions(jti);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_active ON admin_sessions(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_expires ON admin_sessions(expires_at);

-- Add comments for documentation
COMMENT ON TABLE admin_sessions IS 'Tracks active admin sessions for security and concurrent session management';
COMMENT ON COLUMN admin_sessions.id IS 'Unique session identifier';
COMMENT ON COLUMN admin_sessions.admin_id IS 'Reference to the admin user';
COMMENT ON COLUMN admin_sessions.jti IS 'JWT Token ID for token tracking';
COMMENT ON COLUMN admin_sessions.ip IS 'IP address of the session';
COMMENT ON COLUMN admin_sessions.user_agent IS 'User agent string for device identification';
COMMENT ON COLUMN admin_sessions.is_active IS 'Whether the session is currently active';
COMMENT ON COLUMN admin_sessions.created_at IS 'When the session was created';
COMMENT ON COLUMN admin_sessions.last_activity IS 'Last activity timestamp';
COMMENT ON COLUMN admin_sessions.expires_at IS 'When the session expires';
