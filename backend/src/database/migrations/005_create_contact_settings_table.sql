-- Contact Settings Table
-- This table stores all contact information and social media links
-- that can be managed by administrators

CREATE TABLE IF NOT EXISTS contact_settings (
  id SERIAL PRIMARY KEY,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT NOT NULL,
  setting_type VARCHAR(50) NOT NULL DEFAULT 'text', -- text, email, phone, url, address
  category VARCHAR(50) NOT NULL DEFAULT 'general', -- general, social, contact, address
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by_admin INTEGER,
  updated_by_admin INTEGER
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_settings_key ON contact_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_contact_settings_category ON contact_settings(category);
CREATE INDEX IF NOT EXISTS idx_contact_settings_active ON contact_settings(is_active);

-- Insert default contact settings
INSERT INTO contact_settings (setting_key, setting_value, setting_type, category, display_order, is_active) VALUES
-- Contact Information
('contact_email', '<EMAIL>', 'email', 'contact', 1, true),
('support_email', '<EMAIL>', 'email', 'contact', 2, true),
('phone_primary', '+33 1 23 45 67 89', 'phone', 'contact', 3, true),
('phone_secondary', '(+221) 33 123 4567', 'phone', 'contact', 4, true),

-- Address Information
('address_primary', '123 Avenue des Bourses, 75001 Paris, France', 'address', 'address', 1, true),
('address_secondary', 'Dakar, Sénégal\nAvenue de la République', 'address', 'address', 2, true),

-- Social Media Links
('facebook_url', 'https://facebook.com/mabourse', 'url', 'social', 1, true),
('twitter_url', 'https://twitter.com/mabourse', 'url', 'social', 2, true),
('linkedin_url', 'https://linkedin.com/company/mabourse', 'url', 'social', 3, true),
('instagram_url', 'https://instagram.com/mabourse', 'url', 'social', 4, true),
('youtube_url', 'https://youtube.com/@mabourse', 'url', 'social', 5, true),

-- Business Information
('business_name', 'MaBourse', 'text', 'general', 1, true),
('business_description', 'Votre plateforme de référence pour trouver des bourses d\'études adaptées à votre parcours académique et à vos ambitions professionnelles.', 'text', 'general', 2, true),
('business_hours', 'Lundi - Vendredi: 9h00 - 18h00', 'text', 'general', 3, true),

-- Additional Contact Methods
('whatsapp_number', '+33 1 23 45 67 89', 'phone', 'contact', 5, true),
('telegram_url', 'https://t.me/mabourse', 'url', 'social', 6, true)

ON CONFLICT (setting_key) DO NOTHING;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_contact_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_contact_settings_updated_at
    BEFORE UPDATE ON contact_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_contact_settings_updated_at();
