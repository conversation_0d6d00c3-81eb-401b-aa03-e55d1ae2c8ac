/**
 * Database Migration Script
 * 
 * This script handles database schema creation and initial data setup
 */

import fs from 'fs';
import path from 'path';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import { initializeDatabase, query, closeDatabase } from '../config/database';
import { getEnv } from '../utils/envValidator';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env.development') });

/**
 * Run database migrations
 */
export async function runMigrations(): Promise<void> {
  console.log('Starting database migrations...');
  console.log('Database config:', {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    user: process.env.DB_USER
  });

  try {
    // Initialize database connection
    initializeDatabase();
    
    // Read and execute schema
    await createSchema();
    
    // Create initial data
    await createInitialData();
    
    console.log('Database migrations completed successfully');
  } catch (error) {
    console.error('Database migration failed:', error);
    throw error;
  }
}

/**
 * Create database schema
 */
async function createSchema(): Promise<void> {
  console.log('Creating database schema...');
  
  const schemaPath = path.join(__dirname, 'schema.sql');
  const schema = fs.readFileSync(schemaPath, 'utf8');
  
  // Execute the entire schema as one statement
  try {
    await query(schema);
  } catch (error) {
    console.error('Error executing schema:', error);
    throw error;
  }
  
  console.log('Database schema created successfully');
}

/**
 * Create initial data (admin user, security settings, etc.)
 */
async function createInitialData(): Promise<void> {
  console.log('Creating initial data...');
  
  // Create main admin if it doesn't exist
  await createMainAdmin();
  
  // Create default security settings
  await createSecuritySettings();
  
  console.log('Initial data created successfully');
}

/**
 * Create main admin user
 */
async function createMainAdmin(): Promise<void> {
  const adminEmail = getEnv('ADMIN_EMAIL', '<EMAIL>');
  const adminPassword = getEnv('ADMIN_PASSWORD', 'admin123');
  const adminName = getEnv('ADMIN_NAME', 'Main Administrator');
  
  // Check if main admin already exists
  const existingAdmin = await query(
    'SELECT id FROM admins WHERE is_main_admin = TRUE LIMIT 1'
  );
  
  if (existingAdmin.rows.length > 0) {
    console.log('Main admin already exists, skipping creation');
    return;
  }
  
  // Hash password
  const hashedPassword = await bcrypt.hash(adminPassword, 10);
  
  // Create main admin
  await query(`
    INSERT INTO admins (
      name, email, password, role, privileges, is_main_admin, two_factor_enabled
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
  `, [
    adminName,
    adminEmail,
    hashedPassword,
    'super_admin',
    JSON.stringify(['all']),
    true,
    false
  ]);
  
  console.log(`Main admin created: ${adminEmail}`);
}

/**
 * Create default security settings
 */
async function createSecuritySettings(): Promise<void> {
  // Check if security settings already exist
  const existingSettings = await query(
    'SELECT id FROM security_settings WHERE id = 1'
  );
  
  if (existingSettings.rows.length > 0) {
    console.log('Security settings already exist, skipping creation');
    return;
  }
  
  // Create default security settings
  await query(`
    INSERT INTO security_settings (
      id, max_login_attempts, lockout_duration, password_expiry_days,
      require_strong_passwords, two_factor_enabled, min_password_length
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
  `, [1, 5, 30, 90, true, false, 8]);
  
  console.log('Default security settings created');
}

/**
 * Drop all tables (for development/testing)
 */
export async function dropAllTables(): Promise<void> {
  console.log('Dropping all tables...');
  
  const tables = [
    'password_history',
    'security_events',
    'newsletter_subscriptions',
    'messages',
    'scholarships',
    'admins',
    'users',
    'security_settings'
  ];
  
  for (const table of tables) {
    try {
      await query(`DROP TABLE IF EXISTS ${table} CASCADE`);
      console.log(`Dropped table: ${table}`);
    } catch (error) {
      console.error(`Error dropping table ${table}:`, error);
    }
  }
  
  // Drop triggers and functions
  try {
    await query('DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE');
    console.log('Dropped update_updated_at_column function');
  } catch (error) {
    console.error('Error dropping function:', error);
  }
  
  console.log('All tables dropped');
}

/**
 * Reset database (drop and recreate)
 */
export async function resetDatabase(): Promise<void> {
  console.log('Resetting database...');
  
  await dropAllTables();
  await runMigrations();
  
  console.log('Database reset completed');
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  async function main() {
    try {
      switch (command) {
        case 'migrate':
          await runMigrations();
          break;
        case 'reset':
          await resetDatabase();
          break;
        case 'drop':
          await dropAllTables();
          break;
        default:
          console.log('Usage: ts-node migrate.ts [migrate|reset|drop]');
          process.exit(1);
      }
    } catch (error) {
      console.error('Migration script failed:', error);
      process.exit(1);
    } finally {
      await closeDatabase();
    }
  }
  
  main();
}
