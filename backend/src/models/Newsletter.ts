/**
 * Newsletter Model
 * 
 * This model handles all database operations for newsletter subscriptions
 */

import { query } from '../config/database';

export interface NewsletterData {
  id?: number;
  email: string;
  createdAt?: Date;
}

export class Newsletter {
  /**
   * Create a new newsletter subscription
   */
  static async create(email: string): Promise<NewsletterData> {
    const result = await query(`
      INSERT INTO newsletter_subscriptions (email)
      VALUES ($1)
      ON CONFLICT (email) DO NOTHING
      RETURNING *
    `, [email]);
    
    // If no rows returned, the email already exists
    if (result.rows.length === 0) {
      const existing = await this.findByEmail(email);
      if (existing) {
        return existing;
      }
      throw new Error('Failed to create newsletter subscription');
    }
    
    return this.mapRowToNewsletter(result.rows[0]);
  }

  /**
   * Find newsletter subscription by email
   */
  static async findByEmail(email: string): Promise<NewsletterData | null> {
    const result = await query('SELECT * FROM newsletter_subscriptions WHERE email = $1', [email]);
    return result.rows.length > 0 ? this.mapRowToNewsletter(result.rows[0]) : null;
  }

  /**
   * Get all newsletter subscriptions
   */
  static async findAll(options?: {
    limit?: number;
    offset?: number;
    orderBy?: 'created_at' | 'email';
    orderDirection?: 'ASC' | 'DESC';
  }): Promise<NewsletterData[]> {
    let queryText = 'SELECT * FROM newsletter_subscriptions';
    const params: any[] = [];
    let paramCount = 1;

    // Add ordering
    const orderBy = options?.orderBy || 'created_at';
    const orderDirection = options?.orderDirection || 'DESC';
    queryText += ` ORDER BY ${orderBy} ${orderDirection}`;

    // Add pagination
    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;

      if (options?.offset) {
        queryText += ` OFFSET $${paramCount}`;
        params.push(options.offset);
        paramCount++;
      }
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToNewsletter(row));
  }

  /**
   * Search newsletter subscriptions by email
   */
  static async search(searchTerm: string, options?: {
    limit?: number;
    offset?: number;
  }): Promise<NewsletterData[]> {
    let queryText = 'SELECT * FROM newsletter_subscriptions WHERE email ILIKE $1';
    const params: any[] = [`%${searchTerm}%`];
    let paramCount = 2;

    queryText += ' ORDER BY created_at DESC';

    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;

      if (options?.offset) {
        queryText += ` OFFSET $${paramCount}`;
        params.push(options.offset);
        paramCount++;
      }
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToNewsletter(row));
  }

  /**
   * Delete newsletter subscription
   */
  static async delete(id: number): Promise<boolean> {
    const result = await query('DELETE FROM newsletter_subscriptions WHERE id = $1', [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Delete newsletter subscription by email
   */
  static async deleteByEmail(email: string): Promise<boolean> {
    const result = await query('DELETE FROM newsletter_subscriptions WHERE email = $1', [email]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Count total newsletter subscriptions
   */
  static async count(): Promise<number> {
    const result = await query('SELECT COUNT(*) as count FROM newsletter_subscriptions');
    return parseInt(result.rows[0].count);
  }

  /**
   * Check if email is subscribed
   */
  static async isSubscribed(email: string): Promise<boolean> {
    const result = await query('SELECT 1 FROM newsletter_subscriptions WHERE email = $1', [email]);
    return result.rows.length > 0;
  }

  /**
   * Get recent subscriptions
   */
  static async findRecent(limit: number = 10): Promise<NewsletterData[]> {
    const result = await query(`
      SELECT * FROM newsletter_subscriptions 
      ORDER BY created_at DESC
      LIMIT $1
    `, [limit]);

    return result.rows.map(row => this.mapRowToNewsletter(row));
  }

  /**
   * Get subscriptions by date range
   */
  static async findByDateRange(startDate: Date, endDate: Date): Promise<NewsletterData[]> {
    const result = await query(`
      SELECT * FROM newsletter_subscriptions 
      WHERE created_at >= $1 AND created_at <= $2
      ORDER BY created_at DESC
    `, [startDate, endDate]);

    return result.rows.map(row => this.mapRowToNewsletter(row));
  }

  /**
   * Get all emails for newsletter sending
   */
  static async getAllEmails(): Promise<string[]> {
    const result = await query('SELECT email FROM newsletter_subscriptions ORDER BY created_at DESC');
    return result.rows.map(row => row.email);
  }

  /**
   * Bulk create newsletter subscriptions
   */
  static async bulkCreate(emails: string[]): Promise<{ created: number; existing: number }> {
    let created = 0;
    let existing = 0;

    for (const email of emails) {
      try {
        const result = await query(`
          INSERT INTO newsletter_subscriptions (email)
          VALUES ($1)
          ON CONFLICT (email) DO NOTHING
          RETURNING id
        `, [email]);

        if (result.rows.length > 0) {
          created++;
        } else {
          existing++;
        }
      } catch (error) {
        console.error(`Error creating newsletter subscription for ${email}:`, error);
        existing++;
      }
    }

    return { created, existing };
  }

  /**
   * Export all subscriptions to CSV format
   */
  static async exportToCSV(): Promise<string> {
    const subscriptions = await this.findAll();
    
    let csv = 'Email,Subscription Date\n';
    subscriptions.forEach(subscription => {
      const date = subscription.createdAt ? subscription.createdAt.toISOString().split('T')[0] : '';
      csv += `"${subscription.email}","${date}"\n`;
    });
    
    return csv;
  }

  /**
   * Map database row to NewsletterData
   */
  private static mapRowToNewsletter(row: any): NewsletterData {
    return {
      id: row.id,
      email: row.email,
      createdAt: row.created_at,
    };
  }
}
