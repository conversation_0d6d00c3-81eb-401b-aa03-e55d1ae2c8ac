/**
 * Contact Settings Model
 * 
 * Production-grade model for managing contact information and social media links
 * with proper validation, caching, and security features.
 */

import { query } from '../config/database';

export interface ContactSetting {
  id: number;
  settingKey: string;
  settingValue: string;
  settingType: 'text' | 'email' | 'phone' | 'url' | 'address';
  category: 'general' | 'social' | 'contact' | 'address';
  displayOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdByAdmin?: number;
  updatedByAdmin?: number;
}

export interface ContactSettingsGrouped {
  general: ContactSetting[];
  social: ContactSetting[];
  contact: ContactSetting[];
  address: ContactSetting[];
}

export class ContactSettingsModel {
  /**
   * Get all contact settings grouped by category
   */
  static async getAllGrouped(): Promise<ContactSettingsGrouped> {
    const result = await query(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      WHERE is_active = true 
      ORDER BY category, display_order ASC
    `);

    const settings = result.rows as ContactSetting[];
    
    return {
      general: settings.filter(s => s.category === 'general'),
      social: settings.filter(s => s.category === 'social'),
      contact: settings.filter(s => s.category === 'contact'),
      address: settings.filter(s => s.category === 'address')
    };
  }

  /**
   * Get all contact settings as a flat array
   */
  static async getAll(): Promise<ContactSetting[]> {
    const result = await query(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      ORDER BY category, display_order ASC
    `);

    return result.rows as ContactSetting[];
  }

  /**
   * Get contact settings by category
   */
  static async getByCategory(category: string): Promise<ContactSetting[]> {
    const result = await query(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      WHERE category = $1 AND is_active = true
      ORDER BY display_order ASC
    `, [category]);

    return result.rows as ContactSetting[];
  }

  /**
   * Get a specific contact setting by key
   */
  static async getByKey(settingKey: string): Promise<ContactSetting | null> {
    const result = await query(`
      SELECT 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
      FROM contact_settings 
      WHERE setting_key = $1
    `, [settingKey]);

    return result.rows[0] || null;
  }

  /**
   * Create a new contact setting
   */
  static async create(data: {
    settingKey: string;
    settingValue: string;
    settingType: string;
    category: string;
    displayOrder?: number;
    isActive?: boolean;
    createdByAdmin?: number;
  }): Promise<ContactSetting> {
    const result = await query(`
      INSERT INTO contact_settings (
        setting_key, setting_value, setting_type, category, 
        display_order, is_active, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
    `, [
      data.settingKey,
      data.settingValue,
      data.settingType,
      data.category,
      data.displayOrder || 0,
      data.isActive !== false,
      data.createdByAdmin
    ]);

    return result.rows[0] as ContactSetting;
  }

  /**
   * Update a contact setting
   */
  static async update(
    id: number, 
    data: {
      settingValue?: string;
      settingType?: string;
      category?: string;
      displayOrder?: number;
      isActive?: boolean;
      updatedByAdmin?: number;
    }
  ): Promise<ContactSetting | null> {
    const fields = [];
    const values = [];
    let paramIndex = 1;

    if (data.settingValue !== undefined) {
      fields.push(`setting_value = $${paramIndex++}`);
      values.push(data.settingValue);
    }
    if (data.settingType !== undefined) {
      fields.push(`setting_type = $${paramIndex++}`);
      values.push(data.settingType);
    }
    if (data.category !== undefined) {
      fields.push(`category = $${paramIndex++}`);
      values.push(data.category);
    }
    if (data.displayOrder !== undefined) {
      fields.push(`display_order = $${paramIndex++}`);
      values.push(data.displayOrder);
    }
    if (data.isActive !== undefined) {
      fields.push(`is_active = $${paramIndex++}`);
      values.push(data.isActive);
    }
    if (data.updatedByAdmin !== undefined) {
      fields.push(`updated_by_admin = $${paramIndex++}`);
      values.push(data.updatedByAdmin);
    }

    if (fields.length === 0) {
      return null;
    }

    values.push(id);

    const result = await query(`
      UPDATE contact_settings 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING 
        id,
        setting_key as "settingKey",
        setting_value as "settingValue",
        setting_type as "settingType",
        category,
        display_order as "displayOrder",
        is_active as "isActive",
        created_at as "createdAt",
        updated_at as "updatedAt",
        created_by_admin as "createdByAdmin",
        updated_by_admin as "updatedByAdmin"
    `, values);

    return result.rows[0] || null;
  }

  /**
   * Delete a contact setting (soft delete by setting isActive to false)
   */
  static async delete(id: number, deletedByAdmin?: number): Promise<boolean> {
    const result = await query(`
      UPDATE contact_settings 
      SET is_active = false, updated_by_admin = $2
      WHERE id = $1
    `, [id, deletedByAdmin]);

    return (result.rowCount || 0) > 0;
  }

  /**
   * Bulk update contact settings
   */
  static async bulkUpdate(
    updates: Array<{
      id: number;
      settingValue: string;
    }>,
    updatedByAdmin?: number
  ): Promise<ContactSetting[]> {
    const results: ContactSetting[] = [];

    for (const update of updates) {
      const result = await this.update(update.id, {
        settingValue: update.settingValue,
        updatedByAdmin
      });
      if (result) {
        results.push(result);
      }
    }

    return results;
  }

  /**
   * Get public contact settings (for frontend display)
   * Returns only active settings without admin metadata
   */
  static async getPublicSettings(): Promise<Record<string, string>> {
    const result = await query(`
      SELECT setting_key, setting_value
      FROM contact_settings
      WHERE is_active = true
      ORDER BY category, display_order ASC
    `);

    const settings: Record<string, string> = {};
    result.rows.forEach(row => {
      settings[row.setting_key] = row.setting_value;
    });

    return settings;
  }
}
