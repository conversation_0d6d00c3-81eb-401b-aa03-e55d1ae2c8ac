/**
 * User Model
 * 
 * This model handles all database operations for regular users
 */

import bcrypt from 'bcryptjs';
import { query } from '../config/database';

export interface UserData {
  id?: number;
  name: string;
  email: string;
  password?: string;
  role: string;
  failedLoginAttempts: number;
  lockUntil?: Date;
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  lastLogin?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  passwordUpdatedAt?: Date;
  passwordExpiresAt?: Date;
  mustChangePassword: boolean;
}

export class User {
  /**
   * Create a new user
   */
  static async create(userData: Omit<UserData, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserData> {
    const hashedPassword = userData.password ? await bcrypt.hash(userData.password, 10) : null;
    
    const result = await query(`
      INSERT INTO users (
        name, email, password, role, failed_login_attempts, must_change_password
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      userData.name,
      userData.email,
      hashedPassword,
      userData.role || 'user',
      userData.failedLoginAttempts || 0,
      userData.mustChangePassword || false
    ]);
    
    return this.mapRowToUser(result.rows[0]);
  }

  /**
   * Find user by ID
   */
  static async findById(id: number): Promise<UserData | null> {
    const result = await query('SELECT * FROM users WHERE id = $1', [id]);
    return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<UserData | null> {
    const result = await query('SELECT * FROM users WHERE email = $1', [email]);
    return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
  }

  /**
   * Get all users
   */
  static async findAll(limit?: number, offset?: number): Promise<UserData[]> {
    let queryText = 'SELECT * FROM users ORDER BY created_at DESC';
    const params: any[] = [];
    
    if (limit) {
      queryText += ' LIMIT $1';
      params.push(limit);
      
      if (offset) {
        queryText += ' OFFSET $2';
        params.push(offset);
      }
    }
    
    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToUser(row));
  }

  /**
   * Update user
   */
  static async update(id: number, updates: Partial<UserData>): Promise<UserData | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
        const dbKey = this.camelToSnake(key);
        
        if (key === 'password' && value) {
          // Hash password if provided
          fields.push(`${dbKey} = $${paramCount}`);
          values.push(bcrypt.hashSync(value as string, 10));
          // Update password timestamp
          fields.push(`password_updated_at = CURRENT_TIMESTAMP`);
        } else {
          fields.push(`${dbKey} = $${paramCount}`);
          values.push(value);
        }
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    const result = await query(`
      UPDATE users 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);

    return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
  }

  /**
   * Delete user
   */
  static async delete(id: number): Promise<boolean> {
    const result = await query('DELETE FROM users WHERE id = $1', [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Verify password
   */
  static async verifyPassword(user: UserData, password: string): Promise<boolean> {
    if (!user.password) return false;
    return bcrypt.compare(password, user.password);
  }

  /**
   * Update failed login attempts
   */
  static async updateFailedLoginAttempts(id: number, attempts: number, lockUntil?: Date): Promise<void> {
    await query(`
      UPDATE users 
      SET failed_login_attempts = $1, lock_until = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [attempts, lockUntil, id]);
  }

  /**
   * Update last login
   */
  static async updateLastLogin(id: number): Promise<void> {
    await query(`
      UPDATE users 
      SET last_login = CURRENT_TIMESTAMP, failed_login_attempts = 0, lock_until = NULL
      WHERE id = $1
    `, [id]);
  }

  /**
   * Count total users
   */
  static async count(): Promise<number> {
    const result = await query('SELECT COUNT(*) as count FROM users');
    return parseInt(result.rows[0].count);
  }

  /**
   * Search users by name or email
   */
  static async search(searchTerm: string, limit?: number): Promise<UserData[]> {
    const result = await query(`
      SELECT * FROM users 
      WHERE name ILIKE $1 OR email ILIKE $1
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [`%${searchTerm}%`, limit] : [`%${searchTerm}%`]);
    
    return result.rows.map(row => this.mapRowToUser(row));
  }

  /**
   * Map database row to UserData
   */
  private static mapRowToUser(row: any): UserData {
    return {
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      failedLoginAttempts: row.failed_login_attempts,
      lockUntil: row.lock_until,
      resetPasswordToken: row.reset_password_token,
      resetPasswordExpires: row.reset_password_expires,
      lastLogin: row.last_login,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      passwordUpdatedAt: row.password_updated_at,
      passwordExpiresAt: row.password_expires_at,
      mustChangePassword: row.must_change_password,
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
