/**
 * Scholarship Model
 *
 * This model handles all database operations for scholarships
 */

import { query } from '../config/database';

export interface ScholarshipData {
  id?: number;
  title: string;
  description: string;
  level?: string;
  country?: string;
  deadline: Date;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financialBenefitsSummary?: string;
  eligibilitySummary?: string;
  scholarshipLink?: string;
  youtubeLink?: string;
  createdBy?: number | null;
  createdByAdmin?: number | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Scholarship {
  /**
   * Create a new scholarship
   */
  static async create(scholarshipData: Omit<ScholarshipData, 'id' | 'createdAt' | 'updatedAt'>): Promise<ScholarshipData> {
    const result = await query(`
      INSERT INTO scholarships (
        title, description, level, country, deadline, is_open, thumbnail,
        coverage, financial_benefits_summary, eligibility_summary,
        scholarship_link, youtube_link, created_by, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *
    `, [
      scholarshipData.title,
      scholarshipData.description,
      scholarshipData.level,
      scholarshipData.country,
      scholarshipData.deadline,
      scholarshipData.isOpen !== undefined ? scholarshipData.isOpen : true,
      scholarshipData.thumbnail,
      scholarshipData.coverage,
      scholarshipData.financialBenefitsSummary,
      scholarshipData.eligibilitySummary,
      scholarshipData.scholarshipLink,
      scholarshipData.youtubeLink,
      scholarshipData.createdBy,
      scholarshipData.createdByAdmin
    ]);
    
    return this.mapRowToScholarship(result.rows[0]);
  }

  /**
   * Find scholarship by ID
   */
  static async findById(id: number): Promise<ScholarshipData | null> {
    const result = await query('SELECT * FROM scholarships WHERE id = $1', [id]);
    return result.rows.length > 0 ? this.mapRowToScholarship(result.rows[0]) : null;
  }

  /**
   * Get all scholarships
   */
  static async findAll(options?: {
    limit?: number;
    offset?: number;
    isOpen?: boolean;
    level?: string;
    country?: string;
    orderBy?: 'created_at' | 'deadline' | 'title';
    orderDirection?: 'ASC' | 'DESC';
  }): Promise<ScholarshipData[]> {
    let queryText = 'SELECT * FROM scholarships WHERE 1=1';
    const params: any[] = [];
    let paramCount = 1;

    // Add filters
    if (options?.isOpen !== undefined) {
      queryText += ` AND is_open = $${paramCount}`;
      params.push(options.isOpen);
      paramCount++;
    }

    if (options?.level) {
      queryText += ` AND level = $${paramCount}`;
      params.push(options.level);
      paramCount++;
    }

    if (options?.country) {
      queryText += ` AND country = $${paramCount}`;
      params.push(options.country);
      paramCount++;
    }

    // Add ordering
    const orderBy = options?.orderBy || 'created_at';
    const orderDirection = options?.orderDirection || 'DESC';
    queryText += ` ORDER BY ${orderBy} ${orderDirection}`;

    // Add pagination
    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;

      if (options?.offset) {
        queryText += ` OFFSET $${paramCount}`;
        params.push(options.offset);
        paramCount++;
      }
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToScholarship(row));
  }

  /**
   * Search scholarships
   */
  static async search(searchTerm: string, options?: {
    limit?: number;
    offset?: number;
    isOpen?: boolean;
  }): Promise<ScholarshipData[]> {
    let queryText = `
      SELECT * FROM scholarships 
      WHERE (title ILIKE $1 OR description ILIKE $1 OR country ILIKE $1 OR level ILIKE $1)
    `;
    const params: any[] = [`%${searchTerm}%`];
    let paramCount = 2;

    if (options?.isOpen !== undefined) {
      queryText += ` AND is_open = $${paramCount}`;
      params.push(options.isOpen);
      paramCount++;
    }

    queryText += ' ORDER BY created_at DESC';

    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;

      if (options?.offset) {
        queryText += ` OFFSET $${paramCount}`;
        params.push(options.offset);
        paramCount++;
      }
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToScholarship(row));
  }

  /**
   * Update scholarship
   */
  static async update(id: number, updates: Partial<ScholarshipData>): Promise<ScholarshipData | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
        const dbKey = this.camelToSnake(key);
        fields.push(`${dbKey} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    const result = await query(`
      UPDATE scholarships 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);

    return result.rows.length > 0 ? this.mapRowToScholarship(result.rows[0]) : null;
  }

  /**
   * Delete scholarship
   */
  static async delete(id: number): Promise<boolean> {
    const result = await query('DELETE FROM scholarships WHERE id = $1', [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Count total scholarships
   */
  static async count(filters?: { isOpen?: boolean; level?: string; country?: string }): Promise<number> {
    let queryText = 'SELECT COUNT(*) as count FROM scholarships WHERE 1=1';
    const params: any[] = [];
    let paramCount = 1;

    if (filters?.isOpen !== undefined) {
      queryText += ` AND is_open = $${paramCount}`;
      params.push(filters.isOpen);
      paramCount++;
    }

    if (filters?.level) {
      queryText += ` AND level = $${paramCount}`;
      params.push(filters.level);
      paramCount++;
    }

    if (filters?.country) {
      queryText += ` AND country = $${paramCount}`;
      params.push(filters.country);
      paramCount++;
    }

    const result = await query(queryText, params);
    return parseInt(result.rows[0].count);
  }

  /**
   * Get scholarships by deadline (upcoming)
   */
  static async findUpcoming(limit?: number): Promise<ScholarshipData[]> {
    const result = await query(`
      SELECT * FROM scholarships 
      WHERE deadline > CURRENT_TIMESTAMP AND is_open = TRUE
      ORDER BY deadline ASC
      ${limit ? 'LIMIT $1' : ''}
    `, limit ? [limit] : []);

    return result.rows.map(row => this.mapRowToScholarship(row));
  }

  /**
   * Get scholarships by level
   */
  static async findByLevel(level: string, limit?: number): Promise<ScholarshipData[]> {
    const result = await query(`
      SELECT * FROM scholarships 
      WHERE level = $1 AND is_open = TRUE
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [level, limit] : [level]);

    return result.rows.map(row => this.mapRowToScholarship(row));
  }

  /**
   * Get scholarships by country
   */
  static async findByCountry(country: string, limit?: number): Promise<ScholarshipData[]> {
    const result = await query(`
      SELECT * FROM scholarships 
      WHERE country = $1 AND is_open = TRUE
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [country, limit] : [country]);

    return result.rows.map(row => this.mapRowToScholarship(row));
  }

  /**
   * Get countries with scholarship counts
   */
  static async getCountriesWithCounts(): Promise<Array<{country: string, count: number}>> {
    const result = await query(`
      SELECT country, COUNT(*) as count
      FROM scholarships
      WHERE country IS NOT NULL AND country != ''
      GROUP BY country
      ORDER BY count DESC, country ASC
    `);

    return result.rows.map(row => ({
      country: row.country,
      count: parseInt(row.count)
    }));
  }

  /**
   * Search countries by name
   */
  static async searchCountries(searchTerm: string): Promise<Array<{country: string, count: number}>> {
    const result = await query(`
      SELECT country, COUNT(*) as count
      FROM scholarships
      WHERE country IS NOT NULL AND country != '' AND country ILIKE $1
      GROUP BY country
      ORDER BY count DESC, country ASC
    `, [`%${searchTerm}%`]);

    return result.rows.map(row => ({
      country: row.country,
      count: parseInt(row.count)
    }));
  }

  /**
   * Map database row to ScholarshipData
   */
  private static mapRowToScholarship(row: any): ScholarshipData {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      level: row.level,
      country: row.country,
      deadline: row.deadline,
      isOpen: row.is_open,
      thumbnail: row.thumbnail,
      coverage: row.coverage,
      financialBenefitsSummary: row.financial_benefits_summary,
      eligibilitySummary: row.eligibility_summary,
      scholarshipLink: row.scholarship_link,
      youtubeLink: row.youtube_link,
      createdBy: row.created_by,
      createdByAdmin: row.created_by_admin,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
