import { Request } from 'express';
import { User } from '../models/user.model';
import { Admin } from '../models/Admin';

export interface UserPayload {
  id: number;
  email: string;
  role: string;
  isMainAdmin: boolean;
}

export interface JwtPayload extends UserPayload {
  iat?: number;
  exp?: number;
}

declare module 'express' {
  interface Request {
    user?: UserPayload;
  }
}

declare global {
  namespace Express {
    interface Request {
      user?: UserPayload;
    }
  }
} 