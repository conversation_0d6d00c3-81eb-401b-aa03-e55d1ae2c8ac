-- Migration: Create guides and opportunities tables
-- Date: 2025-01-12
-- Description: Add support for guides and opportunities sections

-- Create guides table
CREATE TABLE IF NOT EXISTS guides (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('application', 'documents', 'preparation', 'tips')),
    slug VARCHAR(255) NOT NULL UNIQUE,
    excerpt TEXT,
    thumbnail VARCHAR(500),
    is_published BOOLEAN DEFAULT true,
    read_time INTEGER, -- in minutes
    tags JSONB,
    created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_by_admin INTEGER REFERENCES admins(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create opportunities table
CREATE TABLE IF NOT EXISTS opportunities (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('internship', 'training', 'conference', 'workshop', 'competition')),
    organization VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    is_remote BOOLEAN DEFAULT false,
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    application_link VARCHAR(500),
    requirements TEXT,
    benefits TEXT,
    thumbnail VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    tags JSONB,
    contact_email VARCHAR(255),
    website VARCHAR(500),
    created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_by_admin INTEGER REFERENCES admins(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_guides_category ON guides(category);
CREATE INDEX IF NOT EXISTS idx_guides_is_published ON guides(is_published);
CREATE INDEX IF NOT EXISTS idx_guides_created_at ON guides(created_at);
CREATE INDEX IF NOT EXISTS idx_guides_slug ON guides(slug);

CREATE INDEX IF NOT EXISTS idx_opportunities_type ON opportunities(type);
CREATE INDEX IF NOT EXISTS idx_opportunities_is_active ON opportunities(is_active);
CREATE INDEX IF NOT EXISTS idx_opportunities_deadline ON opportunities(deadline);
CREATE INDEX IF NOT EXISTS idx_opportunities_location ON opportunities(location);
CREATE INDEX IF NOT EXISTS idx_opportunities_is_remote ON opportunities(is_remote);
CREATE INDEX IF NOT EXISTS idx_opportunities_created_at ON opportunities(created_at);

-- Create full-text search indexes
CREATE INDEX IF NOT EXISTS idx_guides_search ON guides USING gin(to_tsvector('english', title || ' ' || content || ' ' || COALESCE(excerpt, '')));
CREATE INDEX IF NOT EXISTS idx_opportunities_search ON opportunities USING gin(to_tsvector('english', title || ' ' || description || ' ' || organization));

-- Insert sample guides data
INSERT INTO guides (title, content, category, slug, excerpt, is_published, read_time, tags) VALUES
('Comment rédiger un CV efficace', 
'Un CV efficace est la clé pour décrocher une bourse d''études. Voici les éléments essentiels à inclure...

## Structure recommandée

1. **Informations personnelles**
   - Nom complet
   - Adresse email professionnelle
   - Numéro de téléphone
   - Adresse (ville, pays)

2. **Profil professionnel**
   - Résumé en 2-3 lignes de votre parcours
   - Objectifs académiques et professionnels

3. **Formation académique**
   - Diplômes obtenus (du plus récent au plus ancien)
   - Mentions et distinctions
   - Projets de recherche

4. **Expérience professionnelle**
   - Stages et emplois
   - Responsabilités et réalisations
   - Compétences développées

5. **Compétences**
   - Langues parlées et niveau
   - Compétences techniques
   - Logiciels maîtrisés

6. **Activités extrascolaires**
   - Bénévolat
   - Associations
   - Projets personnels

## Conseils pratiques

- Limitez votre CV à 1-2 pages maximum
- Utilisez une police lisible (Arial, Calibri)
- Adaptez votre CV à chaque candidature
- Vérifiez l''orthographe et la grammaire
- Utilisez des verbes d''action
- Quantifiez vos réalisations quand possible

## Erreurs à éviter

- Photo non professionnelle
- Informations personnelles inutiles
- Mensonges ou exagérations
- Fautes d''orthographe
- Mise en page désordonnée
- CV trop long ou trop court',
'documents', 
'comment-rediger-cv-efficace',
'Découvrez comment créer un CV qui vous démarquera dans vos candidatures de bourses d''études.',
true,
8,
'["CV", "candidature", "documents", "conseils"]'
),

('Préparer sa lettre de motivation', 
'La lettre de motivation est un élément crucial de votre dossier de candidature...

## Structure de la lettre

1. **En-tête**
   - Vos coordonnées
   - Coordonnées du destinataire
   - Date et lieu

2. **Objet**
   - Précisez clairement l''objet de votre candidature

3. **Introduction**
   - Présentez-vous brièvement
   - Mentionnez la bourse visée

4. **Développement**
   - Expliquez vos motivations
   - Mettez en avant vos atouts
   - Montrez votre connaissance du programme

5. **Conclusion**
   - Réaffirmez votre intérêt
   - Proposez un entretien
   - Formule de politesse

## Conseils de rédaction

- Personnalisez chaque lettre
- Soyez authentique et sincère
- Utilisez un ton professionnel
- Évitez les répétitions avec le CV
- Relisez attentivement

## Exemples de phrases d''accroche

- "Passionné(e) par [domaine], je souhaite approfondir mes connaissances..."
- "Fort(e) de mon expérience en [domaine], je candidate pour..."
- "Votre programme correspond parfaitement à mes aspirations..."',
'application',
'preparer-lettre-motivation',
'Apprenez à rédiger une lettre de motivation convaincante pour vos candidatures de bourses.',
true,
6,
'["lettre de motivation", "candidature", "rédaction", "conseils"]'
),

('Documents requis pour les bourses', 
'Chaque candidature de bourse nécessite un ensemble de documents spécifiques...

## Documents de base

1. **Pièce d''identité**
   - Passeport ou carte d''identité
   - Copie certifiée conforme

2. **Diplômes et relevés de notes**
   - Tous les diplômes obtenus
   - Relevés de notes détaillés
   - Traductions officielles si nécessaire

3. **CV académique**
   - Format adapté au pays de destination
   - Mise à jour récente

4. **Lettre de motivation**
   - Personnalisée pour chaque bourse
   - Respectant les consignes

## Documents complémentaires

1. **Lettres de recommandation**
   - 2-3 lettres de professeurs ou employeurs
   - Format et contenu spécifiques

2. **Certificats de langue**
   - TOEFL, IELTS pour l''anglais
   - DELF, DALF pour le français
   - Autres selon la destination

3. **Projet d''études**
   - Description détaillée de votre projet
   - Objectifs et méthodologie

4. **Justificatifs financiers**
   - Relevés bancaires
   - Attestations de revenus

## Conseils pratiques

- Commencez la préparation tôt
- Vérifiez les exigences spécifiques
- Gardez des copies de tout
- Respectez les délais
- Faites traduire si nécessaire',
'documents',
'documents-requis-bourses',
'Guide complet des documents nécessaires pour constituer un dossier de candidature solide.',
true,
10,
'["documents", "candidature", "préparation", "guide"]'
);

-- Insert sample opportunities data
INSERT INTO opportunities (title, description, type, organization, location, is_remote, deadline, start_date, application_link, is_active, tags) VALUES
('Stage en Recherche - Institut Pasteur',
'L''Institut Pasteur propose des stages de recherche de 3 à 6 mois dans ses laboratoires. Une excellente opportunité pour les étudiants en biologie, médecine et sciences connexes.

**Profil recherché :**
- Étudiant en Master 2 ou Doctorat
- Domaines : biologie, médecine, biochimie
- Niveau d''anglais requis

**Ce que nous offrons :**
- Encadrement par des chercheurs expérimentés
- Accès aux équipements de pointe
- Indemnité de stage
- Possibilité de publication

**Comment postuler :**
Envoyez votre CV, lettre de motivation et relevés de notes.',
'internship',
'Institut Pasteur',
'Paris, France',
false,
'2025-03-15 23:59:59',
'2025-06-01 09:00:00',
'https://www.pasteur.fr/stages',
true,
'["recherche", "biologie", "médecine", "Paris"]'
),

('Formation en Data Science - Google',
'Programme intensif de formation en Data Science proposé par Google. Formation gratuite de 6 mois avec certification reconnue.

**Programme :**
- Machine Learning
- Analyse de données
- Visualisation
- Python et R
- Projets pratiques

**Prérequis :**
- Bases en mathématiques
- Motivation pour l''apprentissage
- Disponibilité 20h/semaine

**Avantages :**
- Formation gratuite
- Certification Google
- Accompagnement personnalisé
- Réseau professionnel',
'training',
'Google',
'En ligne',
true,
'2025-02-28 23:59:59',
'2025-04-01 09:00:00',
'https://grow.google/certificates/data-analytics/',
true,
'["data science", "formation", "Google", "certification"]'
),

('Conférence Internationale sur l''IA',
'Participez à la plus grande conférence sur l''Intelligence Artificielle en Europe. Rencontrez des experts, assistez à des présentations et développez votre réseau.

**Programme :**
- Conférences plénières
- Ateliers pratiques
- Sessions de networking
- Exposition technologique

**Public cible :**
- Étudiants en informatique
- Chercheurs en IA
- Professionnels du secteur

**Inscription :**
Tarif étudiant disponible
Bourses de participation possibles',
'conference',
'AI Europe Association',
'Berlin, Allemagne',
false,
'2025-04-30 23:59:59',
'2025-07-15 09:00:00',
'https://ai-europe-conference.com',
true,
'["IA", "conférence", "Berlin", "networking"]'
);
