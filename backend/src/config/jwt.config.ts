/**
 * Centralized JWT Configuration
 * Industry-standard JWT security configuration for MaBourse
 */

import * as jwt from 'jsonwebtoken';
import { SignOptions, VerifyOptions } from 'jsonwebtoken';
import { getEnv } from '../utils/envValidator';

// JWT Configuration Constants
export const JWT_CONFIG = {
  // Core JWT settings
  SECRET: getEnv('JWT_SECRET', 'mabourse-enterprise-grade-jwt-secret-key-2025-production-ready-security'),
  ALGORITHM: 'HS256' as const,
  ISSUER: getEnv('JWT_ISSUER', 'mabourse-admin-portal'),
  AUDIENCE: getEnv('JWT_AUDIENCE', 'mabourse-admin-users'),
  
  // Token expiration settings
  ACCESS_TOKEN_EXPIRATION: getEnv('JWT_EXPIRATION', '2h'),
  REFRESH_TOKEN_EXPIRATION: getEnv('REFRESH_TOKEN_EXPIRATION', '7d'),
  
  // Security settings
  CLOCK_TOLERANCE: 30, // 30 seconds
  MAX_AGE: '2h',
  
  // Cookie settings
  COOKIE_NAME: 'auth_token',
  REFRESH_COOKIE_NAME: 'refresh_token',
} as const;

// Cookie Configuration
export const COOKIE_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
  path: '/',
  domain: process.env.NODE_ENV === 'production' ? process.env.COOKIE_DOMAIN : undefined,
  priority: 'high' as const,
  
  // Access token cookie (2 hours)
  accessToken: {
    maxAge: 2 * 60 * 60 * 1000, // 2 hours in milliseconds
  },
  
  // Refresh token cookie (7 days)
  refreshToken: {
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
  }
} as const;

// Standard JWT Sign Options
export const getJWTSignOptions = (): SignOptions => ({
  algorithm: JWT_CONFIG.ALGORITHM as any,
  expiresIn: JWT_CONFIG.ACCESS_TOKEN_EXPIRATION as any,
  issuer: JWT_CONFIG.ISSUER,
  audience: JWT_CONFIG.AUDIENCE,
});

// Standard JWT Verify Options
export const getJWTVerifyOptions = (): VerifyOptions => ({
  algorithms: [JWT_CONFIG.ALGORITHM],
  issuer: JWT_CONFIG.ISSUER,
  audience: JWT_CONFIG.AUDIENCE,
  clockTolerance: JWT_CONFIG.CLOCK_TOLERANCE,
  maxAge: JWT_CONFIG.MAX_AGE,
});

// JWT Token Payload Interface
export interface JWTPayload {
  id: number;
  email: string;
  role: string;
  isMainAdmin: boolean;
  jti?: string; // JWT ID for token tracking
  ip?: string; // IP binding for security
  userAgent?: string; // User agent binding
  sessionId?: string; // Session tracking
}

// Utility Functions
export class JWTUtils {
  /**
   * Generate a secure JWT token
   */
  static generateToken(payload: JWTPayload): string {
    // Add security metadata
    const enhancedPayload = {
      ...payload,
      jti: `${payload.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      iat: Math.floor(Date.now() / 1000),
    };

    return jwt.sign(enhancedPayload, JWT_CONFIG.SECRET, getJWTSignOptions());
  }

  /**
   * Verify and decode JWT token
   */
  static verifyToken(token: string): JWTPayload {
    return jwt.verify(token, JWT_CONFIG.SECRET, getJWTVerifyOptions()) as JWTPayload;
  }

  /**
   * Generate refresh token
   */
  static generateRefreshToken(payload: Pick<JWTPayload, 'id' | 'email'>): string {
    const refreshPayload = {
      ...payload,
      type: 'refresh',
      jti: `refresh-${payload.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    return jwt.sign(refreshPayload, JWT_CONFIG.SECRET, {
      algorithm: JWT_CONFIG.ALGORITHM as any,
      expiresIn: JWT_CONFIG.REFRESH_TOKEN_EXPIRATION as any,
      issuer: JWT_CONFIG.ISSUER,
      audience: JWT_CONFIG.AUDIENCE,
    });
  }

  /**
   * Decode token without verification (for debugging)
   */
  static decodeToken(token: string): any {
    return jwt.decode(token);
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.exp) return true;
      
      return Date.now() >= decoded.exp * 1000;
    } catch {
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.exp) return null;
      
      return new Date(decoded.exp * 1000);
    } catch {
      return null;
    }
  }

  /**
   * Generate secure cookie options for access token
   */
  static getAccessTokenCookieOptions() {
    return {
      ...COOKIE_CONFIG,
      ...COOKIE_CONFIG.accessToken,
    };
  }

  /**
   * Generate secure cookie options for refresh token
   */
  static getRefreshTokenCookieOptions() {
    return {
      ...COOKIE_CONFIG,
      ...COOKIE_CONFIG.refreshToken,
    };
  }
}

export default {
  JWT_CONFIG,
  COOKIE_CONFIG,
  JWTUtils,
  getJWTSignOptions,
  getJWTVerifyOptions,
};
