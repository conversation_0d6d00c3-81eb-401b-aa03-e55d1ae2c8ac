PORT=5000
JWT_SECRET=replace-with-strong-production-secret
JWT_EXPIRATION=1d
REFRESH_TOKEN_SECRET=replace-with-strong-production-refresh-secret
REFRESH_TOKEN_EXPIRATION=7d
NODE_ENV=production

# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=production-email-password
EMAIL_FROM=MaBourse <<EMAIL>>

# Database configuration
DB_HOST=production-db-host
DB_PORT=5432
DB_USER=production-db-user
DB_PASSWORD=production-db-password
DB_NAME=mabourse_prod

# Prisma database URL
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# Admin configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=strong-production-password
ADMIN_NAME=Main Administrator

# CORS configuration
CORS_ORIGIN=https://mabourse.com,https://www.mabourse.com,https://admin.mabourse.com

# Frontend URL for password reset links
FRONTEND_URL=https://mabourse.com

# Logging
LOG_LEVEL=info

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=60
