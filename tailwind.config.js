/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // BACKUP: Original colors (for easy reverting)
        // primary: { DEFAULT: '#3a206c', light: '#4a2a7c', dark: '#2a105c' },
        // secondary: { DEFAULT: '#1cc6b7', light: '#2cd6c7', dark: '#0cb6a7' },

        // ENHANCED THEME: Based on beautiful hero gradient
        primary: {
          DEFAULT: '#3a206c',    // Deep purple - main brand color
          light: '#5b3a8c',      // Lighter purple for hover states
          dark: '#2a105c',       // Darker purple for depth
          50: '#f8f6fc',         // Very light purple for backgrounds
          100: '#e8e1f5',        // Light purple for subtle accents
          200: '#d1c2eb',        // Soft purple for borders
          300: '#b19ce0',        // Medium light purple
          400: '#8b6dd4',        // Medium purple
          500: '#3a206c',        // Main primary (DEFAULT)
          600: '#321a5c',        // Slightly darker
          700: '#2a144d',        // Dark purple
          800: '#220f3e',        // Very dark purple
          900: '#1a0a2f',        // Deepest purple
        },
        secondary: {
          DEFAULT: '#1cc6b7',    // Teal - complementary color
          light: '#2cd6c7',      // Light teal
          dark: '#0cb6a7',       // Dark teal
          50: '#f0fdfc',         // Very light teal
          100: '#ccfbf1',        // Light teal background
          200: '#99f6e4',        // Soft teal
          300: '#5eead4',        // Medium light teal
          400: '#2dd4bf',        // Medium teal
          500: '#1cc6b7',        // Main secondary (DEFAULT)
          600: '#0d9488',        // Darker teal
          700: '#0f766e',        // Dark teal
          800: '#115e59',        // Very dark teal
          900: '#134e4a',        // Deepest teal
        },
        // NEW: Gradient colors from hero
        gradient: {
          start: '#111827',      // gray-900 - hero gradient start
          middle: '#2a105c',     // primary-dark - hero gradient middle
          end: '#3a206c',        // primary - hero gradient end
        },
        // NEW: Accent colors for variety
        accent: {
          blue: '#3b82f6',       // For info states
          green: '#10b981',      // For success states
          yellow: '#f59e0b',     // For warning states
          red: '#ef4444',        // For error states
          purple: '#8b5cf6',     // For special highlights
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-in-out',
        'slide-down': 'slideDown 0.5s ease-in-out',
        'slide-left': 'slideLeft 0.5s ease-in-out',
        'slide-right': 'slideRight 0.5s ease-in-out',
        'bounce-slow': 'bounce 3s infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'blob': 'blob 7s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideLeft: {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideRight: {
          '0%': { transform: 'translateX(-20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        blob: {
          '0%': { transform: 'scale(1)' },
          '33%': { transform: 'scale(1.1)' },
          '66%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}