{"name": "scholarship-portal", "version": "1.0.0", "private": true, "dependencies": {"@ant-design/icons": "^5.6.1", "@fortawesome/fontawesome-free": "^7.0.0", "@heroicons/react": "^2.2.0", "@types/node": "^16.18.0", "@types/papaparse": "^5.3.16", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-helmet": "^6.1.11", "antd": "^5.24.9", "autoprefixer": "^10.4.14", "axios": "^1.3.5", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "nodemailer": "^6.10.1", "nth-check": "^2.0.1", "papaparse": "^5.5.2", "postcss": "^8.4.31", "react": "^18.2.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-qr-code": "^2.0.15", "react-router-dom": "^6.10.0", "react-scripts": "^5.0.1", "recharts": "^2.15.3", "tailwindcss": "^3.3.0", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "concurrently \"npm start\" \"cd backend && npm run dev\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/html2canvas": "^0.5.35", "@types/react-csv": "^1.1.10", "@types/react-helmet-async": "^1.0.1", "@types/xlsx": "^0.0.35", "concurrently": "^8.0.1", "typescript": "^5.1.0"}}