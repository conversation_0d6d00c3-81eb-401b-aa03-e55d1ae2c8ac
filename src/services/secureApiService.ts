import axios, { AxiosInstance } from 'axios';

/**
 * Secure API service using HTTP-only cookies for authentication
 * This service handles all authenticated API requests
 */
class SecureApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: (process.env.REACT_APP_API_URL || 'http://localhost:5001') + '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true, // Essential for HTTP-only cookies
    });

    // Setup response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle 401 Unauthorized - redirect to login
        if (error.response?.status === 401) {
          // Only redirect if not already on login page
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/admin/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Generic API request method
   */
  async request(method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, data?: any): Promise<any> {
    try {
      const response = await this.api.request({
        method,
        url,
        data
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Request failed'
      );
    }
  }
}

// Create instance
const secureApiService = new SecureApiService();

// Messages API
export const messagesAPI = {
  // Get all messages
  getMessages: async () => {
    return await secureApiService.request('GET', '/messages');
  },

  // Get message by ID
  getMessage: async (id: number) => {
    return await secureApiService.request('GET', `/messages/${id}`);
  },

  // Create new message
  createMessage: async (messageData: any) => {
    return await secureApiService.request('POST', '/messages', messageData);
  },

  // Update message
  updateMessage: async (id: number, messageData: any) => {
    return await secureApiService.request('PUT', `/messages/${id}`, messageData);
  },

  // Delete message
  deleteMessage: async (id: number) => {
    return await secureApiService.request('DELETE', `/messages/${id}`);
  },

  // Mark message as read
  markAsRead: async (id: number) => {
    return await secureApiService.request('PUT', `/messages/${id}/read`);
  },

  // Mark message as unread
  markAsUnread: async (id: number) => {
    return await secureApiService.request('PUT', `/messages/${id}/unread`);
  },

  // Reply to message
  replyToMessage: async (id: number, replyData: any) => {
    return await secureApiService.request('POST', `/messages/${id}/reply`, replyData);
  }
};

export default secureApiService;
