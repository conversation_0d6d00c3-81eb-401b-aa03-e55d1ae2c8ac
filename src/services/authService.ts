import axios, { AxiosInstance, AxiosResponse } from 'axios';

/**
 * New secure authentication service using HTTP-only cookies
 */
class AuthService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: (process.env.REACT_APP_API_URL || 'http://localhost:5001') + '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true, // Essential for HTTP-only cookies
    });

    // Setup response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        // Handle 401 Unauthorized - redirect to login
        if (error.response?.status === 401) {
          // Only redirect if not already on login page
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/admin/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Admin login
   */
  async adminLogin(email: string, password: string): Promise<any> {
    try {
      const response = await this.api.post('/auth/admin/login', {
        email,
        password
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Login failed'
      );
    }
  }

  /**
   * Admin logout
   */
  async adminLogout(): Promise<any> {
    try {
      const response = await this.api.post('/auth/admin/logout');
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Logout failed'
      );
    }
  }

  /**
   * Get current admin profile
   */
  async getAdminProfile(): Promise<any> {
    try {
      const response = await this.api.get('/auth/admin/profile');
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to get admin profile'
      );
    }
  }

  /**
   * Verify authentication status
   */
  async verifyAuth(): Promise<any> {
    try {
      const response = await this.api.get('/auth/verify');
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Authentication verification failed'
      );
    }
  }

  /**
   * Verify admin authentication status
   */
  async verifyAdminAuth(): Promise<any> {
    try {
      const response = await this.api.get('/auth/admin/verify');
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Admin authentication verification failed'
      );
    }
  }

  /**
   * Generic API request method for authenticated requests
   */
  async request(method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, data?: any): Promise<any> {
    try {
      const response = await this.api.request({
        method,
        url,
        data
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Request failed'
      );
    }
  }

  /**
   * Clear all authentication cookies (for debugging)
   */
  async clearAllCookies(): Promise<any> {
    try {
      const response = await this.api.post('/auth/clear-cookies');
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Failed to clear cookies'
      );
    }
  }

  /**
   * Reset admin account (clear failed attempts)
   */
  async resetAdminAccount(email: string): Promise<any> {
    try {
      const response = await this.api.post('/auth/reset-admin-account', { email });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Failed to reset admin account'
      );
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
