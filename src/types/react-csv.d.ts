declare module 'react-csv' {
  import { ComponentType, ReactNode } from 'react';

  export interface CSVLinkProps {
    data: any[];
    headers?: Array<{ label: string; key: string }>;
    filename?: string;
    className?: string;
    target?: string;
    separator?: string;
    enclosingCharacter?: string;
    onClick?: () => void;
    children?: ReactNode;
  }

  export const CSVLink: ComponentType<CSVLinkProps>;
  export const CSVDownload: ComponentType<Omit<CSVLinkProps, 'children'>>;
}
