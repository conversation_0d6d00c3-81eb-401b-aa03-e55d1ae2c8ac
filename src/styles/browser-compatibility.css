/**
 * Cross-Browser Compatibility Styles
 * 
 * Ensures consistent behavior across all major browsers
 * including Safari, Chrome, Firefox, Edge, and mobile browsers.
 */

/* Reset and normalize browser differences */
* {
  box-sizing: border-box;
}

/* Safari-specific fixes */
@supports (-webkit-appearance: none) {
  /* Fix Safari button styling */
  button {
    -webkit-appearance: none;
    appearance: none;
  }
  
  /* Fix Safari input styling */
  input[type="text"],
  input[type="email"],
  input[type="search"] {
    -webkit-appearance: none;
    appearance: none;
  }
  
  /* Fix Safari transform issues */
  .transform {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Firefox-specific fixes */
@-moz-document url-prefix() {
  /* Fix Firefox button focus outline */
  button::-moz-focus-inner {
    border: 0;
    padding: 0;
  }
  
  /* Fix Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
  }
}

/* Edge/IE compatibility */
@supports (-ms-ime-align: auto) {
  /* Fix Edge flexbox issues */
  .flex {
    display: -ms-flexbox;
    display: flex;
  }
  
  .flex-col {
    -ms-flex-direction: column;
    flex-direction: column;
  }
  
  .items-center {
    -ms-flex-align: center;
    align-items: center;
  }
  
  .justify-between {
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}

/* Webkit-based browsers (Chrome, Safari, Edge) */
@supports (-webkit-backdrop-filter: blur(10px)) {
  .backdrop-blur-md {
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
  }
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(255, 255, 255, 0.95);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for mobile */
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Remove hover effects on touch devices */
  .hover\:scale-105:hover {
    transform: none;
  }
  
  .hover\:shadow-lg:hover {
    box-shadow: none;
  }
  
  /* Optimize animations for mobile */
  * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-gray-200 {
    border-color: #000;
  }
  
  .text-gray-700 {
    color: #000;
  }
  
  .bg-gray-50 {
    background-color: #fff;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: #1a202c;
    color: #e2e8f0;
  }
  
  .text-gray-700 {
    color: #e2e8f0;
  }
  
  .border-gray-200 {
    border-color: #2d3748;
  }
  
  .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  }
}

/* Print styles */
@media print {
  .fixed,
  .sticky {
    position: static !important;
  }
  
  .shadow-lg,
  .shadow-xl {
    box-shadow: none !important;
  }
  
  .bg-gradient-to-r {
    background: #000 !important;
    color: #fff !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus management for keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Fix for iOS Safari viewport units */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Fix for Chrome autofill styling */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px white inset;
  -webkit-text-fill-color: #1a202c;
  transition: background-color 5000s ease-in-out 0s;
}

/* Fix for Firefox placeholder opacity */
::-moz-placeholder {
  opacity: 1;
}

/* Fix for IE11 flexbox bugs */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .flex {
    display: -ms-flexbox;
    display: flex;
  }
  
  .flex-1 {
    -ms-flex: 1 1 0%;
    flex: 1 1 0%;
  }
  
  .flex-shrink-0 {
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
}

/* Fix for Safari date input */
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* Fix for mobile Safari 100vh issue */
.mobile-vh-fix {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* Ensure proper stacking context */
.dropdown-menu {
  z-index: 9999;
  position: relative;
}

/* Fix for Chrome's aggressive caching of transforms */
.will-change-transform {
  will-change: transform;
}

.will-change-auto {
  will-change: auto;
}

/* Performance optimizations */
.gpu-layer {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 10000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* Fix for Android Chrome address bar height changes */
@media screen and (max-width: 768px) {
  .mobile-header-fix {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
  }
}
