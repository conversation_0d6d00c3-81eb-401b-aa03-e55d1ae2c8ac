/* GreatYOP Card Styles - Based on the HTML file structure */

.gy-pcard-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.gy-post-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  cursor: pointer;
  flex: 1;
  min-width: 300px;
  max-width: 400px;
}

.gy-post-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px 0 rgba(0, 0, 0, 0.1), 0 4px 6px 0 rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

.gyp-article-thumb {
  position: relative;
  overflow: hidden;
  height: 184px;
  background-color: #f3f4f6;
}

.gyp-article-thumb a {
  display: block;
  width: 100%;
  height: 100%;
}

.gyp-article-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
  display: block;
  max-width: 100%;
  max-height: 100%;
}

.gy-post-card:hover .gyp-article-thumb img {
  transform: scale(1.05);
}

.gyp-archive-post-header-wrapper {
  padding: 1rem 1.25rem 0.5rem;
}

.entry-header {
  margin: 0;
}

.entry-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
  color: #353535;
  text-transform: capitalize;
}

.entry-title a,
.entry-title button {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
}

.entry-title a:hover,
.entry-title button:hover {
  color: #3c6c90;
}

.gyp-article-thumb button {
  outline: none;
}

.gyp-article-thumb button:focus {
  outline: 2px solid #3c6c90;
  outline-offset: 2px;
}

.gy-pcard-bottom {
  padding: 0.5rem 1.25rem 1rem;
  border-top: 1px solid #f3f4f6;
  margin-top: 0.5rem;
}

.gy-pcard-bottom p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  display: flex;
  align-items: center;
}

.gy-pcard-bottom .flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.gy-pcard-bottom i {
  color: #9ca3af;
  margin-right: 0.5rem;
}

/* FontAwesome icon styles */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-hourglass-half:before {
  content: "\f252";
}

.fa-map-marker-alt:before {
  content: "\f3c5";
}

/* Responsive grid layout */
@media (min-width: 640px) {
  .gy-pcard-wrap {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .gy-post-card {
    flex: none;
    max-width: 400px; /* Maintain maximum width even with single card */
  }
}

@media (min-width: 1024px) {
  .gy-pcard-wrap {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }

  .gy-post-card {
    max-width: 400px; /* Ensure cards don't become too large */
  }
}

/* Status colors */
.gy-pcard-bottom .text-red-600 {
  color: #dc2626;
}

.gy-pcard-bottom .text-amber-600 {
  color: #d97706;
}

.gy-pcard-bottom .text-gray-600 {
  color: #4b5563;
}

/* Animation for card entrance */
.gy-post-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.gy-post-card .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* GreatYOP Pagination Styles */
.gy-paginat-position {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  width: 100%;
  clear: both;
}

.navigation.pagination {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-numbers {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.page-numbers:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: #111827;
}

.page-numbers.current {
  background-color: #3c6c90;
  border-color: #3c6c90;
  color: #ffffff;
  font-weight: 600;
}

.page-numbers.current:hover {
  background-color: #2d5a7b;
  border-color: #2d5a7b;
}

.page-numbers.dots {
  border: none;
  background: none;
  color: #9ca3af;
  cursor: default;
  pointer-events: none;
}

.page-numbers.prev-page,
.page-numbers.next-page {
  font-weight: 600;
}

.page-numbers.prev-page:hover,
.page-numbers.next-page:hover {
  background-color: #3c6c90;
  border-color: #3c6c90;
  color: #ffffff;
}

/* FontAwesome caret icons */
.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

/* Screen reader only text */
.screen-reader-text,
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive pagination */
@media (max-width: 640px) {
  .page-numbers {
    min-width: 2rem;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .nav-links {
    gap: 0.125rem;
  }
}
