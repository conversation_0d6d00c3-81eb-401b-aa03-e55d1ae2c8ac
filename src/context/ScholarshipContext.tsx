import React, { createContext, useContext, useState, useEffect } from 'react';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level?: string;
  country?: string;
  deadline: string;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financial_benefits_summary?: string;
  eligibility_summary?: string;
  scholarship_link?: string;
  youtube_link?: string;
}

interface ScholarshipContextType {
  scholarships: Scholarship[];
  loading: boolean;
  error: string | null;
  getScholarshipById: (id: number) => Scholarship | undefined;
  addScholarship: (scholarship: Omit<Scholarship, 'id'>) => void;
  updateScholarship: (id: number, scholarship: Partial<Scholarship>) => void;
  deleteScholarship: (id: number) => void;
}

const ScholarshipContext = createContext<ScholarshipContextType | undefined>(undefined);

export const useScholarships = () => {
  const context = useContext(ScholarshipContext);
  if (context === undefined) {
    throw new Error('useScholarships must be used within a ScholarshipProvider');
  }
  return context;
};

export const ScholarshipProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Load scholarships from localStorage on initial mount
    const loadScholarships = () => {
      try {
        const storedScholarships = JSON.parse(localStorage.getItem('scholarships') || '[]');
        setScholarships(storedScholarships);
      } catch (err) {
        setError('Failed to load scholarships');
        console.error('Error loading scholarships:', err);
      } finally {
        setLoading(false);
      }
    };

    loadScholarships();
  }, []);

  useEffect(() => {
    // Save scholarships to localStorage whenever they change
    localStorage.setItem('scholarships', JSON.stringify(scholarships));
  }, [scholarships]);

  const getScholarshipById = (id: number) => {
    return scholarships.find((scholarship) => scholarship.id === id);
  };

  const addScholarship = (scholarship: Omit<Scholarship, 'id'>) => {
    const newScholarship = {
      ...scholarship,
      id: Date.now(), // Generate a unique ID
    };
    setScholarships((prev) => [...prev, newScholarship]);
  };

  const updateScholarship = (id: number, updatedScholarship: Partial<Scholarship>) => {
    setScholarships((prev) =>
      prev.map((scholarship) =>
        scholarship.id === id ? { ...scholarship, ...updatedScholarship } : scholarship
      )
    );
  };

  const deleteScholarship = (id: number) => {
    setScholarships((prev) => prev.filter((scholarship) => scholarship.id !== id));
  };

  const value = {
    scholarships,
    loading,
    error,
    getScholarshipById,
    addScholarship,
    updateScholarship,
    deleteScholarship,
  };

  return <ScholarshipContext.Provider value={value}>{children}</ScholarshipContext.Provider>;
};

export default ScholarshipContext; 