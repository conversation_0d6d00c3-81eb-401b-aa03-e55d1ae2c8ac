import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { fr } from '../translations/fr';
import { en } from '../translations/en';
import { ar } from '../translations/ar';

export type Language = 'fr' | 'en' | 'ar';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  translations: typeof fr;
  direction: 'ltr' | 'rtl';
}

const translations = {
  fr,
  en,
  ar,
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>(() => {
    // Always default to French if no language is saved
    const savedLanguage = localStorage.getItem('language') as Language;
    // Validate that the saved language is one of our supported languages
    if (savedLanguage && ['fr', 'en', 'ar'].includes(savedLanguage)) {
      return savedLanguage;
    }
    // Default to French
    return 'fr';
  });

  // Set initial language on mount
  useEffect(() => {
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }, [language]);

  // Update language when it changes
  useEffect(() => {
    localStorage.setItem('language', language);
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }, [language]);

  const direction = language === 'ar' ? 'rtl' : 'ltr';

  return (
    <LanguageContext.Provider
      value={{
        language,
        setLanguage,
        translations: translations[language],
        direction
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};