import { useState, useCallback, useMemo } from 'react';

interface SearchFilters {
  searchTerm: string;
  level?: string;
  country?: string;
  isOpen?: boolean;
}

interface UseSearchReturn<T> {
  filteredItems: T[];
  filters: SearchFilters;
  setFilters: (filters: Partial<SearchFilters>) => void;
  resetFilters: () => void;
}

export const useSearch = <T extends Record<string, any>>(
  items: T[],
  searchableFields: (keyof T)[],
  initialFilters: Partial<SearchFilters> = {}
): UseSearchReturn<T> => {
  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: '',
    ...initialFilters,
  });

  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      searchTerm: '',
      ...initialFilters,
    });
  }, [initialFilters]);

  const filteredItems = useMemo(() => {
    return items.filter((item) => {
      // Search term filter
      const matchesSearch = !filters.searchTerm || searchableFields.some((field) => {
        const value = item[field];
        return value && value.toString().toLowerCase().includes(filters.searchTerm.toLowerCase());
      });

      // Level filter
      const matchesLevel = !filters.level || item.level === filters.level;

      // Country filter
      const matchesCountry = !filters.country || item.country === filters.country;

      // Open/Closed filter
      const matchesStatus = filters.isOpen === undefined || item.isOpen === filters.isOpen;

      return matchesSearch && matchesLevel && matchesCountry && matchesStatus;
    });
  }, [items, filters, searchableFields]);

  return {
    filteredItems,
    filters,
    setFilters: updateFilters,
    resetFilters,
  };
}; 