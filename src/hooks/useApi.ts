import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const useApi = () => {
  const get = async <T>(url: string, config?: AxiosRequestConfig) => {
    return api.get<T>(url, config);
  };

  const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return api.post<T>(url, data, config);
  };

  const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return api.put<T>(url, data, config);
  };

  const del = async <T>(url: string, config?: AxiosRequestConfig) => {
    return api.delete<T>(url, config);
  };

  return {
    get,
    post,
    put,
    delete: del,
  };
}; 