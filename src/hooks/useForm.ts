import { useState, ChangeEvent, FormEvent } from 'react';

interface FormState {
  [key: string]: string;
}

interface UseFormReturn {
  formData: FormState;
  handleChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>, callback: (data: FormState) => void) => void;
  resetForm: () => void;
  setFormData: (data: FormState) => void;
}

export const useForm = (initialState: FormState = {}): UseFormReturn => {
  const [formData, setFormData] = useState<FormState>(initialState);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>, callback: (data: FormState) => void) => {
    e.preventDefault();
    callback(formData);
  };

  const resetForm = () => {
    setFormData(initialState);
  };

  return {
    formData,
    handleChange,
    handleSubmit,
    resetForm,
    setFormData,
  };
}; 