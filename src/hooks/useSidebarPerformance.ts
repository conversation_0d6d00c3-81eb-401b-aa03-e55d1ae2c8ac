import { useEffect, useRef, useState } from 'react';
import sidebarService from '../services/sidebarService';

interface PerformanceMetrics {
  loadTime: number;
  cacheHitRate: number;
  errorRate: number;
  lastUpdate: Date;
}

interface SidebarPerformanceHook {
  metrics: PerformanceMetrics;
  clearCache: () => void;
  preloadData: (configs: any[]) => Promise<void>;
  getCacheStats: () => { size: number; keys: string[] };
}

export const useSidebarPerformance = (): SidebarPerformanceHook => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    cacheHitRate: 0,
    errorRate: 0,
    lastUpdate: new Date()
  });

  const requestCount = useRef(0);
  const cacheHits = useRef(0);
  const errors = useRef(0);
  const totalLoadTime = useRef(0);

  // Track performance metrics
  const trackRequest = (startTime: number, fromCache: boolean, hasError: boolean) => {
    const loadTime = Date.now() - startTime;
    requestCount.current++;
    totalLoadTime.current += loadTime;

    if (fromCache) {
      cacheHits.current++;
    }

    if (hasError) {
      errors.current++;
    }

    setMetrics({
      loadTime: totalLoadTime.current / requestCount.current,
      cacheHitRate: requestCount.current > 0 ? (cacheHits.current / requestCount.current) * 100 : 0,
      errorRate: requestCount.current > 0 ? (errors.current / requestCount.current) * 100 : 0,
      lastUpdate: new Date()
    });
  };

  // Enhanced sidebar service with performance tracking
  const enhancedSidebarService = {
    async fetchSidebarData(config: any) {
      const startTime = Date.now();
      let fromCache = false;
      let hasError = false;

      try {
        // Check if data might be from cache (simplified check)
        const cacheStats = sidebarService.getCacheStats();
        const cacheKey = `${config.type}:${config.currentItem || ''}`;
        fromCache = cacheStats.keys.some(key => key.includes(cacheKey));

        const result = await sidebarService.fetchSidebarData(config);
        return result;
      } catch (error) {
        hasError = true;
        throw error;
      } finally {
        trackRequest(startTime, fromCache, hasError);
      }
    }
  };

  const clearCache = () => {
    sidebarService.clearCache();
    // Reset metrics
    requestCount.current = 0;
    cacheHits.current = 0;
    errors.current = 0;
    totalLoadTime.current = 0;
    setMetrics({
      loadTime: 0,
      cacheHitRate: 0,
      errorRate: 0,
      lastUpdate: new Date()
    });
  };

  const preloadData = async (configs: any[]) => {
    const startTime = Date.now();
    try {
      await sidebarService.preloadData(configs);
      trackRequest(startTime, false, false);
    } catch (error) {
      trackRequest(startTime, false, true);
      throw error;
    }
  };

  const getCacheStats = () => {
    return sidebarService.getCacheStats();
  };

  // Log performance metrics in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Sidebar Performance Metrics:', metrics);
    }
  }, [metrics]);

  return {
    metrics,
    clearCache,
    preloadData,
    getCacheStats
  };
};

export default useSidebarPerformance;
