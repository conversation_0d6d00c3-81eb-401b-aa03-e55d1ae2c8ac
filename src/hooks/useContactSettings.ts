/**
 * Contact Settings Hook
 * 
 * Production-grade React hook for managing contact information and social media links
 * with caching, error handling, and automatic updates.
 */

import { useState, useEffect, useCallback } from 'react';

export interface ContactSettings {
  // Contact Information
  contact_email: string;
  support_email: string;
  phone_primary: string;
  phone_secondary: string;
  whatsapp_number: string;
  
  // Address Information
  address_primary: string;
  address_secondary: string;
  
  // Social Media Links
  facebook_url: string;
  twitter_url: string;
  linkedin_url: string;
  instagram_url: string;
  youtube_url: string;
  telegram_url: string;
  
  // Business Information
  business_name: string;
  business_description: string;
  business_hours: string;
}

interface UseContactSettingsReturn {
  settings: ContactSettings | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Cache for contact settings
let cachedSettings: ContactSettings | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Hook to fetch and manage contact settings
 */
export const useContactSettings = (): UseContactSettingsReturn => {
  const [settings, setSettings] = useState<ContactSettings | null>(cachedSettings);
  const [loading, setLoading] = useState<boolean>(!cachedSettings);
  const [error, setError] = useState<string | null>(null);

  const fetchContactSettings = useCallback(async () => {
    // Check if we have valid cached data
    const now = Date.now();
    if (cachedSettings && (now - cacheTimestamp) < CACHE_DURATION) {
      setSettings(cachedSettings);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await fetch(`${apiUrl}/api/contact-settings/public`);

      if (!response.ok) {
        throw new Error(`Failed to fetch contact settings: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch contact settings');
      }

      const contactSettings = result.data as ContactSettings;
      
      // Update cache
      cachedSettings = contactSettings;
      cacheTimestamp = now;
      
      setSettings(contactSettings);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Contact settings fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache to force fresh fetch
    cachedSettings = null;
    cacheTimestamp = 0;
    await fetchContactSettings();
  }, [fetchContactSettings]);

  useEffect(() => {
    fetchContactSettings();
  }, [fetchContactSettings]);

  return {
    settings,
    loading,
    error,
    refetch
  };
};

/**
 * Helper function to get a specific contact setting
 */
export const useContactSetting = (key: keyof ContactSettings): string => {
  const { settings } = useContactSettings();
  return settings?.[key] || '';
};

/**
 * Helper function to get social media links
 */
export const useSocialMediaLinks = () => {
  const { settings } = useContactSettings();
  
  if (!settings) {
    return [];
  }

  return [
    {
      name: 'Facebook',
      url: settings.facebook_url,
      icon: 'facebook',
      color: '#1877F2'
    },
    {
      name: 'Twitter',
      url: settings.twitter_url,
      icon: 'twitter',
      color: '#1DA1F2'
    },
    {
      name: 'LinkedIn',
      url: settings.linkedin_url,
      icon: 'linkedin',
      color: '#0A66C2'
    },
    {
      name: 'Instagram',
      url: settings.instagram_url,
      icon: 'instagram',
      color: '#E4405F'
    },
    {
      name: 'YouTube',
      url: settings.youtube_url,
      icon: 'youtube',
      color: '#FF0000'
    },
    {
      name: 'Telegram',
      url: settings.telegram_url,
      icon: 'telegram',
      color: '#0088CC'
    }
  ].filter(link => link.url && link.url !== '#');
};

/**
 * Helper function to get contact information
 */
export const useContactInfo = () => {
  const { settings } = useContactSettings();
  
  if (!settings) {
    return {
      emails: [],
      phones: [],
      addresses: []
    };
  }

  return {
    emails: [
      { label: 'Contact', value: settings.contact_email },
      { label: 'Support', value: settings.support_email }
    ].filter(item => item.value),
    
    phones: [
      { label: 'Principal', value: settings.phone_primary },
      { label: 'Secondaire', value: settings.phone_secondary },
      { label: 'WhatsApp', value: settings.whatsapp_number }
    ].filter(item => item.value),
    
    addresses: [
      { label: 'Adresse principale', value: settings.address_primary },
      { label: 'Adresse secondaire', value: settings.address_secondary }
    ].filter(item => item.value)
  };
};

/**
 * Clear contact settings cache (useful for admin updates)
 */
export const clearContactSettingsCache = () => {
  cachedSettings = null;
  cacheTimestamp = 0;
};
