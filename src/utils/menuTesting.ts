/**
 * Menu Testing Utilities
 * 
 * Comprehensive testing utilities to verify menu functionality
 * across different browsers and devices.
 */

export interface MenuTestResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

export class MenuTester {
  private results: MenuTestResult[] = [];

  /**
   * Run all menu tests
   */
  async runAllTests(): Promise<MenuTestResult[]> {
    this.results = [];
    
    // Browser capability tests
    await this.testBrowserCapabilities();
    
    // API endpoint tests
    await this.testAPIEndpoints();
    
    // DOM interaction tests
    await this.testDOMInteractions();
    
    // Responsive design tests
    await this.testResponsiveDesign();
    
    // Accessibility tests
    await this.testAccessibility();
    
    // Performance tests
    await this.testPerformance();
    
    return this.results;
  }

  /**
   * Test browser capabilities
   */
  private async testBrowserCapabilities(): Promise<void> {
    // Test CSS Grid support
    this.addResult({
      test: 'CSS Grid Support',
      passed: CSS.supports('display', 'grid'),
      message: CSS.supports('display', 'grid') ? 'CSS Grid is supported' : 'CSS Grid is not supported'
    });

    // Test Flexbox support
    this.addResult({
      test: 'Flexbox Support',
      passed: CSS.supports('display', 'flex'),
      message: CSS.supports('display', 'flex') ? 'Flexbox is supported' : 'Flexbox is not supported'
    });

    // Test backdrop-filter support
    this.addResult({
      test: 'Backdrop Filter Support',
      passed: CSS.supports('backdrop-filter', 'blur(10px)'),
      message: CSS.supports('backdrop-filter', 'blur(10px)') ? 'Backdrop filter is supported' : 'Backdrop filter is not supported'
    });

    // Test touch support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    this.addResult({
      test: 'Touch Support',
      passed: true, // Always pass, just informational
      message: hasTouch ? 'Touch events are supported' : 'Touch events are not supported',
      details: { hasTouch, maxTouchPoints: navigator.maxTouchPoints }
    });

    // Test viewport dimensions
    this.addResult({
      test: 'Viewport Dimensions',
      passed: true,
      message: `Viewport: ${window.innerWidth}x${window.innerHeight}`,
      details: {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio
      }
    });
  }

  /**
   * Test API endpoints
   */
  private async testAPIEndpoints(): Promise<void> {
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';
    const endpoints = [
      '/api/countries',
      '/api/scholarships/levels',
      '/api/scholarships/funding-sources',
      '/api/opportunities/types'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${baseURL}${endpoint}`);
        const isSuccess = response.ok;
        
        this.addResult({
          test: `API Endpoint: ${endpoint}`,
          passed: isSuccess,
          message: isSuccess ? `${endpoint} is accessible` : `${endpoint} returned ${response.status}`,
          details: {
            status: response.status,
            statusText: response.statusText,
            url: `${baseURL}${endpoint}`
          }
        });

        if (isSuccess) {
          const data = await response.json();
          this.addResult({
            test: `API Data: ${endpoint}`,
            passed: Array.isArray(data) || (data && Array.isArray(data.data)),
            message: `Data structure is valid for ${endpoint}`,
            details: {
              dataType: typeof data,
              isArray: Array.isArray(data),
              hasDataProperty: data && Array.isArray(data.data),
              itemCount: Array.isArray(data) ? data.length : (data && Array.isArray(data.data) ? data.data.length : 0)
            }
          });
        }
      } catch (error) {
        this.addResult({
          test: `API Endpoint: ${endpoint}`,
          passed: false,
          message: `Failed to fetch ${endpoint}: ${(error as Error).message}`,
          details: { error: (error as Error).message }
        });
      }
    }
  }

  /**
   * Test DOM interactions
   */
  private async testDOMInteractions(): Promise<void> {
    // Test if dropdown elements exist
    const dropdownTriggers = document.querySelectorAll('[aria-expanded]');
    this.addResult({
      test: 'Dropdown Triggers Present',
      passed: dropdownTriggers.length > 0,
      message: `Found ${dropdownTriggers.length} dropdown triggers`,
      details: { count: dropdownTriggers.length }
    });

    // Test mobile menu button
    const mobileMenuButton = document.querySelector('[aria-label*="menu"], [aria-label*="navigation"]');
    this.addResult({
      test: 'Mobile Menu Button Present',
      passed: !!mobileMenuButton,
      message: mobileMenuButton ? 'Mobile menu button found' : 'Mobile menu button not found'
    });

    // Test navigation links
    const navLinks = document.querySelectorAll('nav a[href]');
    this.addResult({
      test: 'Navigation Links Present',
      passed: navLinks.length > 0,
      message: `Found ${navLinks.length} navigation links`,
      details: { count: navLinks.length }
    });

    // Test ARIA attributes
    const elementsWithAria = document.querySelectorAll('[aria-expanded], [aria-haspopup], [role]');
    this.addResult({
      test: 'ARIA Attributes Present',
      passed: elementsWithAria.length > 0,
      message: `Found ${elementsWithAria.length} elements with ARIA attributes`,
      details: { count: elementsWithAria.length }
    });
  }

  /**
   * Test responsive design
   */
  private async testResponsiveDesign(): Promise<void> {
    const width = window.innerWidth;
    
    // Test mobile breakpoint
    const isMobile = width < 768;
    this.addResult({
      test: 'Mobile Breakpoint Detection',
      passed: true,
      message: isMobile ? 'Mobile layout detected' : 'Desktop layout detected',
      details: { width, isMobile, breakpoint: isMobile ? 'mobile' : 'desktop' }
    });

    // Test if mobile menu is hidden on desktop
    const mobileMenu = document.querySelector('.md\\:hidden');
    this.addResult({
      test: 'Mobile Menu Visibility',
      passed: !!mobileMenu,
      message: mobileMenu ? 'Mobile menu element found' : 'Mobile menu element not found'
    });

    // Test if desktop menu is hidden on mobile
    const desktopMenu = document.querySelector('.hidden.md\\:flex, .hidden.md\\:block');
    this.addResult({
      test: 'Desktop Menu Visibility',
      passed: !!desktopMenu,
      message: desktopMenu ? 'Desktop menu element found' : 'Desktop menu element not found'
    });
  }

  /**
   * Test accessibility features
   */
  private async testAccessibility(): Promise<void> {
    // Test keyboard navigation
    const focusableElements = document.querySelectorAll(
      'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    this.addResult({
      test: 'Focusable Elements',
      passed: focusableElements.length > 0,
      message: `Found ${focusableElements.length} focusable elements`,
      details: { count: focusableElements.length }
    });

    // Test alt text on images
    const images = document.querySelectorAll('img');
    const imagesWithAlt = Array.from(images).filter(img => img.alt && img.alt.trim() !== '');
    this.addResult({
      test: 'Image Alt Text',
      passed: images.length === 0 || imagesWithAlt.length === images.length,
      message: `${imagesWithAlt.length}/${images.length} images have alt text`,
      details: { total: images.length, withAlt: imagesWithAlt.length }
    });

    // Test heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    this.addResult({
      test: 'Heading Elements',
      passed: headings.length > 0,
      message: `Found ${headings.length} heading elements`,
      details: { count: headings.length }
    });
  }

  /**
   * Test performance metrics
   */
  private async testPerformance(): Promise<void> {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        
        this.addResult({
          test: 'Page Load Performance',
          passed: loadTime < 3000, // Less than 3 seconds
          message: `Page loaded in ${loadTime.toFixed(2)}ms`,
          details: {
            loadTime,
            domContentLoaded,
            firstContentfulPaint: navigation.responseEnd - navigation.requestStart
          }
        });
      }
    }

    // Test memory usage (if available)
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.addResult({
        test: 'Memory Usage',
        passed: memory.usedJSHeapSize < 50 * 1024 * 1024, // Less than 50MB
        message: `Using ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB of memory`,
        details: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        }
      });
    }
  }

  /**
   * Add a test result
   */
  private addResult(result: MenuTestResult): void {
    this.results.push(result);
  }

  /**
   * Get test summary
   */
  getTestSummary(): { total: number; passed: number; failed: number; passRate: number } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    const passRate = total > 0 ? (passed / total) * 100 : 0;

    return { total, passed, failed, passRate };
  }

  /**
   * Export results as JSON
   */
  exportResults(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      summary: this.getTestSummary(),
      results: this.results
    }, null, 2);
  }
}

// Export singleton instance
export const menuTester = new MenuTester();
