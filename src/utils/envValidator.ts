/**
 * Environment variable validation utility
 * 
 * This utility validates that all required environment variables are set
 * and logs warnings for missing optional variables.
 */

// Required environment variables
const requiredVariables = [
  'REACT_APP_API_URL',
  'REACT_APP_USE_REAL_API',
];

// Optional environment variables with default values
const optionalVariables: Record<string, string> = {
  'REACT_APP_ENABLE_TWO_FACTOR': 'false',
  'REACT_APP_ENABLE_EMAIL_NOTIFICATIONS': 'false',
  'REACT_APP_APP_NAME': 'MaBourse Admin Portal',
  'REACT_APP_DEFAULT_LANGUAGE': 'fr',
  'REACT_APP_DEBUG_MODE': 'false',
};

/**
 * Validates environment variables and logs warnings/errors
 * @returns {boolean} True if all required variables are set, false otherwise
 */
export const validateEnv = (): boolean => {
  let isValid = true;
  const missingVars: string[] = [];

  // Check required variables
  requiredVariables.forEach(varName => {
    if (!process.env[varName]) {
      console.error(`❌ Required environment variable ${varName} is not set`);
      missingVars.push(varName);
      isValid = false;
    }
  });

  // Check optional variables and set defaults if needed
  Object.entries(optionalVariables).forEach(([varName, defaultValue]) => {
    if (!process.env[varName]) {
      console.warn(`⚠️ Optional environment variable ${varName} is not set, using default: ${defaultValue}`);
      // Note: We can't actually set process.env here in production builds
      // This is just for logging purposes
    }
  });

  // Log environment mode
  console.info(`🌍 Environment: ${process.env.NODE_ENV}`);
  
  if (!isValid) {
    console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
    console.error('Please check your .env file or environment configuration');
  } else {
    console.info('✅ Environment validation passed');
  }

  return isValid;
};

/**
 * Gets an environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {string} fallback - The fallback value if the variable is not set
 * @returns {string} The environment variable value or fallback
 */
export const getEnv = (name: string, fallback: string): string => {
  return process.env[name] || fallback;
};

/**
 * Gets a boolean environment variable with a fallback value
 * @param {string} name - The environment variable name
 * @param {boolean} fallback - The fallback value if the variable is not set
 * @returns {boolean} The environment variable value as boolean or fallback
 */
export const getBoolEnv = (name: string, fallback: boolean): boolean => {
  const value = process.env[name];
  if (value === undefined) return fallback;
  return value.toLowerCase() === 'true';
};

export default validateEnv;
