/**
 * Date formatting utility functions for scholarship deadlines
 */

/**
 * Calculate days remaining until a deadline
 * @param deadlineDate - The deadline date string
 * @param language - The language code (fr, en, ar)
 * @returns Object containing days remaining and status
 */
export const calculateDaysRemaining = (deadlineDate: string, language: string = 'fr'): {
  daysRemaining: number;
  isOpen: boolean;
  formattedText: string;
} => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day

  const deadline = new Date(deadlineDate);
  deadline.setHours(23, 59, 59, 999); // Set time to end of day

  // Calculate difference in milliseconds
  const diffTime = deadline.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  const isOpen = diffDays >= 0;

  // Format the text based on days remaining and language
  let formattedText = '';

  // Text templates for different languages
  const translations = {
    fr: {
      expired: 'Clôturée',
      today: 'Dernier jour',
      oneDay: '1 jour restant',
      manyDays: (days: number) => `${days} jours restants`
    },
    en: {
      expired: 'Expired',
      today: 'Last day',
      oneDay: '1 day left',
      manyDays: (days: number) => `${days} days left`
    },
    ar: {
      expired: 'منتهية',
      today: 'آخر يوم',
      oneDay: 'يوم واحد متبقي',
      manyDays: (days: number) => `${days} أيام متبقية`
    }
  };

  // Default to French if language not supported
  const lang = translations[language as keyof typeof translations] || translations.fr;

  if (!isOpen) {
    formattedText = lang.expired;
  } else if (diffDays === 0) {
    formattedText = lang.today;
  } else if (diffDays === 1) {
    formattedText = lang.oneDay;
  } else {
    formattedText = lang.manyDays(diffDays);
  }

  return {
    daysRemaining: diffDays,
    isOpen,
    formattedText
  };
};

/**
 * Format a date to a localized string
 * @param dateString - The date string to format
 * @param language - The language code (fr, en, ar)
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, language: string = 'fr'): string => {
  const date = new Date(dateString);

  // Map language codes to locales
  const localeMap: Record<string, string> = {
    fr: 'fr-FR',
    en: 'en-US',
    ar: 'ar-SA'
  };

  // Get the locale or default to French
  const locale = localeMap[language] || 'fr-FR';

  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
