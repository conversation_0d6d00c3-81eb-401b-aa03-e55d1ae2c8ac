/**
 * Data Prefetcher Utility
 * Intelligently prefetches data for better user experience
 */

import sidebarService, { SidebarConfig } from '../services/sidebarService';

interface PrefetchConfig {
  priority: 'high' | 'medium' | 'low';
  delay?: number;
  condition?: () => boolean;
}

interface PrefetchItem {
  config: SidebarConfig;
  options: PrefetchConfig;
}

class DataPrefetcher {
  private prefetchQueue: PrefetchItem[] = [];
  private isProcessing = false;
  private prefetchedUrls = new Set<string>();

  /**
   * Add items to prefetch queue
   */
  public addToPrefetchQueue(items: PrefetchItem[]): void {
    this.prefetchQueue.push(...items);
    this.processPrefetchQueue();
  }

  /**
   * Prefetch data for common navigation patterns
   */
  public prefetchCommonRoutes(): void {
    const commonConfigs: PrefetchItem[] = [
      // Prefetch countries sidebar data
      {
        config: { type: 'countries', limit: 15 },
        options: { priority: 'high', delay: 100 }
      },
      // Prefetch levels sidebar data
      {
        config: { type: 'levels', limit: 10 },
        options: { priority: 'high', delay: 200 }
      },
      // Prefetch opportunities sidebar data
      {
        config: { type: 'opportunities', limit: 10 },
        options: { priority: 'medium', delay: 300 }
      },
      // Prefetch latest scholarships
      {
        config: { type: 'countries' }, // Will fetch latest scholarships
        options: { priority: 'medium', delay: 500 }
      }
    ];

    this.addToPrefetchQueue(commonConfigs);
  }

  /**
   * Prefetch data based on user's current page
   */
  public prefetchRelatedData(currentPageType: string, currentItem?: string): void {
    const relatedConfigs: PrefetchItem[] = [];

    switch (currentPageType) {
      case 'country':
        if (currentItem) {
          // Prefetch other countries and latest scholarships
          relatedConfigs.push(
            {
              config: { type: 'countries', currentItem, limit: 15 },
              options: { priority: 'high', delay: 0 }
            },
            {
              config: { type: 'levels', limit: 10 },
              options: { priority: 'medium', delay: 200 }
            }
          );
        }
        break;

      case 'level':
        if (currentItem) {
          // Prefetch other levels and latest scholarships
          relatedConfigs.push(
            {
              config: { type: 'levels', currentItem, limit: 10 },
              options: { priority: 'high', delay: 0 }
            },
            {
              config: { type: 'countries', limit: 15 },
              options: { priority: 'medium', delay: 200 }
            }
          );
        }
        break;

      case 'opportunity':
        if (currentItem) {
          // Prefetch other opportunity types and latest opportunities
          relatedConfigs.push(
            {
              config: { type: 'opportunities', currentItem, limit: 10 },
              options: { priority: 'high', delay: 0 }
            }
          );
        }
        break;
    }

    this.addToPrefetchQueue(relatedConfigs);
  }

  /**
   * Prefetch data when user hovers over links
   */
  public prefetchOnHover(targetConfig: SidebarConfig): void {
    const prefetchItem: PrefetchItem = {
      config: targetConfig,
      options: { 
        priority: 'medium', 
        delay: 100,
        condition: () => this.shouldPrefetch()
      }
    };

    this.addToPrefetchQueue([prefetchItem]);
  }

  /**
   * Process the prefetch queue with priority and timing
   */
  private async processPrefetchQueue(): Promise<void> {
    if (this.isProcessing || this.prefetchQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    // Sort by priority
    this.prefetchQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.options.priority] - priorityOrder[a.options.priority];
    });

    while (this.prefetchQueue.length > 0) {
      const item = this.prefetchQueue.shift();
      if (!item) continue;

      try {
        // Check condition if provided
        if (item.options.condition && !item.options.condition()) {
          continue;
        }

        // Apply delay if specified
        if (item.options.delay) {
          await new Promise(resolve => setTimeout(resolve, item.options.delay));
        }

        // Check if we should still prefetch (user might have navigated away)
        if (!this.shouldPrefetch()) {
          break;
        }

        // Generate cache key to avoid duplicate prefetches
        const cacheKey = this.generateCacheKey(item.config);
        if (this.prefetchedUrls.has(cacheKey)) {
          continue;
        }

        // Prefetch the data
        await sidebarService.fetchSidebarData(item.config);
        this.prefetchedUrls.add(cacheKey);

        console.log(`Prefetched data for ${item.config.type}:`, item.config);

      } catch (error) {
        console.warn('Prefetch failed:', error);
      }

      // Small delay between prefetches to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    this.isProcessing = false;
  }

  /**
   * Check if we should continue prefetching
   */
  private shouldPrefetch(): boolean {
    // Don't prefetch if user is on slow connection
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && (connection.saveData || connection.effectiveType === 'slow-2g')) {
        return false;
      }
    }

    // Don't prefetch if page is not visible
    if (document.hidden) {
      return false;
    }

    // Don't prefetch if user is idle for too long
    return true;
  }

  /**
   * Generate cache key for deduplication
   */
  private generateCacheKey(config: SidebarConfig): string {
    const parts: string[] = [config.type];
    if (config.currentItem) parts.push(config.currentItem);
    if (config.excludeId) parts.push(`exclude-${config.excludeId}`);
    if (config.limit) parts.push(`limit-${config.limit}`);
    return parts.join(':');
  }

  /**
   * Clear prefetch cache
   */
  public clearPrefetchCache(): void {
    this.prefetchedUrls.clear();
    this.prefetchQueue.length = 0;
  }

  /**
   * Get prefetch statistics
   */
  public getPrefetchStats(): { queueSize: number; prefetchedCount: number } {
    return {
      queueSize: this.prefetchQueue.length,
      prefetchedCount: this.prefetchedUrls.size
    };
  }
}

// Export singleton instance
export const dataPrefetcher = new DataPrefetcher();
export default dataPrefetcher;
