/**
 * Converts a string to a URL-friendly slug
 * @param text The text to convert to a slug
 * @returns A URL-friendly slug
 */
export const slugify = (text: string): string => {
  return text
    .toString()
    .normalize('NFD')                   // Split accented characters into their base characters and diacritical marks
    .replace(/[\u0300-\u036f]/g, '')    // Remove diacritical marks
    .toLowerCase()                      // Convert to lowercase
    .trim()                             // Remove whitespace from both ends
    .replace(/\s+/g, '-')               // Replace spaces with hyphens
    .replace(/[^\w-]+/g, '')            // Remove all non-word characters (except hyphens)
    .replace(/--+/g, '-')               // Replace multiple hyphens with a single hyphen
    .replace(/^-+/, '')                 // Remove leading hyphens
    .replace(/-+$/, '');                // Remove trailing hyphens
};

/**
 * Generates a slug from a scholarship title and ID
 * @param title The scholarship title
 * @param id The scholarship ID
 * @returns A URL-friendly slug with the ID appended
 */
export const generateScholarshipSlug = (title: string, id: number): string => {
  return `${slugify(title)}-${id}`;
};

/**
 * Extracts the ID from a scholarship slug
 * @param slug The scholarship slug
 * @returns The scholarship ID
 */
export const extractIdFromSlug = (slug: string): number | null => {
  const match = slug.match(/-(\d+)$/);
  if (match && match[1]) {
    return parseInt(match[1], 10);
  }
  return null;
};
