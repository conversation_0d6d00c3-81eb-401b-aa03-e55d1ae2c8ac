import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import sidebarService, { SidebarConfig, SidebarData } from '../services/sidebarService';
import dataPrefetcher from '../utils/dataPrefetcher';

interface ProfessionalSidebarProps {
  config: SidebarConfig;
  className?: string;
}

interface SidebarItem {
  id?: number;
  name: string;
  title?: string;
  thumbnail?: string;
  totalCount?: number;
  openCount?: number;
  activeCount?: number;
  count?: number;
  slug?: string;
  country?: string;
  level?: string;
  type?: string;
  organization?: string;
  deadline?: string;
  isOpen?: boolean;
  isActive?: boolean;
  isExpired?: boolean;
  daysRemaining?: number;
}

const ProfessionalSidebar: React.FC<ProfessionalSidebarProps> = ({ config, className = '' }) => {
  const [sidebarData, setSidebarData] = useState<SidebarData>({
    relatedItems: [],
    latestItems: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSidebarData = React.useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the enhanced caching service
      const data = await sidebarService.fetchSidebarData(config);
      setSidebarData(data);

      // If we got some data but not all, show partial loading
      if (data.relatedItems.length === 0 && data.latestItems.length === 0) {
        setError('Unable to load sidebar content');
      }
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
      setError('Failed to load sidebar content');

      // Try to get fallback data from cache
      try {
        const fallbackData = await sidebarService.fetchSidebarData(config);
        if (fallbackData.relatedItems.length > 0 || fallbackData.latestItems.length > 0) {
          setSidebarData(fallbackData);
          setError('Showing cached content');
        }
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  }, [config]);

  useEffect(() => {
    fetchSidebarData();
  }, [fetchSidebarData]);



  const getRelatedItemsTitle = () => {
    switch (config.type) {
      case 'countries':
        return 'Autres Pays';
      case 'levels':
        return 'Autres Niveaux d\'Études';
      case 'opportunities':
        return 'Autres Types d\'Opportunités';
      default:
        return 'Éléments Connexes';
    }
  };

  const getLatestItemsTitle = () => {
    return config.type === 'opportunities' ? 'Dernières Opportunités' : 'Dernières Bourses';
  };

  const getRelatedItemLink = (item: SidebarItem) => {
    switch (config.type) {
      case 'countries':
        return `/countries/${encodeURIComponent(item.name)}`;
      case 'levels':
        return `/scholarships/level/${encodeURIComponent(item.name)}`;
      case 'opportunities':
        return `/opportunities/type/${encodeURIComponent(item.name)}`;
      default:
        return '#';
    }
  };

  const getRelatedItemIcon = (item: SidebarItem) => {
    switch (config.type) {
      case 'countries':
        return sidebarService.getCountryFlag(item.name);
      case 'levels':
        return sidebarService.getLevelIcon(item.name);
      case 'opportunities':
        return sidebarService.getOpportunityIcon(item.name);
      default:
        return '📄';
    }
  };

  const getLatestItemLink = (item: SidebarItem) => {
    if (config.type === 'opportunities') {
      return `/opportunities/${item.id}`;
    }
    return `/scholarships/${item.id}`;
  };

  const renderErrorState = (message: string, onRetry?: () => void) => (
    <div className="text-center py-8">
      <div className="text-4xl mb-2">⚠️</div>
      <p className="text-sm text-gray-600 mb-3">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
        >
          Réessayer
        </button>
      )}
    </div>
  );

  const renderRelatedItems = () => {
    if (loading) {
      return (
        <div className="space-y-2">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="flex items-center p-3 rounded-lg">
                <div className="w-8 h-8 bg-gray-200 rounded mr-3"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (error && sidebarData.relatedItems.length === 0) {
      return renderErrorState(error, fetchSidebarData);
    }

    return (
      <div className="space-y-1 max-h-80 overflow-y-auto custom-scrollbar">
        {sidebarData.relatedItems.slice(0, 15).map((item: SidebarItem, index) => (
          <Link
            key={index}
            to={getRelatedItemLink(item)}
            className="flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group border border-transparent hover:border-blue-100 hover:shadow-sm"
            onMouseEnter={() => {
              // Prefetch data on hover for better UX
              if (config.type === 'countries') {
                dataPrefetcher.prefetchOnHover({ type: 'countries', currentItem: item.name });
              }
            }}
          >
            <div className="relative">
              <span className="text-2xl mr-3 group-hover:scale-110 transition-transform duration-300 filter group-hover:brightness-110">
                {getRelatedItemIcon(item)}
              </span>
              {((item.openCount || 0) > 0 || (item.activeCount || 0) > 0) && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-200">
                {item.name}
              </div>
              <div className="text-xs text-gray-500 flex items-center space-x-2">
                <span className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1"></span>
                  {item.totalCount || item.count} {config.type === 'opportunities' ? 'opportunités' : 'bourses'}
                </span>
                {(item.openCount || item.activeCount) && (
                  <span className="flex items-center text-green-600">
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
                    {item.openCount || item.activeCount} {config.type === 'opportunities' ? 'actives' : 'ouvertes'}
                  </span>
                )}
              </div>
            </div>
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  const renderLatestItems = () => {
    if (loading) {
      return (
        <div className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="p-3 rounded-lg">
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (error && sidebarData.latestItems.length === 0) {
      return renderErrorState(error, fetchSidebarData);
    }

    return (
      <div className="space-y-3">
        {sidebarData.latestItems.slice(0, 5).map((item: SidebarItem, index) => (
          <Link
            key={item.id}
            to={getLatestItemLink(item)}
            className="block p-4 rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 group border border-transparent hover:border-blue-100 hover:shadow-sm"
          >
            <div className="flex items-start space-x-3">
              {/* Thumbnail */}
              <div className="flex-shrink-0">
                {item.thumbnail ? (
                  <div className="relative">
                    <img
                      src={item.thumbnail}
                      alt={item.title || item.name}
                      className="w-12 h-12 rounded-lg object-cover border border-gray-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/assets/default-scholarship.jpg';
                      }}
                    />
                    <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                      (config.type === 'opportunities' ? item.isActive : item.isOpen)
                        ? 'bg-green-500'
                        : 'bg-gray-400'
                    }`}></div>
                  </div>
                ) : (
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border border-gray-200">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                      (config.type === 'opportunities' ? item.isActive : item.isOpen)
                        ? 'bg-green-500'
                        : 'bg-gray-400'
                    }`}></div>
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 text-sm mb-2 line-clamp-2 group-hover:text-blue-700 transition-colors duration-200">
                  {item.title}
                </h4>
                <div className="flex items-center text-xs text-gray-500 mb-2 space-x-2">
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {config.type === 'opportunities' ? item.organization : item.country}
                  </span>
                  <span className="text-gray-300">•</span>
                  <span className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    {config.type === 'opportunities' ? item.type : item.level}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                    (config.type === 'opportunities' ? item.isActive : item.isOpen)
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-gray-100 text-gray-600 border border-gray-200'
                  }`}>
                    <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                      (config.type === 'opportunities' ? item.isActive : item.isOpen)
                        ? 'bg-green-500'
                        : 'bg-gray-400'
                    }`}></span>
                    {(config.type === 'opportunities' ? item.isActive : item.isOpen)
                      ? (config.type === 'opportunities' ? 'Active' : 'Ouverte')
                      : (config.type === 'opportunities' ? 'Inactive' : 'Fermée')
                    }
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  return (
    <>

      <div className={`lg:w-1/3 space-y-6 ${className}`}>
        {/* Error Banner */}
        {error && (sidebarData.relatedItems.length > 0 || sidebarData.latestItems.length > 0) && (
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3 flex-1">
                <span className="text-sm font-medium text-yellow-800">{error}</span>
              </div>
              <button
                onClick={fetchSidebarData}
                className="ml-3 text-xs font-medium text-yellow-700 hover:text-yellow-900 bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded-full transition-colors duration-200"
              >
                Actualiser
              </button>
            </div>
          </div>
        )}

        {/* Related Items Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <span className="text-blue-600">🔗</span>
              </div>
              <span className="flex-1">{getRelatedItemsTitle()}</span>
              {!loading && sidebarData.relatedItems.length > 0 && (
                <span className="text-xs font-medium text-blue-700 bg-blue-100 px-2.5 py-1 rounded-full">
                  {sidebarData.relatedItems.length}
                </span>
              )}
            </h3>
          </div>
          <div className="p-6">
            {renderRelatedItems()}
          </div>
        </div>

        {/* Latest Items Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <span className="text-green-600">⭐</span>
              </div>
              <span className="flex-1">{getLatestItemsTitle()}</span>
              {!loading && sidebarData.latestItems.length > 0 && (
                <span className="text-xs font-medium text-green-700 bg-green-100 px-2.5 py-1 rounded-full">
                  {sidebarData.latestItems.length}
                </span>
              )}
            </h3>
          </div>
          <div className="p-6">
            {renderLatestItems()}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfessionalSidebar;
