import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../../context/LanguageContext';
import LanguageSwitcher from '../common/LanguageSwitcher';

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { translations } = useLanguage();

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="bg-white shadow-sm fixed w-full top-0 z-50">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <img
                  src="/assets/images/MaBoursedetudeLogo.jpeg"
                  alt={translations.brand.name}
                  className="h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight">
                  {translations.brand.name}
                </span>
                <span className="text-xs text-gray-500 font-medium tracking-wider">
                  {translations.brand.tagline}
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`text-sm font-medium ${
                isActive('/') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.home}
            </Link>
            <Link
              to="/scholarships"
              className={`text-sm font-medium ${
                isActive('/scholarships') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.scholarships}
            </Link>
            <Link
              to="/countries"
              className={`text-sm font-medium ${
                isActive('/countries') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.countries}
            </Link>
            <Link
              to="/guides"
              className={`text-sm font-medium ${
                isActive('/guides') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.guides}
            </Link>
            <Link
              to="/opportunities"
              className={`text-sm font-medium ${
                isActive('/opportunities') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.opportunities}
            </Link>
            <Link
              to="/about"
              className={`text-sm font-medium ${
                isActive('/about') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.about}
            </Link>
            <Link
              to="/contact"
              className={`text-sm font-medium ${
                isActive('/contact') ? 'text-secondary' : 'text-gray-700 hover:text-secondary'
              }`}
            >
              {translations.navigation.contact}
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-secondary focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>

          <div className="flex items-center">
            <LanguageSwitcher />
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link
              to="/"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.home}
            </Link>
            <Link
              to="/scholarships"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/scholarships') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.scholarships}
            </Link>
            <Link
              to="/countries"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/countries') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.countries}
            </Link>
            <Link
              to="/guides"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/guides') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.guides}
            </Link>
            <Link
              to="/opportunities"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/opportunities') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.opportunities}
            </Link>
            <Link
              to="/about"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/about') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.about}
            </Link>
            <Link
              to="/contact"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/scholarships') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.scholarships}
            </Link>
            <Link
              to="/contact"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                isActive('/contact') ? 'text-secondary bg-gray-50' : 'text-gray-700 hover:text-secondary hover:bg-gray-50'
              }`}
            >
              {translations.navigation.contact}
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header; 