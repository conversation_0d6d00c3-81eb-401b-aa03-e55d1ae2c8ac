import React from 'react';
import { Outlet } from 'react-router-dom';
import EnhancedHeader from './EnhancedHeader';
import Footer from './Footer';
import { useLanguage } from '../../context/LanguageContext';

const Layout: React.FC = () => {
  const { direction } = useLanguage();

  return (
    <div 
      className={`min-h-screen flex flex-col ${direction === 'rtl' ? 'rtl' : 'ltr'}`}
      dir={direction}
    >
      <EnhancedHeader />
      <main className="flex-grow pt-16">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default Layout; 