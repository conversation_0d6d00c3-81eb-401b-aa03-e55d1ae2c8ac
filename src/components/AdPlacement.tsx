import React, { useEffect, useRef } from 'react';

// Extend the Window interface to include adsbygoogle
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export type AdSize = 
  | 'responsive'
  | 'auto'
  | 'horizontal'
  | 'rectangle'
  | 'vertical'
  | 'leaderboard';

interface AdPlacementProps {
  adClient?: string;
  adSlot: string;
  adSize: AdSize;
  adFormat?: 'auto' | 'fluid' | 'rectangle';
  className?: string;
  style?: React.CSSProperties;
  responsive?: boolean;
  fullWidth?: boolean;
}

const getAdSizeConfig = (size: AdSize): string => {
  switch (size) {
    case 'responsive':
      return 'data-ad-format="auto" data-full-width-responsive="true"';
    case 'auto':
      return 'data-ad-format="auto"';
    case 'horizontal':
      return 'data-ad-format="horizontal"';
    case 'rectangle':
      return 'data-ad-format="rectangle"';
    case 'vertical':
      return 'data-ad-format="vertical"';
    case 'leaderboard':
      return '';
    default:
      return 'data-ad-format="auto"';
  }
};

const AdPlacement: React.FC<AdPlacementProps> = ({
  adClient = 'ca-pub-xxxxxxxxxxxxxxxx', // Replace with your actual AdSense client ID
  adSlot,
  adSize,
  adFormat,
  className = '',
  style = {},
  responsive = true,
  fullWidth = false,
}) => {
  const adRef = useRef<HTMLDivElement>(null);
  const adSizeConfig = getAdSizeConfig(adSize);

  useEffect(() => {
    // Only load ads in production
    if (process.env.NODE_ENV === 'production') {
      try {
        // Check if Google AdSense script is already loaded
        if (typeof window !== 'undefined' && window.adsbygoogle) {
          // Push the ad to Google AdSense
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        } else {
          console.warn('AdSense not available');
        }
      } catch (error) {
        console.error('Error loading AdSense ad:', error);
      }
    }
  }, []);

  // In development, show a placeholder
  if (process.env.NODE_ENV !== 'production') {
    return (
      <div
        className={`ad-placeholder ${className}`}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f0f0f0',
          border: '1px dashed #ccc',
          borderRadius: '8px',
          padding: '20px',
          textAlign: 'center',
          color: '#666',
          fontSize: '14px',
          minHeight: adSize === 'leaderboard' ? '90px' : '250px',
          width: adSize === 'leaderboard' ? '728px' : '100%',
          maxWidth: '100%',
          margin: '0 auto',
          ...style,
        }}
      >
        <div>
          <div className="text-sm font-medium">Ad Placement</div>
          <div className="text-xs mt-1">Size: {adSize}</div>
          <div className="text-xs mt-1">Slot: {adSlot}</div>
        </div>
      </div>
    );
  }

  // In production, render the actual ad
  return (
    <div
      ref={adRef}
      className={`ad-container ${className}`}
      style={{
        display: 'block',
        textAlign: 'center',
        margin: '20px auto',
        overflow: 'hidden',
        ...style,
      }}
    >
      <ins
        className="adsbygoogle"
        style={{
          display: 'block',
          width: fullWidth ? '100%' : undefined,
          height: adSize === 'leaderboard' ? '90px' : '100%',
          minHeight: adSize === 'vertical' ? '600px' : undefined,
        }}
        data-ad-client={adClient}
        data-ad-slot={adSlot}
        data-full-width-responsive={responsive ? 'true' : 'false'}
        {...(adFormat && { 'data-ad-format': adFormat })}
        dangerouslySetInnerHTML={{
          __html: ' ',
        }}
      />
    </div>
  );
};

export default AdPlacement;
