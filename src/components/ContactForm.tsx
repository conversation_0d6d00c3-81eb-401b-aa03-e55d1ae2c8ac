import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import axios from 'axios';

const { TextArea } = Input;

interface ContactFormProps {
  className?: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ className }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      await axios.post('/api/messages', values);
      message.success('Your message has been sent successfully!');
      form.resetFields();
    } catch (error) {
      console.error('Error sending message:', error);
      message.error('Failed to send message. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={className}>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        requiredMark={false}
        className="space-y-6"
      >
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <Form.Item
            name="name"
            label={<span className="text-sm font-medium text-gray-700">Nom</span>}
            rules={[{ required: true, message: 'Veuillez entrer votre nom' }]}
          >
            <Input
              placeholder="Votre nom complet"
              size="large"
              className="rounded-xl border-gray-300 focus:border-primary focus:ring-primary"
            />
          </Form.Item>

          <Form.Item
            name="email"
            label={<span className="text-sm font-medium text-gray-700">Email</span>}
            rules={[
              { required: true, message: 'Veuillez entrer votre email' },
              { type: 'email', message: 'Veuillez entrer un email valide' }
            ]}
          >
            <Input
              placeholder="Votre adresse email"
              size="large"
              className="rounded-xl border-gray-300 focus:border-primary focus:ring-primary"
            />
          </Form.Item>
        </div>

        <Form.Item
          name="subject"
          label={<span className="text-sm font-medium text-gray-700">Sujet</span>}
          rules={[{ required: true, message: 'Veuillez entrer un sujet' }]}
        >
          <Input
            placeholder="Sujet de votre message"
            size="large"
            className="rounded-xl border-gray-300 focus:border-primary focus:ring-primary"
          />
        </Form.Item>

        <Form.Item
          name="content"
          label={<span className="text-sm font-medium text-gray-700">Message</span>}
          rules={[{ required: true, message: 'Veuillez entrer votre message' }]}
        >
          <TextArea
            placeholder="Votre message"
            rows={6}
            size="large"
            className="rounded-xl border-gray-300 focus:border-primary focus:ring-primary"
          />
        </Form.Item>

        <div className="flex items-center">
          <input
            id="privacy-policy"
            name="privacy-policy"
            type="checkbox"
            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            required
          />
          <label htmlFor="privacy-policy" className="ml-2 block text-sm text-gray-600">
            J'accepte la <a href="#" className="text-primary hover:text-primary-dark">politique de confidentialité</a>
          </label>
        </div>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            loading={loading}
            className="w-full bg-primary hover:bg-primary-dark rounded-xl py-3 h-auto shadow-md transition-colors duration-300"
          >
            Envoyer le message
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ContactForm;
