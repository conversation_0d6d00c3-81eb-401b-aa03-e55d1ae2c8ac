import React, { useState, useEffect } from 'react';

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface StandardizedFiltersProps {
  searchQuery: string;
  selectedLevel: string;
  selectedCountry: string;
  selectedStatus: string;
  onSearchChange: (query: string) => void;
  onLevelChange: (level: string) => void;
  onCountryChange: (country: string) => void;
  onStatusChange: (status: string) => void;
  onReset: () => void;
  className?: string;
  showSearch?: boolean;
  showLevel?: boolean;
  showCountry?: boolean;
  showStatus?: boolean;
}

const StandardizedFilters: React.FC<StandardizedFiltersProps> = ({
  searchQuery,
  selectedLevel,
  selectedCountry,
  selectedStatus,
  onSearchChange,
  onLevelChange,
  onCountryChange,
  onStatusChange,
  onReset,
  className = '',
  showSearch = true,
  showLevel = true,
  showCountry = true,
  showStatus = true
}) => {
  const [levels, setLevels] = useState<FilterOption[]>([]);
  const [countries, setCountries] = useState<FilterOption[]>([]);

  // Fetch filter options from API
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        // Fetch levels
        if (showLevel) {
          const levelsResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/scholarships/levels`);
          if (levelsResponse.ok) {
            const levelsData = await levelsResponse.json();
            setLevels(levelsData.data?.map((level: any) => ({
              value: level.name,
              label: level.name,
              count: level.count
            })) || []);
          }
        }

        // Fetch countries
        if (showCountry) {
          const countriesResponse = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/scholarships/countries-sidebar`);
          if (countriesResponse.ok) {
            const countriesData = await countriesResponse.json();
            setCountries(countriesData.data?.map((country: any) => ({
              value: country.country,
              label: country.country,
              count: country.count
            })) || []);
          }
        }
      } catch (error) {
        console.error('Error fetching filter options:', error);
      }
    };

    fetchFilterOptions();
  }, [showLevel, showCountry]);

  // Default level options (fallback)
  const defaultLevels: FilterOption[] = [
    { value: 'Licence', label: 'Licence' },
    { value: 'Master', label: 'Master' },
    { value: 'Doctorat', label: 'Doctorat' },
    { value: 'Post-doctorat', label: 'Post-doctorat' }
  ];

  // Default country options (fallback)
  const defaultCountries: FilterOption[] = [
    { value: 'France', label: 'France' },
    { value: 'Canada', label: 'Canada' },
    { value: 'Belgique', label: 'Belgique' },
    { value: 'Suisse', label: 'Suisse' },
    { value: 'Maroc', label: 'Maroc' },
    { value: 'Tunisie', label: 'Tunisie' },
    { value: 'Sénégal', label: 'Sénégal' },
    { value: 'Côte d\'Ivoire', label: 'Côte d\'Ivoire' }
  ];

  // Status options
  const statusOptions: FilterOption[] = [
    { value: 'open', label: 'Ouvertes' },
    { value: 'closed', label: 'Fermées' },
    { value: 'urgent', label: 'Urgentes' }
  ];

  const levelOptions = levels.length > 0 ? levels : defaultLevels;
  const countryOptions = countries.length > 0 ? countries : defaultCountries;

  return (
    <div className={`bg-white shadow-xl rounded-2xl p-8 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900">Filtrer les Bourses</h2>
        <button
          onClick={onReset}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center transition-colors duration-200"
        >
          <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Réinitialiser les filtres
        </button>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* Search Field */}
        {showSearch && (
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Recherche
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                id="search"
                name="search"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 block w-full rounded-xl border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                placeholder="Rechercher des bourses..."
              />
            </div>
          </div>
        )}

        {/* Level Filter */}
        {showLevel && (
          <div>
            <label htmlFor="level" className="block text-sm font-medium text-gray-700 mb-1">
              Niveau d'Études
            </label>
            <select
              id="level"
              name="level"
              value={selectedLevel}
              onChange={(e) => onLevelChange(e.target.value)}
              className="block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
            >
              <option value="">Tous les niveaux</option>
              {levelOptions.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.label}
                  {level.count !== undefined && ` (${level.count})`}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Country Filter */}
        {showCountry && (
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
              Pays
            </label>
            <select
              id="country"
              name="country"
              value={selectedCountry}
              onChange={(e) => onCountryChange(e.target.value)}
              className="block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
            >
              <option value="">Tous les pays</option>
              {countryOptions.map((country) => (
                <option key={country.value} value={country.value}>
                  {country.label}
                  {country.count !== undefined && ` (${country.count})`}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Status Filter */}
        {showStatus && (
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Statut
            </label>
            <select
              id="status"
              name="status"
              value={selectedStatus}
              onChange={(e) => onStatusChange(e.target.value)}
              className="block w-full rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
            >
              <option value="">Tous les statuts</option>
              {statusOptions.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>
    </div>
  );
};

export default StandardizedFilters;
