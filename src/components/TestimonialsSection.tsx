import React, { useState, useEffect } from 'react';

const TestimonialsSection: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: '<PERSON><PERSON><PERSON><PERSON> en Master, Université de Paris',
      image: '/assets/testimonial-1.jpg',
      fallbackImage: 'https://randomuser.me/api/portraits/women/44.jpg',
      quote: '<PERSON><PERSON><PERSON><PERSON> à MaBourse, j\'ai obtenu une bourse complète pour mon Master en Relations Internationales. Le processus de recherche était simple et les conseils fournis ont été déterminants pour ma candidature.',
      scholarship: '<PERSON><PERSON><PERSON>, <PERSON>',
      rating: 5
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Doctorant en Informatique, ETH Zurich',
      image: '/assets/testimonial-2.jpg',
      fallbackImage: 'https://randomuser.me/api/portraits/men/32.jpg',
      quote: 'J\'ai découvert une bourse de recherche que je n\'aurais jamais trouvée autrement. MaBourse m\'a permis de financer mon doctorat en Suisse et de me concentrer pleinement sur mes recherches.',
      scholarship: 'Bourse d\'Excellence, ETH Zurich',
      rating: 5
    },
    {
      id: 3,
      name: 'Amina Ndiaye',
      role: 'Étudiante en Licence, Université Cheikh Anta Diop',
      image: '/assets/testimonial-3.jpg',
      fallbackImage: 'https://randomuser.me/api/portraits/women/68.jpg',
      quote: 'Les alertes personnalisées de MaBourse m\'ont permis de postuler à temps pour une bourse gouvernementale. Sans cette plateforme, j\'aurais manqué cette opportunité qui a changé ma vie.',
      scholarship: 'Programme de Mobilité Internationale',
      rating: 4
    }
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % testimonials.length);
    }, 8000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <svg
        key={index}
        className={`h-5 w-5 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <section className="py-8 bg-gray-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-8">
          <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-2">
            Témoignages
          </span>
          <h2 className="text-2xl font-bold text-gray-900">
            Ce Que Disent Nos Utilisateurs
          </h2>
          <p className="mt-2 text-base text-gray-600 max-w-3xl mx-auto">
            Découvrez comment MaBourse a aidé des étudiants du monde entier à financer leurs études
          </p>
        </div>

        {/* Testimonials carousel */}
        <div className="relative">
          {/* Testimonial cards */}
          <div className="relative h-[32rem] md:h-96">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
                  index === activeIndex
                    ? 'opacity-100 translate-x-0 z-10'
                    : index === (activeIndex - 1 + testimonials.length) % testimonials.length
                    ? 'opacity-0 -translate-x-full z-0'
                    : 'opacity-0 translate-x-full z-0'
                }`}
              >
                <div className="h-full flex flex-col md:flex-row bg-white rounded-2xl shadow-xl overflow-hidden">
                  {/* Left column - Image (hidden on mobile) */}
                  <div className="relative hidden md:block md:w-1/3 bg-primary">
                    <div className="absolute inset-0 bg-gradient-to-br from-primary to-primary-dark opacity-90"></div>
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="absolute inset-0 h-full w-full object-cover mix-blend-overlay"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = testimonial.fallbackImage;
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center p-8">
                      <div className="relative z-10 text-white text-center">
                        <div className="w-24 h-24 mx-auto mb-4 rounded-full border-4 border-white/30 overflow-hidden">
                          <img
                            src={testimonial.image}
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = testimonial.fallbackImage;
                            }}
                          />
                        </div>
                        <h3 className="text-xl font-bold">{testimonial.name}</h3>
                        <p className="text-white/80 text-sm">{testimonial.role}</p>
                        <div className="mt-4 flex justify-center">
                          {renderStars(testimonial.rating)}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right column - Content */}
                  <div className="md:w-2/3 p-8 md:p-12 flex flex-col">
                    {/* Mobile image */}
                    <div className="flex items-center mb-6 md:hidden">
                      <div className="w-16 h-16 rounded-full overflow-hidden mr-4">
                        <img
                          src={testimonial.image}
                          alt={testimonial.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = testimonial.fallbackImage;
                          }}
                        />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900">{testimonial.name}</h3>
                        <p className="text-gray-600 text-sm">{testimonial.role}</p>
                      </div>
                    </div>

                    {/* Quote */}
                    <div className="mb-8">
                      <svg className="h-10 w-10 text-primary/20 mb-4" fill="currentColor" viewBox="0 0 32 32">
                        <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-***********-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                      </svg>
                      <p className="text-xl text-gray-600 italic leading-relaxed">
                        "{testimonial.quote}"
                      </p>
                    </div>

                    {/* Scholarship info */}
                    <div className="mt-auto">
                      <div className="flex items-center">
                        <svg className="h-5 w-5 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                        </svg>
                        <span className="text-gray-700 font-medium">{testimonial.scholarship}</span>
                      </div>

                      {/* Mobile rating */}
                      <div className="mt-4 flex md:hidden">
                        {renderStars(testimonial.rating)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation dots */}
          <div className="flex justify-center mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 mx-1 rounded-full transition-all duration-300 ${
                  index === activeIndex ? 'bg-primary scale-125' : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
