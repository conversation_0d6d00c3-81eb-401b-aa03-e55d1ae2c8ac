import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

interface Scholarship {
  id: number;
  title: string;
  thumbnail: string;
  slug?: string;
}

interface SimplifiedSidebarProps {
  config: {
    type: 'levels' | 'countries' | 'opportunities';
    currentItem: string;
    limit: number;
  };
  className?: string;
}

const SimplifiedSidebar: React.FC<SimplifiedSidebarProps> = ({ config, className = '' }) => {
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        limit: config.limit.toString(),
        exclude: config.currentItem
      });

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await fetch(`${apiUrl}/api/scholarships/latest?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching sidebar scholarships:', error);
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [config.currentItem, config.limit]);

  const getScholarshipUrl = (scholarship: Scholarship) => {
    if (scholarship.slug) {
      return `/bourse/${scholarship.slug}`;
    }
    return `/scholarships/${scholarship.id}`;
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="h-12 w-16 bg-gray-200 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Dernières Bourses
        </h3>
        <div className="w-12 h-1 bg-blue-600 rounded"></div>
      </div>

      {/* Scholarships List */}
      <div className="space-y-4">
        {scholarships.length > 0 ? (
          scholarships.map((scholarship) => (
            <Link
              key={scholarship.id}
              to={getScholarshipUrl(scholarship)}
              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
            >
              {/* Thumbnail */}
              <div className="flex-shrink-0">
                <img
                  src={scholarship.thumbnail || '/images/default-scholarship.jpg'}
                  alt={scholarship.title}
                  className="h-12 w-16 object-cover rounded"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/default-scholarship.jpg';
                  }}
                />
              </div>
              
              {/* Title */}
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2">
                  {scholarship.title}
                </h4>
              </div>
            </Link>
          ))
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-sm text-gray-500">Aucune bourse disponible</p>
          </div>
        )}
      </div>

      {/* Newsletter Subscription */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-semibold text-blue-900 mb-2">
          Newsletter
        </h4>
        <p className="text-xs text-blue-700 mb-3">
          Recevez les dernières bourses directement dans votre boîte mail.
        </p>
        <div className="flex">
          <input
            type="email"
            placeholder="Votre email"
            className="flex-1 px-3 py-2 text-xs border border-blue-200 rounded-l-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <button className="px-3 py-2 bg-blue-600 text-white text-xs rounded-r-md hover:bg-blue-700 transition-colors duration-200">
            S'abonner
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedSidebar;
