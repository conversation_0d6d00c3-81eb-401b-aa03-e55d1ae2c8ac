import React, { useState, useEffect } from 'react';
import { calculateDaysRemaining } from '../utils/dateFormatter';
import { generateScholarshipSlug } from '../utils/slugify';
import { useLanguage } from '../context/LanguageContext';
import { constructImageUrl, handleImageError, getImagePlaceholder, preloadImageWithRetry, ImageLoadState, reportImageError } from '../utils/imageUtils';

interface EnhancedScholarshipCardProps {
  id: number;
  title: string;
  thumbnail: string;
  deadline: string;
  isOpen: boolean;
  onClick: (id: number, slug?: string) => void;
  level?: string;
  fundingSource?: string;
  country?: string;
  featured?: boolean;
  index?: number;
  variant?: 'default' | 'greatyop'; // Add variant prop to switch between designs
}

const EnhancedScholarshipCard: React.FC<EnhancedScholarshipCardProps> = ({
  id,
  title,
  thumbnail,
  deadline,
  isOpen,
  onClick,
  level,
  fundingSource,
  country,
  featured = false,
  index = 0,
  variant = 'default',
}) => {
  const [imageUrl, setImageUrl] = useState<string>(getImagePlaceholder());
  const [imageState, setImageState] = useState<ImageLoadState>(ImageLoadState.LOADING);

  const { translations, language } = useLanguage();
  const { formattedText, isOpen: isNotExpired, daysRemaining } = calculateDaysRemaining(deadline, language);

  // Use the calculated isOpen status if available, otherwise use the prop
  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;

  // Determine urgency level for visual cues
  const isUrgent = scholarshipStatus && daysRemaining <= 7;

  // Animation delay based on index
  const animationDelay = `${index * 0.1}s`;

  // Generate slug for SEO-friendly URL
  const scholarshipSlug = generateScholarshipSlug(title, id);

  // Load optimized image
  useEffect(() => {
    const loadImage = async () => {
      setImageState(ImageLoadState.LOADING);

      try {
        // Try card-sized thumbnail first with enhanced error handling
        const cardUrl = constructImageUrl(thumbnail, 'card');
        const cardState = await preloadImageWithRetry(cardUrl, 0, (progress) => {
          if (progress.error) {
            reportImageError(cardUrl, progress.error);
          }
        });

        if (cardState === ImageLoadState.LOADED) {
          setImageUrl(cardUrl);
          setImageState(ImageLoadState.LOADED);
          return;
        }

        // Fallback to original image
        const originalUrl = constructImageUrl(thumbnail, 'original');
        const originalState = await preloadImageWithRetry(originalUrl, 0, (progress) => {
          if (progress.error) {
            reportImageError(originalUrl, progress.error);
          }
        });

        if (originalState === ImageLoadState.LOADED) {
          setImageUrl(originalUrl);
          setImageState(ImageLoadState.LOADED);
          return;
        }

        // Final fallback
        setImageUrl(constructImageUrl(null));
        setImageState(ImageLoadState.FALLBACK);
      } catch (error) {
        const errorMessage = `Error loading scholarship image: ${(error as Error).message}`;
        console.error(errorMessage);
        reportImageError(thumbnail || 'unknown', errorMessage);
        setImageUrl(constructImageUrl(null));
        setImageState(ImageLoadState.ERROR);
      }
    };

    loadImage();
  }, [thumbnail]);

  // Render GreatYOP style card
  if (variant === 'greatyop') {
    return (
      <div className="gy-post-card">
        <article className="post type-post status-publish format-standard has-post-thumbnail hentry">
          {/* Image section */}
          <div className="gyp-article-thumb">
            <button
              type="button"
              className="w-full h-full border-0 p-0 bg-transparent cursor-pointer"
              onClick={() => onClick(id, scholarshipSlug)}
            >
              {imageState === ImageLoadState.LOADING && (
                <div className="w-full h-[184px] bg-gray-200 animate-pulse flex items-center justify-center">
                  <div className="text-gray-400 text-sm">Loading...</div>
                </div>
              )}
              <img
                src={imageUrl}
                className={`attachment-thumbnail size-thumbnail wp-post-image ${
                  imageState === ImageLoadState.LOADING ? 'opacity-0' : 'opacity-100'
                }`}
                alt={title}
                decoding="async"
                loading="lazy"
                onLoad={() => setImageState(ImageLoadState.LOADED)}
                onError={(e) => handleImageError(e, constructImageUrl(null))}
              />
            </button>
          </div>

          {/* Header section */}
          <div className="gyp-archive-post-header-wrapper">
            <div className="entry-header">
              <h2 className="entry-title">
                <button
                  type="button"
                  className="text-left w-full border-0 p-0 bg-transparent cursor-pointer text-gray-900 hover:text-blue-600 transition-colors duration-200"
                  onClick={() => onClick(id, scholarshipSlug)}
                >
                  {title}
                </button>
              </h2>
            </div>
          </div>

          {/* Bottom section with status and country with proper spacing */}
          <div className="gy-pcard-bottom relative">
            {/* Date in bottom left with spacing */}
            <div className="absolute bottom-2 left-3 flex items-center">
              <svg className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className={`text-sm ${
                !scholarshipStatus ? 'text-red-600' :
                isUrgent ? 'text-amber-600' : 'text-gray-600'
              }`}>
                {formattedText}
              </span>
            </div>

            {/* Country in bottom right with spacing */}
            {country && (
              <div className="absolute bottom-2 right-3 flex items-center text-gray-600">
                <svg className="h-4 w-4 mr-1 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-sm">{country}</span>
              </div>
            )}
          </div>
        </article>
      </div>
    );
  }

  // Default enhanced card design
  return (
    <div
      className={`group relative bg-white rounded-2xl shadow-lg overflow-hidden w-full transition-all duration-500 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-2 cursor-pointer ${
        featured ? 'border-2 border-primary ring-2 ring-primary/20' : 'border border-gray-100 hover:border-primary/30'
      }`}
      onClick={() => onClick(id, scholarshipSlug)}
      style={{ animationDelay }}
    >
      {/* Featured badge */}
      {featured && (
        <div className="absolute top-0 right-0 z-20">
          <div className="bg-primary text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
            {translations.common.popular}
          </div>
        </div>
      )}

      {/* Thumbnail with overlay */}
      <div className="relative aspect-[16/9] overflow-hidden">
        {imageState === ImageLoadState.LOADING && (
          <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-400 text-sm">Loading...</div>
          </div>
        )}
        <img
          src={imageUrl}
          alt={`${title} - Scholarship thumbnail`}
          className={`w-full h-full object-cover transform transition-all duration-700 group-hover:scale-110 ${
            imageState === ImageLoadState.LOADING ? 'opacity-0' : 'opacity-100'
          }`}
          loading="lazy"
          onLoad={() => setImageState(ImageLoadState.LOADED)}
          onError={(e) => handleImageError(e, constructImageUrl(null))}
        />

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-60 group-hover:opacity-70 transition-opacity duration-300"></div>

        {/* Level badge - moved to bottom right */}
        {level && (
          <div className="absolute bottom-3 right-3 z-10">
            <span className={`px-2.5 py-1 rounded-full text-xs font-medium shadow-sm ${
              level === 'Licence' ? 'bg-primary-100 text-primary-800 border border-primary-200' :
              level === 'Master' ? 'bg-primary-200 text-primary-900 border border-primary-300' :
              'bg-primary-300 text-primary-900 border border-primary-400'
            }`}>
              {level}
            </span>
          </div>
        )}

        {/* Country */}
        {country && (
          <div className="absolute bottom-3 left-3 z-10">
            <span className="px-2.5 py-1 rounded-full text-xs font-medium bg-white/80 text-gray-700 backdrop-blur-sm shadow-sm">
              {country}
            </span>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-5">
        {/* Title */}
        <h3 className="text-lg font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-300">
          {title}
        </h3>

        {/* Deadline */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center">
            <svg className="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className={`text-sm font-medium ${
              !scholarshipStatus ? 'text-red-600' :
              isUrgent ? 'text-amber-600' : 'text-gray-600'
            }`}>
              {formattedText}
            </span>
          </div>

          {/* View details icon */}
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
};

export default EnhancedScholarshipCard;
