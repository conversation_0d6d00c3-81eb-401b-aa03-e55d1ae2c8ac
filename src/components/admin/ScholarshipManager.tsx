import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentArrowUpIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import ScholarshipForm from '../../admin/components/ScholarshipForm';
import Modal from '../../admin/components/Modal';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  country: string;
  level: string;
  deadline: string;
  amount?: string;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financialBenefitsSummary?: string;
  eligibilitySummary?: string;
  scholarshipLink?: string;
  youtubeLink?: string;
  createdAt: string;
  updatedAt: string;
}

const ScholarshipManager: React.FC = () => {
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState('');
  const [filterCountry, setFilterCountry] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedScholarship, setSelectedScholarship] = useState<Scholarship | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchScholarships();
  }, []);

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/scholarships', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const result = await response.json();
      const scholarshipsData = result.data || result.scholarships || [];
      setScholarships(scholarshipsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateScholarship = async (formData: any) => {
    try {
      setIsSubmitting(true);

      // Create FormData for multipart submission (industry standard)
      const submitData = new FormData();

      // Add all form fields
      Object.keys(formData).forEach(key => {
        if (key === 'thumbnail' && formData[key] && formData[key].startsWith('data:image/')) {
          // Handle base64 images - backend will convert to files
          submitData.append(key, formData[key]);
        } else if (formData[key] !== null && formData[key] !== undefined) {
          submitData.append(key, formData[key]);
        }
      });

      const response = await fetch('http://localhost:5000/api/scholarships', {
        method: 'POST',
        body: submitData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to create scholarship');
      }

      await fetchScholarships();
      setShowAddModal(false);
      alert('Scholarship created successfully!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to create scholarship');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateScholarship = async (formData: any) => {
    if (!selectedScholarship) return;

    try {
      setIsSubmitting(true);

      // Create FormData for multipart submission (industry standard)
      const submitData = new FormData();

      // Add all form fields
      Object.keys(formData).forEach(key => {
        if (key === 'thumbnail' && formData[key] && formData[key].startsWith('data:image/')) {
          // Handle base64 images - backend will convert to files
          submitData.append(key, formData[key]);
        } else if (formData[key] !== null && formData[key] !== undefined) {
          submitData.append(key, formData[key]);
        }
      });

      const response = await fetch(`http://localhost:5000/api/scholarships/${selectedScholarship.id}`, {
        method: 'PUT',
        body: submitData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to update scholarship');
      }

      await fetchScholarships();
      setShowEditModal(false);
      setSelectedScholarship(null);
      alert('Scholarship updated successfully!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to update scholarship');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this scholarship?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/scholarships/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to delete scholarship');
      }

      setScholarships(scholarships.filter(s => s.id !== id));
      alert('Scholarship deleted successfully!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to delete scholarship');
    }
  };

  const handleEdit = (scholarship: Scholarship) => {
    setSelectedScholarship(scholarship);
    setShowEditModal(true);
  };

  const handleView = (scholarship: Scholarship) => {
    // Open scholarship detail in new tab
    window.open(`/scholarships/${scholarship.id}`, '_blank');
  };

  const handleCloseModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedScholarship(null);
  };

  const filteredScholarships = scholarships.filter(scholarship => {
    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = !filterLevel || scholarship.level === filterLevel;
    const matchesCountry = !filterCountry || scholarship.country === filterCountry;
    
    return matchesSearch && matchesLevel && matchesCountry;
  });

  const uniqueLevels = [...new Set(scholarships.map(s => s.level))].filter(Boolean);
  const uniqueCountries = [...new Set(scholarships.map(s => s.country))].filter(Boolean);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Scholarship Management</h1>
          <p className="text-gray-600">Manage scholarships, add new ones, and update existing entries</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Add Scholarship</span>
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2">
            <DocumentArrowUpIcon className="h-5 w-5" />
            <span>Bulk Import</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search scholarships..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={filterLevel}
            onChange={(e) => setFilterLevel(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Levels</option>
            {uniqueLevels.map(level => (
              <option key={level} value={level}>{level}</option>
            ))}
          </select>
          <select
            value={filterCountry}
            onChange={(e) => setFilterCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Countries</option>
            {uniqueCountries.map(country => (
              <option key={country} value={country}>{country}</option>
            ))}
          </select>
          <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5" />
            <span>More Filters</span>
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-blue-600">{scholarships.length}</div>
          <div className="text-sm text-gray-600">Total Scholarships</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-green-600">
            {scholarships.filter(s => s.isOpen).length}
          </div>
          <div className="text-sm text-gray-600">Open Applications</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-orange-600">
            {scholarships.filter(s => !s.isOpen).length}
          </div>
          <div className="text-sm text-gray-600">Closed Applications</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-purple-600">{uniqueCountries.length}</div>
          <div className="text-sm text-gray-600">Countries</div>
        </div>
      </div>

      {/* Scholarships Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Scholarship
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Country
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Deadline
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredScholarships.map((scholarship) => (
                <tr key={scholarship.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {scholarship.title}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {scholarship.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {scholarship.country}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {scholarship.level}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(scholarship.deadline).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      scholarship.isOpen
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {scholarship.isOpen ? 'Open' : 'Closed'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(scholarship)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Scholarship"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(scholarship)}
                        className="text-green-600 hover:text-green-900"
                        title="Edit Scholarship"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(scholarship.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Scholarship"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredScholarships.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No scholarships found matching your criteria.</div>
          </div>
        )}
      </div>

      {/* Add Scholarship Modal */}
      {showAddModal && (
        <Modal
          isOpen={showAddModal}
          onClose={handleCloseModals}
          title="Add New Scholarship"
        >
          <ScholarshipForm
            scholarship={null}
            onSubmit={handleCreateScholarship}
            onCancel={handleCloseModals}
          />
          {isSubmitting && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Creating scholarship...</p>
              </div>
            </div>
          )}
        </Modal>
      )}

      {/* Edit Scholarship Modal */}
      {showEditModal && selectedScholarship && (
        <Modal
          isOpen={showEditModal}
          onClose={handleCloseModals}
          title="Edit Scholarship"
        >
          <ScholarshipForm
            scholarship={selectedScholarship}
            onSubmit={handleUpdateScholarship}
            onCancel={handleCloseModals}
          />
          {isSubmitting && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Updating scholarship...</p>
              </div>
            </div>
          )}
        </Modal>
      )}
    </div>
  );
};

export default ScholarshipManager;
