import React, { useState } from 'react';
import { Form, Input, Button, message, Spin } from 'antd';
import { MailOutlined, SendOutlined } from '@ant-design/icons';
import axios from 'axios';

interface NewsletterSubscriptionProps {
  title?: string;
  subtitle?: string;
  buttonText?: string;
  className?: string;
}

const NewsletterSubscription: React.FC<NewsletterSubscriptionProps> = ({
  title = 'Subscribe to Our Newsletter',
  subtitle = 'Stay updated with the latest scholarships and opportunities',
  buttonText = 'Subscribe',
  className = '',
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [subscribed, setSubscribed] = useState(false);

  const handleSubmit = async (values: { email: string }) => {
    try {
      setLoading(true);

      // Call the API to subscribe
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      await axios.post(`${apiUrl}/api/newsletter`, { email: values.email });

      // Show success message
      message.success('Thank you for subscribing to our newsletter!');

      // Reset form
      form.resetFields();

      // Show success state
      setSubscribed(true);

      // Reset success state after 5 seconds
      setTimeout(() => {
        setSubscribed(false);
      }, 5000);
    } catch (error: any) {
      console.error('Error subscribing to newsletter:', error);

      // Handle duplicate email error
      if (error.response && error.response.status === 400 && error.response.data.message === 'Email already subscribed') {
        message.info('This email is already subscribed to our newsletter.');
      } else {
        message.error('Failed to subscribe. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-primary/5 py-12 px-4 sm:px-6 lg:px-8 rounded-lg ${className}`}>
      <div className="max-w-3xl mx-auto text-center">
        <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
          {title}
        </h2>
        <p className="mt-4 text-lg leading-6 text-gray-500">
          {subtitle}
        </p>
        <div className="mt-8">
          <Form
            form={form}
            onFinish={handleSubmit}
            layout="inline"
            className="flex flex-col sm:flex-row justify-center items-center gap-4"
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: 'Please enter your email' },
                { type: 'email', message: 'Please enter a valid email' }
              ]}
              className="w-full sm:w-auto flex-grow max-w-md"
            >
              <Input
                prefix={<MailOutlined className="text-gray-400" />}
                placeholder="Your email address"
                size="large"
                disabled={loading || subscribed}
              />
            </Form.Item>
            <Form.Item className="w-full sm:w-auto">
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                icon={subscribed ? <MailOutlined /> : <SendOutlined />}
                loading={loading}
                className={`w-full ${subscribed ? 'bg-green-600 hover:bg-green-700' : ''}`}
              >
                {loading ? 'Subscribing...' : subscribed ? 'Subscribed!' : buttonText}
              </Button>
            </Form.Item>
          </Form>
          <p className="mt-3 text-sm text-gray-500">
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NewsletterSubscription;
