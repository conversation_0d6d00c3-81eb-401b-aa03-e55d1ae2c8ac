import React from 'react';
import { Link } from 'react-router-dom';
import ScholarshipCard from './ScholarshipCard';
import { Scholarship } from './ScholarshipGrid';
import { GraduationCap } from './icons';

interface StudyLevelSectionProps {
  licenceScholarships: Scholarship[];
  masterScholarships: Scholarship[];
  doctoratScholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const StudyLevelSection: React.FC<StudyLevelSectionProps> = ({
  licenceScholarships,
  masterScholarships,
  doctoratScholarships,
  loading,
  onScholarshipClick
}) => {
  return (
    <section className="py-10 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900">
            Par Niveau d'Étude
          </h2>
        </div>

        {/* Study levels */}
        <div className="space-y-10">
          {/* Licence Section */}
          <div>
            <div className="flex items-center mb-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mr-3">
                <GraduationCap className="w-4 h-4 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Bourses de Licence</h3>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-md animate-pulse">
                    <div className="h-40 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {licenceScholarships.slice(0, 3).map((scholarship) => (
                  <ScholarshipCard
                    key={scholarship.id}
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                ))}
              </div>
            )}

            <div className="mt-4 text-right">
              <Link
                to="/scholarships?level=Licence"
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
              >
                Voir toutes les bourses de Licence
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Master Section */}
          <div>
            <div className="flex items-center mb-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mr-3">
                <GraduationCap className="w-4 h-4 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Bourses de Master</h3>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-md animate-pulse">
                    <div className="h-40 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {masterScholarships.slice(0, 3).map((scholarship) => (
                  <ScholarshipCard
                    key={scholarship.id}
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                ))}
              </div>
            )}

            <div className="mt-4 text-right">
              <Link
                to="/scholarships?level=Master"
                className="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-800"
              >
                Voir toutes les bourses de Master
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Doctorat Section */}
          <div>
            <div className="flex items-center mb-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 mr-3">
                <GraduationCap className="w-4 h-4 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Bourses de Doctorat</h3>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-md animate-pulse">
                    <div className="h-40 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {doctoratScholarships.slice(0, 3).map((scholarship) => (
                  <ScholarshipCard
                    key={scholarship.id}
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                ))}
              </div>
            )}

            <div className="mt-4 text-right">
              <Link
                to="/scholarships?level=Doctorat"
                className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800"
              >
                Voir toutes les bourses de Doctorat
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StudyLevelSection;
