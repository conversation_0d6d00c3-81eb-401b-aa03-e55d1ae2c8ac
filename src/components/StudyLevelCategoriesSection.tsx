import React from 'react';
import { Link } from 'react-router-dom';
import { GraduationCap } from './icons/index';

const StudyLevelCategoriesSection: React.FC = () => {
  const categories = [
    {
      id: 'licence',
      title: 'Licence',
      description: 'Bourses pour les étudiants de premier cycle (Bac+3)',
      color: 'blue',
      emoji: '🟣',
      icon: <GraduationCap className="w-6 h-6 text-blue-600" />,
      link: '/scholarships?level=Licence'
    },
    {
      id: 'master',
      title: 'Master',
      description: 'Bourses pour les étudiants de deuxième cycle (Bac+5)',
      color: 'purple',
      emoji: '🔵',
      icon: <GraduationCap className="w-6 h-6 text-purple-600" />,
      link: '/scholarships?level=Master'
    },
    {
      id: 'doctorat',
      title: 'Doctorat',
      description: 'Bourses pour les étudiants de troisième cycle (Bac+8)',
      color: 'indigo',
      emoji: '🟠',
      icon: <GraduationCap className="w-6 h-6 text-indigo-600" />,
      link: '/scholarships?level=Doctorat'
    }
  ];

  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            🎓 Bourses par Niveau d'Études
          </h2>
          <p className="mt-2 text-gray-600">
            Trouvez des bourses adaptées à votre parcours académique
          </p>
        </div>

        {/* Categories grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Link
              key={category.id}
              to={category.link}
              className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden p-6 transition-all duration-300 hover:shadow-md hover:-translate-y-1 flex flex-col`}
            >
              <div className="flex items-center mb-4">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full bg-${category.color}-100 mr-4`}>
                  {category.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 flex items-center">
                    {category.emoji} {category.title}
                  </h3>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4">
                {category.description}
              </p>
              
              <div className="mt-auto flex items-center text-primary font-medium">
                Voir les bourses
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StudyLevelCategoriesSection;
