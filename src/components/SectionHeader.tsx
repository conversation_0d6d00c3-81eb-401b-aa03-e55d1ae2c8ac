import React from 'react';

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  className?: string;
  alignment?: 'left' | 'center' | 'right';
}

/**
 * Reusable section header component with title, subtitle and optional icon
 */
const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  icon,
  className = '',
  alignment = 'center',
}) => {
  const alignmentClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  return (
    <div className={`mb-8 ${alignmentClasses[alignment]} ${className}`}>
      {icon && <div className="mb-2 text-primary">{icon}</div>}
      <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
        {title}
      </h2>
      {subtitle && (
        <p className="mt-3 max-w-2xl mx-auto text-lg text-gray-600 sm:mt-4">
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default SectionHeader;
