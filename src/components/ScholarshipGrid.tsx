import React from 'react';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';

export interface Scholarship {
  id: number;
  title: string;
  thumbnail: string;
  deadline: string; // Changed from dateline to deadline for consistency
  isOpen: boolean;
  level?: string;
  fundingSource?: string;
  country?: string;
}

interface ScholarshipGridProps {
  scholarships: Scholarship[];
  onCardClick: (id: number) => void;
  columns?: number;
  rows?: number;
  className?: string;
}

const ScholarshipGrid: React.FC<ScholarshipGridProps> = ({
  scholarships,
  onCardClick,
  columns = 3,
  rows,
  className = '',
}) => {
  // Limit the number of scholarships based on rows and columns if specified
  const limitedScholarships = rows
    ? scholarships.slice(0, rows * columns)
    : scholarships;

  // Determine grid columns based on the columns prop
  const gridColsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  const gridCols = gridColsClasses[columns as keyof typeof gridColsClasses] || gridColsClasses[3];

  return (
    <div className="gy-pcard-wrap">
      {limitedScholarships.map((scholarship, index) => (
        <EnhancedScholarshipCard
          key={scholarship.id}
          id={scholarship.id}
          title={scholarship.title}
          thumbnail={scholarship.thumbnail}
          deadline={scholarship.deadline}
          isOpen={scholarship.isOpen}
          level={scholarship.level}
          country={scholarship.country}
          onClick={(id) => onCardClick(id)}
          index={index}
          variant="greatyop"
        />
      ))}
    </div>
  );
};

export default ScholarshipGrid;