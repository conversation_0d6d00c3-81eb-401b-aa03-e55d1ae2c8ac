import React, { useRef } from 'react';
import { Link } from 'react-router-dom';
import ScholarshipCard from './ScholarshipCard';
import { Scholarship } from './ScholarshipGrid';

interface GovernmentScholarshipsSectionProps {
  scholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const GovernmentScholarshipsSection: React.FC<GovernmentScholarshipsSectionProps> = ({
  scholarships,
  loading,
  onScholarshipClick
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: 'smooth' });
    }
  };

  return (
    <section className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header with navigation buttons */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
              🌍 Bourses Gouvernementales
            </h2>
            <p className="mt-2 text-gray-600">
              Opportunités financées par des gouvernements du monde entier
            </p>
          </div>

          {/* Navigation buttons for carousel */}
          <div className="hidden md:flex space-x-2">
            <button
              onClick={scrollLeft}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-300"
              aria-label="Scroll left"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              onClick={scrollRight}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-300"
              aria-label="Scroll right"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        {/* Scholarship carousel with loading state */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
                <div className="aspect-[16/9] bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                  <div className="flex justify-between mt-4">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            {/* Desktop carousel */}
            <div
              ref={scrollContainerRef}
              className="hidden md:flex overflow-x-auto pb-4 space-x-6 scrollbar-hide"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {scholarships.slice(0, 6).map((scholarship) => (
                <div key={scholarship.id} className="flex-none w-[350px]">
                  <ScholarshipCard
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    country={scholarship.country}
                    onClick={onScholarshipClick}
                  />
                </div>
              ))}
            </div>

            {/* Mobile grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:hidden gap-6">
              {scholarships.slice(0, 4).map((scholarship) => (
                <ScholarshipCard
                  key={scholarship.id}
                  id={scholarship.id}
                  title={scholarship.title}
                  thumbnail={scholarship.thumbnail}
                  deadline={scholarship.deadline}
                  isOpen={scholarship.isOpen}
                  country={scholarship.country}
                  onClick={onScholarshipClick}
                />
              ))}
            </div>
          </>
        )}

        {/* Call to action */}
        <div className="flex justify-center mt-8">
          <Link
            to="/scholarships?source=Gouvernement"
            className="inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary-dark transition-colors duration-300"
          >
            Voir toutes les bourses gouvernementales
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default GovernmentScholarshipsSection;
