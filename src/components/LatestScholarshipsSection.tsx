import React, { useRef } from 'react';
import { Link } from 'react-router-dom';
import ScholarshipCard from './ScholarshipCard';
import { Scholarship } from './ScholarshipGrid';

interface LatestScholarshipsSectionProps {
  scholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const LatestScholarshipsSection: React.FC<LatestScholarshipsSectionProps> = ({
  scholarships,
  loading,
  onScholarshipClick
}) => {
  const sectionRef = useRef<HTMLElement>(null);

  return (
    <section ref={sectionRef} id="latest-scholarships" className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            🎓 Dernières Bourses Publiées
          </h2>
          <p className="mt-2 text-gray-600">
            Découvrez nos opportunités de bourses les plus récentes
          </p>
        </div>

        {/* Scholarship grid with loading state */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
                <div className="aspect-[16/9] bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                  <div className="flex justify-between mt-4">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {scholarships.slice(0, 6).map((scholarship) => (
              <ScholarshipCard
                key={scholarship.id}
                id={scholarship.id}
                title={scholarship.title}
                thumbnail={scholarship.thumbnail}
                deadline={scholarship.deadline}
                isOpen={scholarship.isOpen}
                level={scholarship.level}
                country={scholarship.country}
                onClick={onScholarshipClick}
              />
            ))}
          </div>
        )}

        {/* Call to action */}
        <div className="flex justify-center mt-10">
          <Link
            to="/scholarships"
            className="inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary-dark transition-colors duration-300"
          >
            Voir plus de bourses
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default LatestScholarshipsSection;
