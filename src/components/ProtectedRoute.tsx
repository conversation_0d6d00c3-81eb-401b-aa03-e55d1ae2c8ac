import React from 'react';
import { Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireMainAdmin?: boolean;
}

/**
 * Protected route component for admin authentication
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireMainAdmin = false 
}) => {
  const { isAuthenticated, isLoading, admin } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" tip="Verifying authentication..." />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/admin/login" replace />;
  }

  // Check main admin requirement
  if (requireMainAdmin && !admin?.isMainAdmin) {
    return <Navigate to="/admin/dashboard" replace />;
  }

  // Render protected content
  return <>{children}</>;
};

export default ProtectedRoute;
