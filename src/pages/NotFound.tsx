import React from 'react';
import { Link } from 'react-router-dom';

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center px-4 py-16 sm:px-6 sm:py-24 lg:px-8">
      <div className="max-w-3xl mx-auto text-center">
        <div className="relative">
          {/* Background decorative elements */}
          <div className="absolute -top-10 -left-10 w-40 h-40 bg-primary/5 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
          <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-secondary/5 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>

          {/* 404 Text */}
          <h1 className="text-9xl font-extrabold text-primary tracking-tight animate-pulse">
            404
          </h1>
        </div>

        <div className="mt-6">
          <h2 className="text-3xl font-bold text-gray-900 tracking-tight sm:text-4xl">
            Page non trouvée
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-md mx-auto">
            Désolé, nous n'avons pas pu trouver la page que vous recherchez. Veuillez vérifier l'URL dans la barre d'adresse et réessayer.
          </p>
        </div>

        <div className="mt-10 flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center">
          <Link
            to="/"
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-xl shadow-md text-white bg-primary hover:bg-primary-dark transition-colors duration-300"
          >
            <svg className="mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            Retour à l'accueil
          </Link>
          <Link
            to="/contact"
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-300"
          >
            <svg className="mr-2 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            Contacter le support
          </Link>
        </div>

        {/* Suggested links */}
        <div className="mt-12 border-t border-gray-200 pt-8">
          <h3 className="text-lg font-medium text-gray-900">Vous pourriez être intéressé par</h3>
          <ul className="mt-4 flex flex-wrap justify-center gap-4">
            <li>
              <Link to="/scholarships" className="text-primary hover:text-primary-dark font-medium">
                Toutes les bourses
              </Link>
            </li>
            <li>
              <Link to="/about" className="text-primary hover:text-primary-dark font-medium">
                À propos de nous
              </Link>
            </li>
            <li>
              <Link to="/contact" className="text-primary hover:text-primary-dark font-medium">
                Nous contacter
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default NotFound;