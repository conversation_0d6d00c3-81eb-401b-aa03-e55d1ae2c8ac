import React from 'react';
import { useParams } from 'react-router-dom';
import StandardCountryPage from '../components/StandardCountryPage';

// Helper function to get country flag
const getCountryFlag = (countryName: string): string => {
  const flagMap: { [key: string]: string } = {
    'France': '🇫🇷',
    'Canada': '🇨🇦',
    'États-Unis': '🇺🇸',
    'Allemagne': '🇩🇪',
    'Royaume-Uni': '🇬🇧',
    'Australie': '🇦🇺',
    'Suisse': '🇨🇭',
    'Belgique': '🇧🇪',
    'Pays-Bas': '🇳🇱',
    'Suède': '🇸🇪',
    'Norvège': '🇳🇴',
    'Danemark': '🇩🇰',
    'Finlande': '🇫🇮',
    'Japon': '🇯🇵',
    'Corée du Sud': '🇰🇷',
    'Singapour': '🇸🇬',
    'Nouvelle-Zélande': '🇳🇿',
    'Italie': '🇮🇹',
    'Espagne': '🇪🇸',
    'Portugal': '🇵🇹'
  };
  return flagMap[countryName] || '🌍';
};

const CountryDetail: React.FC = () => {
  const { country } = useParams<{ country: string }>();
  const decodedCountry = country ? decodeURIComponent(country) : '';

  const pageConfig = {
    country: decodedCountry,
    title: `Bourses d'Études en ${decodedCountry} | Opportunités de Financement`,
    description: `Découvrez toutes les bourses d'études disponibles en ${decodedCountry}. Financez vos études supérieures avec des opportunités de bourses prestigieuses.`,
    keywords: `bourses ${decodedCountry}, financement études ${decodedCountry}, études supérieures ${decodedCountry}`,
    heroTitle: 'Bourses d\'Études en',
    heroSubtitle: `Découvrez toutes les opportunités de bourses d'études disponibles en ${decodedCountry} et financez votre avenir académique.`,
    infoTitle: `Pourquoi Étudier en ${decodedCountry} ?`,
    infoContent: `${decodedCountry} offre un système éducatif de qualité mondiale avec des universités prestigieuses et des opportunités de recherche exceptionnelles. Avec une bourse d'études, vous pouvez accéder à cette excellence académique sans contraintes financières.`,
    benefits: [
      'Universités de renommée mondiale',
      'Programmes académiques d\'excellence',
      'Environnement multiculturel enrichissant',
      'Opportunités de recherche avancée',
      'Réseau professionnel international',
      'Expérience culturelle unique'
    ],
    apiEndpoint: '/api/scholarships/search',
    flag: getCountryFlag(decodedCountry)
  };

  return <StandardCountryPage config={pageConfig} />;
};

export default CountryDetail;
