import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

interface TypeStatistics {
  type: string;
  totalOpportunities: number;
  activeOpportunities: number;
  inactiveOpportunities: number;
}

const OpportunitiesByType: React.FC = () => {
  const { type } = useParams<{ type: string }>();
  const { translations } = useLanguage();
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [allTypes, setAllTypes] = useState<string[]>([]);
  const [latestOpportunities, setLatestOpportunities] = useState<Opportunity[]>([]);
  const [statistics, setStatistics] = useState<TypeStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const decodedType = type ? decodeURIComponent(type) : '';

  useEffect(() => {
    if (decodedType) {
      fetchOpportunitiesByType();
      fetchAllTypes();
      fetchLatestOpportunities();
      fetchTypeStatistics();
    }
  }, [decodedType, currentPage]);

  const fetchOpportunitiesByType = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/opportunities/type/${encodeURIComponent(decodedType)}?page=${currentPage}&limit=12`);
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
      }
    } catch (error) {
      console.error('Error fetching opportunities by type:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllTypes = async () => {
    try {
      const response = await fetch('/api/opportunities/types');
      if (response.ok) {
        const data = await response.json();
        setAllTypes(data.data?.map((item: any) => item.name) || []);
      }
    } catch (error) {
      console.error('Error fetching types:', error);
    }
  };

  const fetchLatestOpportunities = async () => {
    try {
      const response = await fetch('/api/opportunities?limit=6&orderBy=created_at&orderDirection=DESC');
      if (response.ok) {
        const data = await response.json();
        setLatestOpportunities(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching latest opportunities:', error);
    }
  };

  const fetchTypeStatistics = async () => {
    try {
      const response = await fetch(`/api/opportunities/types/statistics?type=${encodeURIComponent(decodedType)}`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Error fetching type statistics:', error);
    }
  };

  const getTypeIcon = (typeName: string): string => {
    const icons: { [key: string]: string } = {
      'internship': '🎓',
      'training': '📚',
      'conference': '🎤',
      'workshop': '🔧',
      'competition': '🏆'
    };
    return icons[typeName.toLowerCase()] || '📋';
  };

  const getTypeColor = (typeName: string): string => {
    const colors: { [key: string]: string } = {
      'internship': 'blue',
      'training': 'green',
      'conference': 'purple',
      'workshop': 'orange',
      'competition': 'red'
    };
    return colors[typeName.toLowerCase()] || 'gray';
  };

  const getTypeLabel = (typeName: string): string => {
    const labels: { [key: string]: string } = {
      'internship': 'Stages',
      'training': 'Formations',
      'conference': 'Conférences',
      'workshop': 'Ateliers',
      'competition': 'Concours'
    };
    return labels[typeName.toLowerCase()] || typeName;
  };

  const isExpired = (deadline: string): boolean => {
    return new Date(deadline) < new Date();
  };

  const getDaysUntilDeadline = (deadline: string): number => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className={`bg-gradient-to-r from-${getTypeColor(decodedType)}-600 to-${getTypeColor(decodedType)}-700 text-white py-16`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-6">
            <Link
              to="/opportunities"
              className={`flex items-center text-${getTypeColor(decodedType)}-200 hover:text-white transition-colors duration-200`}
            >
              <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Retour aux opportunités
            </Link>
          </div>
          
          <div className="flex items-center mb-4">
            <div className="text-6xl mr-4">
              {getTypeIcon(decodedType)}
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-2">
                {getTypeLabel(decodedType)}
              </h1>
              <p className={`text-xl text-${getTypeColor(decodedType)}-100`}>
                Découvrez toutes les opportunités de ce type
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      {statistics && (
        <div className="bg-white py-12 border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className={`text-3xl font-bold text-${getTypeColor(decodedType)}-600 mb-2`}>
                  {statistics.totalOpportunities}
                </div>
                <div className="text-gray-600">Total des opportunités</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {statistics.activeOpportunities}
                </div>
                <div className="text-gray-600">Opportunités actives</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600 mb-2">
                  {statistics.inactiveOpportunities}
                </div>
                <div className="text-gray-600">Opportunités fermées</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content Area */}
          <div className="lg:w-2/3">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {getTypeLabel(decodedType)} ({opportunities.length})
              </h2>
            </div>

            {/* Opportunities Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                    <div className="aspect-[16/9] bg-gray-200"></div>
                    <div className="p-6">
                      <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                      <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : opportunities.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">{getTypeIcon(decodedType)}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Aucune opportunité trouvée
                </h3>
                <p className="text-gray-600">
                  Aucune opportunité n'est disponible pour ce type actuellement.
                </p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {opportunities.map((opportunity) => {
                    const expired = isExpired(opportunity.deadline);
                    const daysLeft = getDaysUntilDeadline(opportunity.deadline);
                    
                    return (
                      <div
                        key={opportunity.id}
                        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
                      >
                        {opportunity.thumbnail && (
                          <div className="aspect-[16/9] bg-gray-200">
                            <img
                              src={opportunity.thumbnail}
                              alt={opportunity.title}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = '/assets/default-opportunity.jpg';
                              }}
                            />
                          </div>
                        )}
                        
                        <div className="p-6">
                          <div className="flex items-center justify-between mb-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getTypeColor(decodedType)}-100 text-${getTypeColor(decodedType)}-800`}>
                              {getTypeIcon(decodedType)} {getTypeLabel(decodedType)}
                            </span>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              expired ? 'bg-red-100 text-red-800' : 
                              daysLeft <= 7 ? 'bg-yellow-100 text-yellow-800' : 
                              'bg-green-100 text-green-800'
                            }`}>
                              {expired ? 'Expiré' : daysLeft <= 0 ? 'Aujourd\'hui' : `${daysLeft} jours`}
                            </span>
                          </div>
                          
                          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                            {opportunity.title}
                          </h3>
                          
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {opportunity.description}
                          </p>
                          
                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm text-gray-600">
                              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                              </svg>
                              {opportunity.organization}
                            </div>
                            
                            <div className="flex items-center text-sm text-gray-600">
                              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                              </svg>
                              {opportunity.isRemote ? 'À distance' : opportunity.location}
                            </div>
                            
                            <div className="flex items-center text-sm text-gray-600">
                              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              Date limite: {formatDate(opportunity.deadline)}
                            </div>
                          </div>
                          
                          {opportunity.applicationLink && (
                            <a
                              href={opportunity.applicationLink}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-${getTypeColor(decodedType)}-600 hover:bg-${getTypeColor(decodedType)}-700 transition-colors duration-200`}
                            >
                              Postuler maintenant
                              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                            </a>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-12">
                    <div className="flex space-x-2">
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                            currentPage === index + 1
                              ? `bg-${getTypeColor(decodedType)}-600 text-white`
                              : 'bg-white text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/3">
            {/* Other Types */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Autres Types d'Opportunités
              </h3>
              <div className="space-y-2">
                {allTypes.filter(t => t !== decodedType).map((typeName) => (
                  <Link
                    key={typeName}
                    to={`/opportunities/type/${encodeURIComponent(typeName)}`}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <span className="text-2xl mr-3">{getTypeIcon(typeName)}</span>
                    <span className="font-medium text-gray-900">{getTypeLabel(typeName)}</span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Latest Opportunities */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Dernières Opportunités
              </h3>
              <div className="space-y-4">
                {latestOpportunities.slice(0, 5).map((opportunity) => (
                  <div
                    key={opportunity.id}
                    className="cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <h4 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
                      {opportunity.title}
                    </h4>
                    <p className="text-xs text-gray-600 mb-2">
                      {opportunity.organization} • {getTypeLabel(opportunity.type)}
                    </p>
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      opportunity.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {opportunity.isActive ? 'Actif' : 'Fermé'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpportunitiesByType;
