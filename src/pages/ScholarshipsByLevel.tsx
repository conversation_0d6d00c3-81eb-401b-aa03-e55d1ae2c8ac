import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import ScholarshipCard from '../components/ScholarshipCard';
import ProfessionalPageLayout, { ProfessionalContentGrid } from '../components/ProfessionalPageLayout';
import sidebarService from '../services/sidebarService';

interface Scholarship {
  id: number;
  title: string;
  thumbnail?: string;
  deadline: string;
  isOpen: boolean;
  country: string;
  level: string;
  description?: string;
  financialBenefitsSummary?: string;
  eligibilitySummary?: string;
}

interface LevelStatistics {
  level: string;
  totalScholarships: number;
  openScholarships: number;
  closedScholarships: number;
}

const ScholarshipsByLevel: React.FC = () => {
  const { level } = useParams<{ level: string }>();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [statistics, setStatistics] = useState<LevelStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const decodedLevel = level ? decodeURIComponent(level) : '';

  const fetchScholarshipsByLevel = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/scholarships?level=${encodeURIComponent(decodedLevel)}&page=${currentPage}&limit=12`);
      if (response.ok) {
        const data = await response.json();
        setScholarships(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
      }
    } catch (error) {
      console.error('Error fetching scholarships by level:', error);
    } finally {
      setLoading(false);
    }
  }, [decodedLevel, currentPage]);

  const fetchLevelStatistics = React.useCallback(async () => {
    try {
      // Calculate statistics from the scholarships data
      const totalScholarships = scholarships.length;
      const openScholarships = scholarships.filter(s => s.isOpen).length;
      const closedScholarships = totalScholarships - openScholarships;

      setStatistics({
        level: decodedLevel,
        totalScholarships,
        openScholarships,
        closedScholarships
      });
    } catch (error) {
      console.error('Error calculating level statistics:', error);
    }
  }, [decodedLevel, scholarships]);

  useEffect(() => {
    if (decodedLevel) {
      fetchScholarshipsByLevel();
    }
  }, [decodedLevel, fetchScholarshipsByLevel]);

  useEffect(() => {
    if (scholarships.length > 0) {
      fetchLevelStatistics();
    }
  }, [scholarships, fetchLevelStatistics]);



  const handleScholarshipClick = (id: number) => {
    window.location.href = `/scholarships/${id}`;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderScholarshipItem = (scholarship: Scholarship) => (
    <ScholarshipCard
      key={scholarship.id}
      id={scholarship.id}
      title={scholarship.title}
      thumbnail={scholarship.thumbnail || ''}
      deadline={scholarship.deadline}
      isOpen={scholarship.isOpen}
      country={scholarship.country}
      onClick={handleScholarshipClick}
    />
  );
  return (
    <ProfessionalPageLayout
      hero={{
        title: `Bourses de ${decodedLevel}`,
        subtitle: "Découvrez toutes les opportunités pour ce niveau d'études",
        icon: sidebarService.getLevelIcon(decodedLevel),
        backgroundColor: 'bg-gradient-to-r from-green-600 to-green-800'
      }}
      statistics={statistics ? {
        total: statistics.totalScholarships,
        active: statistics.openScholarships,
        inactive: statistics.closedScholarships,
        label: 'Total des bourses',
        activeLabel: 'Bourses ouvertes',
        inactiveLabel: 'Bourses fermées'
      } : undefined}
      sidebarConfig={{
        type: 'levels',
        currentItem: decodedLevel,
        limit: 10
      }}
    >
      <ProfessionalContentGrid
        items={scholarships}
        loading={loading}
        emptyMessage="Aucune bourse n'est actuellement disponible pour ce niveau d'études."
        emptyIcon="🎓"
        renderItem={renderScholarshipItem}
        pagination={{
          currentPage,
          totalPages,
          onPageChange: handlePageChange
        }}
      />
    </ProfessionalPageLayout>
  );
};

export default ScholarshipsByLevel;
