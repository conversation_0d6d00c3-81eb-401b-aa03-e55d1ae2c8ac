import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

interface Guide {
  id: number;
  title: string;
  excerpt?: string;
  category: 'application' | 'documents' | 'preparation' | 'tips';
  slug: string;
  thumbnail?: string;
  readTime?: number;
  tags?: string[];
  createdAt: string;
}

const Guides: React.FC = () => {
  const { translations } = useLanguage();
  const [guides, setGuides] = useState<Guide[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchGuides();
  }, [selectedCategory]);

  const fetchGuides = async () => {
    try {
      const params = new URLSearchParams({
        published: 'true',
        orderBy: 'created_at',
        orderDirection: 'DESC'
      });

      if (selectedCategory) {
        params.append('category', selectedCategory);
      }

      const response = await fetch(`/api/guides?${params}`);
      if (response.ok) {
        const data = await response.json();
        setGuides(data.data.guides || []);
      } else {
        console.error('Failed to fetch guides');
      }
    } catch (error) {
      console.error('Error fetching guides:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchTerm.trim() === '') {
      fetchGuides();
      return;
    }

    try {
      const params = new URLSearchParams({
        q: searchTerm,
        category: selectedCategory
      });

      const response = await fetch(`/api/guides/search?${params}`);
      if (response.ok) {
        const data = await response.json();
        setGuides(data.data.guides || []);
      }
    } catch (error) {
      console.error('Error searching guides:', error);
    }
  };

  const getCategoryIcon = (category: string): string => {
    const icons = {
      application: '📝',
      documents: '📄',
      preparation: '🎯',
      tips: '💡'
    };
    return icons[category as keyof typeof icons] || '📚';
  };

  const getCategoryColor = (category: string): string => {
    const colors = {
      application: 'bg-blue-100 text-blue-800',
      documents: 'bg-green-100 text-green-800',
      preparation: 'bg-purple-100 text-purple-800',
      tips: 'bg-yellow-100 text-yellow-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des guides...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-gray-900 via-primary-dark to-primary text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {translations.guides.title}
            </h1>
            <p className="text-xl text-primary-100 max-w-3xl mx-auto">
              {translations.guides.subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Rechercher dans les guides..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Toutes les catégories</option>
                <option value="application">{translations.guides.categories.application}</option>
                <option value="documents">{translations.guides.categories.documents}</option>
                <option value="preparation">{translations.guides.categories.preparation}</option>
                <option value="tips">{translations.guides.categories.tips}</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 flex justify-center">
            <button
              onClick={handleSearch}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              Rechercher
            </button>
          </div>
        </div>
      </div>

      {/* Category Quick Links */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(translations.guides.categories).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setSelectedCategory(selectedCategory === key ? '' : key)}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                selectedCategory === key
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              <div className="text-2xl mb-2">{getCategoryIcon(key)}</div>
              <div className="font-medium">{label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Guides Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {guides.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Aucun guide trouvé
            </h3>
            <p className="text-gray-600">
              Essayez de modifier votre recherche ou parcourez toutes les catégories.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {guides.map((guide) => (
              <Link
                key={guide.id}
                to={`/guides/${guide.slug}`}
                className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200"
              >
                {guide.thumbnail && (
                  <div className="aspect-w-16 aspect-h-9 overflow-hidden">
                    <img
                      src={guide.thumbnail}
                      alt={guide.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                )}
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(guide.category)}`}>
                      <span className="mr-1">{getCategoryIcon(guide.category)}</span>
                      {translations.guides.categories[guide.category]}
                    </span>
                    
                    {guide.readTime && (
                      <span className="text-sm text-gray-500">
                        {guide.readTime} min
                      </span>
                    )}
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2 line-clamp-2">
                    {guide.title}
                  </h3>
                  
                  {guide.excerpt && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {guide.excerpt}
                    </p>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {formatDate(guide.createdAt)}
                    </span>
                    
                    <div className="flex items-center text-blue-600 text-sm font-medium">
                      <span>{translations.guides.readMore}</span>
                      <svg className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                  
                  {guide.tags && guide.tags.length > 0 && (
                    <div className="mt-4 flex flex-wrap gap-1">
                      {guide.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Guides;
