import React from 'react';
import { Card, Typography, Alert } from 'antd';
import { LockOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const AccountRecovery: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card className="shadow-lg">
          <div className="text-center mb-6">
            <LockOutlined className="text-4xl text-blue-500 mb-4" />
            <Title level={2}>Account Recovery</Title>
          </div>
          
          <Alert
            message="Feature Under Development"
            description="Account recovery functionality is being updated to work with the new secure authentication system. Please contact your system administrator for account recovery assistance."
            type="info"
            showIcon
          />
        </Card>
      </div>
    </div>
  );
};

export default AccountRecovery;
