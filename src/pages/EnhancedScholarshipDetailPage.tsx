import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { <PERSON><PERSON>, Tag, <PERSON>ton, Spin, Badge } from 'antd';
import { extractIdFromSlug } from '../utils/slugify';
import dateUtils, { DateFormat } from '../utils/dateUtils';
import { getEnv } from '../utils/envValidator';
import {
  CalendarOutlined,
  GlobalOutlined,
  BankOutlined,
  BookOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  FileTextOutlined,
  LinkOutlined,
  TrophyOutlined,
  UserOutlined,
  YoutubeOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';



// Define the Scholarship interface based on the Prisma schema
interface Scholarship {
  id: number;
  title: string;
  description: string;
  level?: string;
  country?: string;
  deadline: string;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financial_benefits_summary?: string;
  eligibility_summary?: string;
  scholarship_link?: string;
  youtube_link?: string;
  createdAt: string;
  updatedAt: string;

  // Additional fields from the backend
  isExpired?: boolean;
  daysRemaining?: number;
  formattedDeadline?: string;

  // Additional fields that might not be in the database but we'll parse from existing fields
  financial_benefits_list?: string[];
  eligibility_criteria_list?: string[];
  study_fields?: string[];
  universities?: string[];
  required_documents?: { name: string; description: string }[];
  deadline_description?: string;
}

// Loading skeleton component
const LoadingSkeleton: React.FC = () => (
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-pulse">
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="h-96 bg-gray-200 rounded-xl"></div>
      <div className="lg:col-span-2 space-y-6">
        <div className="h-8 bg-gray-200 rounded w-1/3"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>

        <div className="h-8 bg-gray-200 rounded w-1/3 mt-8"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
      </div>
    </div>
  </div>
);

const EnhancedScholarshipDetailPage: React.FC = () => {
  const { id, slug } = useParams<{ id?: string; slug?: string }>();
  const navigate = useNavigate();
  const [scholarship, setScholarship] = useState<Scholarship | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchScholarship = async () => {
      setLoading(true);
      setError(null);

      try {
        let scholarshipId: string | null = null;

        // If we have an ID, use it directly
        if (id) {
          scholarshipId = id;
        }
        // If we have a slug, extract the ID from it
        else if (slug) {
          const extractedId = extractIdFromSlug(slug);
          if (extractedId) {
            scholarshipId = extractedId.toString();
          } else {
            throw new Error('Invalid slug format');
          }
        }

        if (scholarshipId) {
          // Log the request for debugging
          console.log(`Fetching scholarship with ID ${scholarshipId}`);

          try {
            // Log the URL we're fetching from
            console.log(`Fetching from URL: /api/scholarships/${scholarshipId}`);

            // Make the API request using axios directly to avoid any interceptors
            const response = await fetch(`http://localhost:5000/api/scholarships/${scholarshipId}`);

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API Response:', data);

            // Handle the correct API response format: { success: true, data: {...} }
            const scholarshipData = data.data || data;

            // Process the scholarship data
            if (scholarshipData) {
              processScholarshipData(scholarshipData);
            } else {
              console.error('Unexpected API response format:', data);
              throw new Error('Invalid API response format');
            }
          } catch (apiError) {
            // This will be caught by the outer try/catch block
            throw apiError;
          }
        } else {
          throw new Error('No valid ID or slug provided');
        }
      } catch (err: any) {
        console.error('Error fetching scholarship:', err);

        // Provide more detailed error message if available
        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          const statusCode = err.response.status;
          const errorMessage = err.response.data?.message || 'An error occurred';

          if (statusCode === 404) {
            setError(`La bourse avec l'ID ${id || slug} n'existe pas. Veuillez vérifier l'ID et réessayer.`);
          } else {
            setError(`Erreur ${statusCode}: ${errorMessage}`);
          }
        } else if (err.request) {
          // The request was made but no response was received
          setError('Aucune réponse reçue du serveur. Veuillez vérifier votre connexion et que le serveur backend est en cours d\'exécution.');
          console.log('Request that failed:', err.request);
        } else {
          // Something happened in setting up the request that triggered an Error
          setError(`Échec du chargement des détails de la bourse: ${err.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchScholarship();
  }, [id, slug]);

  // Process and enhance the scholarship data
  const processScholarshipData = (data: Scholarship) => {
    // Ensure we have valid date strings
    const deadline = typeof data.deadline === 'object'
      ? new Date(data.deadline).toISOString()
      : data.deadline;

    const createdAt = data.createdAt || new Date().toISOString();
    const updatedAt = data.updatedAt || new Date().toISOString();

    // Calculate date-related fields if not provided by the API
    const isExpired = data.isExpired !== undefined
      ? data.isExpired
      : dateUtils.isDatePast(deadline);

    const daysRemaining = data.daysRemaining !== undefined
      ? data.daysRemaining
      : dateUtils.getDaysRemaining(deadline);

    const formattedDeadline = data.formattedDeadline || dateUtils.formatDate(deadline, DateFormat.MEDIUM);

    // Parse string fields into arrays where needed
    const enhancedData = {
      ...data,
      deadline,
      createdAt,
      updatedAt,
      isExpired,
      daysRemaining,
      formattedDeadline,

      // Parse financial benefits into a list if it exists
      financial_benefits_list: data.financial_benefits_summary
        ? data.financial_benefits_summary.split(',').map(item => item.trim())
        : [],

      // Parse eligibility criteria into a list if it exists
      eligibility_criteria_list: data.eligibility_summary
        ? data.eligibility_summary.split(',').map(item => item.trim())
        : [],

      // These fields might not exist in the database, so we create placeholders
      // In a real implementation, these would come from the database or be parsed from other fields
      study_fields: data.study_fields || ['Business', 'Engineering', 'Computer Science', 'Medicine'],
      universities: data.universities || ['University of Paris', 'Sorbonne University', 'École Polytechnique'],
      required_documents: data.required_documents || [
        { name: 'CV/Resume', description: 'Updated curriculum vitae' },
        { name: 'Motivation Letter', description: 'Explaining why you deserve this scholarship' },
        { name: 'Academic Transcripts', description: 'Official transcripts from your institution' }
      ],
      deadline_description: data.deadline_description || 'Applications must be submitted before midnight on the deadline date.'
    };

    // Log the processed data for debugging
    console.log('Processed scholarship data:', enhancedData);

    setScholarship(enhancedData);
  };

  // Render loading state
  if (loading) {
    return <LoadingSkeleton />;
  }

  // Render error state
  if (error || !scholarship) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Alert
          message="Bourse non trouvée"
          description={error || "La bourse que vous recherchez n'existe pas ou a été supprimée."}
          type="error"
          showIcon
          className="mb-6"
        />
        <div className="mt-6">
          <Link
            to="/scholarships"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Retour aux bourses
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Metadata */}
      <Helmet>
        <title>{scholarship.title} | MaBourse</title>
        <meta name="description" content={`${scholarship.title} - ${scholarship.description.substring(0, 160)}...`} />
        <meta property="og:title" content={`${scholarship.title} | MaBourse`} />
        <meta property="og:description" content={`${scholarship.title} - ${scholarship.description.substring(0, 160)}...`} />
        {scholarship.thumbnail && <meta property="og:image" content={scholarship.thumbnail} />}
        <script type="application/ld+json">
          {JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'EducationalOccupationalCredential',
            'name': scholarship.title,
            'description': scholarship.description,
            'educationalLevel': scholarship.level,
            'validIn': {
              '@type': 'Country',
              'name': scholarship.country
            },
            'validUntil': scholarship.deadline
          })}
        </script>
      </Helmet>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Very Slim Hero Section - Only Title */}
        <div className="relative rounded-xl overflow-hidden mb-6 shadow-md h-24 bg-gray-100">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-100"></div>
          <div className="relative h-full flex items-center px-6">
            <div className="max-w-4xl">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">
                {scholarship.title}
              </h1>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-5">
            {/* Key Information Section - Simple List */}
            <section className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <FileTextOutlined className="text-primary mr-2" />
                Informations clés
              </h2>
              <ul className="text-sm space-y-1.5">
                {scholarship.deadline && (
                  <li className="flex items-start">
                    <CalendarOutlined className="text-primary mr-2 mt-0.5" />
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium text-gray-700">Date limite :</span>
                        <span className="text-gray-600 ml-1">
                          {scholarship.formattedDeadline || dateUtils.formatDate(scholarship.deadline, DateFormat.MEDIUM)}
                        </span>

                        {/* Status badge */}
                        {scholarship.isExpired ? (
                          <Badge
                            className="ml-2"
                            count="Expirée"
                            style={{ backgroundColor: '#ff4d4f' }}
                          />
                        ) : (
                          <Badge
                            className="ml-2"
                            count={`${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} restant${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''}`}
                            style={{ backgroundColor: (scholarship.daysRemaining || 0) <= 7 ? '#faad14' : '#52c41a' }}
                          />
                        )}
                      </div>

                      {/* Days remaining indicator */}
                      {!scholarship.isExpired && (
                        <div className="text-xs text-gray-500 mt-1 flex items-center">
                          <ClockCircleOutlined className="mr-1" />
                          {(scholarship.daysRemaining || 0) === 0
                            ? "Dernier jour pour postuler !"
                            : `Il reste ${scholarship.daysRemaining || 0} jour${(scholarship.daysRemaining || 0) !== 1 ? 's' : ''} pour postuler`}
                        </div>
                      )}
                    </div>
                  </li>
                )}

                {scholarship.level && (
                  <li className="flex items-center">
                    <UserOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Niveau d'études :</span>
                    <span className="text-gray-600 ml-1">{scholarship.level}</span>
                  </li>
                )}

                {scholarship.country && (
                  <li className="flex items-center">
                    <GlobalOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Pays :</span>
                    <span className="text-gray-600 ml-1">{scholarship.country}</span>
                  </li>
                )}

                {scholarship.coverage && (
                  <li className="flex items-center">
                    <TrophyOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Couverture :</span>
                    <span className="text-gray-600 ml-1">{scholarship.coverage}</span>
                  </li>
                )}
              </ul>
            </section>

            {/* Description Section */}
            <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3">Description</h2>
              <div className="prose max-w-none text-gray-600 text-sm">
                <p>{scholarship.description}</p>
              </div>
            </section>

            {/* Eligibility Criteria Section */}
            {scholarship.eligibility_summary && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <CheckCircleOutlined className="text-primary mr-2" />
                  Critères d'Éligibilité
                </h2>
                <div className="text-gray-600 text-sm">
                  <p className="mb-2">{scholarship.eligibility_summary}</p>
                  {scholarship.eligibility_criteria_list && scholarship.eligibility_criteria_list.length > 0 && (
                    <ul className="list-disc pl-5 space-y-1">
                      {scholarship.eligibility_criteria_list.map((criterion, index) => (
                        <li key={index} className="text-gray-600">{criterion}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </section>
            )}

            {/* Financial Benefits Section */}
            {scholarship.financial_benefits_summary && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <DollarOutlined className="text-primary mr-2" />
                  Avantages Financiers
                </h2>
                <div className="text-gray-600 text-sm">
                  <p className="mb-2">{scholarship.financial_benefits_summary}</p>
                  {scholarship.financial_benefits_list && scholarship.financial_benefits_list.length > 0 && (
                    <ul className="list-disc pl-5 space-y-1">
                      {scholarship.financial_benefits_list.map((benefit, index) => (
                        <li key={index} className="text-gray-600">{benefit}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </section>
            )}

            {/* Study Fields Section */}
            {scholarship.study_fields && scholarship.study_fields.length > 0 && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <BookOutlined className="text-primary mr-2" />
                  Domaines d'Études
                </h2>
                <div className="flex flex-wrap gap-2">
                  {scholarship.study_fields.map((field, index) => (
                    <Tag key={index} color="blue" className="px-2 py-0.5 text-xs rounded-full">
                      {field}
                    </Tag>
                  ))}
                </div>
              </section>
            )}

            {/* Universities Section */}
            {scholarship.universities && scholarship.universities.length > 0 && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <BankOutlined className="text-primary mr-2" />
                  Universités Participantes
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {scholarship.universities.map((university, index) => (
                    <div key={index} className="flex items-center p-2 bg-gray-50 rounded-lg text-sm">
                      <BankOutlined className="text-gray-400 mr-2" />
                      <span className="text-gray-700">{university}</span>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Required Documents Section */}
            {scholarship.required_documents && scholarship.required_documents.length > 0 && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <FileTextOutlined className="text-primary mr-2" />
                  Documents Requis
                </h2>
                <div className="space-y-3">
                  {scholarship.required_documents.map((doc, index) => (
                    <div key={index} className="bg-gray-50 p-3 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-1 text-sm">{doc.name}</h3>
                      <p className="text-gray-600 text-xs">{doc.description}</p>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Essential Links Section */}
            <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <LinkOutlined className="text-primary mr-2" />
                Liens Essentiels
              </h2>
              <ul className="space-y-2 text-sm">
                <li className="text-gray-700">
                  Site officiel de la bourse: {" "}
                  <a
                    href={scholarship.scholarship_link || "https://example.com/scholarship"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    {scholarship.scholarship_link || "https://example.com/scholarship"}
                  </a>
                </li>
                <li className="text-gray-700">
                  Portail de candidature en ligne: {" "}
                  <a
                    href="https://example.com/apply"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    https://example.com/apply
                  </a>
                </li>
                <li className="text-gray-700">
                  Guide d'application PDF: {" "}
                  <a
                    href="https://example.com/guide.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    https://example.com/guide.pdf
                  </a>
                </li>
                <li className="text-gray-700">
                  FAQ sur la bourse: {" "}
                  <a
                    href="https://example.com/faq"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    https://example.com/faq
                  </a>
                </li>
              </ul>
            </section>

            {/* YouTube Channel Section */}
            <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <YoutubeOutlined className="text-red-600 mr-2" />
                Notre Chaîne YouTube
              </h2>
              <ul className="space-y-2 text-sm">
                <li className="text-gray-700">
                  Tutoriel vidéo sur cette bourse: {" "}
                  <a
                    href={scholarship.youtube_link || "https://www.youtube.com/watch?v=example1"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    {scholarship.youtube_link || "https://www.youtube.com/watch?v=example1"}
                  </a>
                </li>
                <li className="text-gray-700">
                  Comment préparer votre dossier de candidature: {" "}
                  <a
                    href="https://www.youtube.com/watch?v=example2"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    https://www.youtube.com/watch?v=example2
                  </a>
                </li>
                <li className="text-gray-700">
                  Visitez notre chaîne YouTube pour plus de tutoriels: {" "}
                  <a
                    href="https://www.youtube.com/channel/UCxxxxxxxxxxx"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-dark underline"
                  >
                    https://www.youtube.com/channel/UCxxxxxxxxxxx
                  </a>
                </li>
              </ul>
            </section>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Newsletter Subscription */}
            <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h3 className="text-base font-bold text-gray-900 mb-2">Restez informé</h3>
              <p className="text-xs text-gray-600 mb-3">
                Recevez les dernières bourses et opportunités directement dans votre boîte mail.
              </p>
              <form className="space-y-2">
                <div>
                  <input
                    type="email"
                    placeholder="Votre adresse email"
                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                <Button
                  type="primary"
                  size="small"
                  className="w-full"
                >
                  S'abonner
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  En vous inscrivant, vous acceptez notre politique de confidentialité.
                </p>
              </form>
            </div>

            {/* Suggested Scholarships */}
            <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h3 className="text-base font-bold text-gray-900 mb-3">Bourses Similaires</h3>
              <div className="space-y-3">
                {/* These would be dynamically loaded in a real implementation */}
                {[6, 7, 8, 10, 11, 13].map((scholarshipId, index) => (
                  <div key={index} className="group flex items-start p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" onClick={() => navigate(`/scholarships/${scholarshipId}`)}>
                    <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden mr-2">
                      <img
                        src={`/assets/scholarship${(index % 3) + 1}.jpg`}
                        alt="Scholarship thumbnail"
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/assets/default-thumbnail.jpg';
                        }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-xs font-medium text-gray-900 group-hover:text-primary transition-colors line-clamp-2">
                        {index === 0 ? "Fulbright Scholarship Program" :
                         index === 1 ? "Chevening Scholarship" :
                         index === 2 ? "DAAD Scholarship" :
                         index === 3 ? "Bourse du gouvernment indien" :
                         index === 4 ? "Updated Test Scholarship" :
                         "Example Scholarship 1"}
                      </h4>
                      <div className="flex items-center mt-0.5">
                        <CalendarOutlined className="text-gray-400 text-xs mr-1" />
                        <span className="text-xs text-gray-500 truncate">
                          {new Date(new Date().setMonth(new Date().getMonth() + index + 1)).toLocaleDateString()}
                        </span>
                        <GlobalOutlined className="text-gray-400 text-xs ml-2 mr-1" />
                        <span className="text-xs text-gray-500 truncate">
                          {index === 0 ? "United States" :
                           index === 1 ? "United Kingdom" :
                           index === 2 ? "Germany" :
                           index === 3 ? "Inde" :
                           index === 4 ? "France" :
                           "Canada"}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                <div className="pt-1 flex space-x-2">
                  <Button
                    type="default"
                    size="small"
                    className="flex-1"
                    onClick={() => navigate('/scholarships')}
                  >
                    Voir plus
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    className="flex-1"
                    onClick={() => navigate('/scholarships')}
                  >
                    Retour
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedScholarshipDetailPage;
