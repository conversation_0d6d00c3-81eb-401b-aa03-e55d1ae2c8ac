import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import scholarshipService from '../services/scholarshipService';
import { Scholarship } from '../components/ScholarshipGrid';

// Import enhanced components
import EnhancedHeroSection from '../components/EnhancedHeroSection';
import EnhancedLatestScholarshipsSection from '../components/EnhancedLatestScholarshipsSection';
import EnhancedStudyLevelSection from '../components/EnhancedStudyLevelSection';
import EnhancedFundingSourcesSection from '../components/EnhancedFundingSourcesSection';
import FeatureHighlightsSection from '../components/FeatureHighlightsSection';

import EnhancedNewsletterSection from '../components/EnhancedNewsletterSection';

const EnhancedHome: React.FC = () => {
  const navigate = useNavigate();

  // State for different scholarship categories
  const [latestScholarships, setLatestScholarships] = useState<Scholarship[]>([]);
  const [governmentScholarships, setGovernmentScholarships] = useState<Scholarship[]>([]);
  const [universityScholarships, setUniversityScholarships] = useState<Scholarship[]>([]);
  const [organizationScholarships, setOrganizationScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);

  // Scroll to latest scholarships section
  const scrollToLatestScholarships = () => {
    const element = document.getElementById('latest-scholarships');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Fetch scholarships on component mount
  useEffect(() => {
    const fetchAllScholarships = async () => {
      try {
        setLoading(true);

        // Fetch all scholarships at once
        const allScholarships = await scholarshipService.getAllScholarships();

        // Set latest scholarships (most recent 6)
        const sortedByDate = [...allScholarships].sort((a, b) => {
          const dateA = new Date(a.deadline);
          const dateB = new Date(b.deadline);
          return dateB.getTime() - dateA.getTime();
        });
        setLatestScholarships(sortedByDate.slice(0, 6));

        // Filter scholarships by funding source more precisely
        const government = allScholarships.filter(scholarship => {
          const source = scholarship.fundingSource?.toLowerCase() || '';
          return source === 'gouvernement' ||
                 source.includes('government') ||
                 source.includes('gouvernement') ||
                 source.includes('ministry') ||
                 source.includes('ministère');
        });
        setGovernmentScholarships(government);

        const university = allScholarships.filter(scholarship => {
          const source = scholarship.fundingSource?.toLowerCase() || '';
          return source === 'université' ||
                 source.includes('university') ||
                 source.includes('université') ||
                 source.includes('college') ||
                 source.includes('école');
        });
        setUniversityScholarships(university);

        const organization = allScholarships.filter(scholarship => {
          const source = scholarship.fundingSource?.toLowerCase() || '';
          return source === 'organisation' ||
                 source.includes('organization') ||
                 source.includes('organisation') ||
                 source.includes('foundation') ||
                 source.includes('fondation') ||
                 source.includes('ngo') ||
                 source.includes('ong') ||
                 (!source.includes('gouvernement') &&
                  !source.includes('government') &&
                  !source.includes('université') &&
                  !source.includes('university'));
        });
        setOrganizationScholarships(organization);
      } catch (error) {
        console.error('Error fetching scholarships:', error);
        // Use fallback data if API fails
        const fallbackData = [
          {
            id: 1,
            title: 'Bourse d\'Excellence en Informatique',
            thumbnail: '/assets/scholarship1.jpg',
            deadline: '2024-06-30',
            isOpen: true,
            level: 'Licence',
            fundingSource: 'Université',
            country: 'France',
          },
          {
            id: 2,
            title: 'Programme de Bourse en Génie Civil',
            thumbnail: '/assets/scholarship2.jpg',
            deadline: '2024-05-15',
            isOpen: true,
            level: 'Master',
            fundingSource: 'Gouvernement',
            country: 'Canada',
          },
          {
            id: 3,
            title: 'Bourse Internationale en Médecine',
            thumbnail: '/assets/scholarship3.jpg',
            deadline: '2024-04-30',
            isOpen: false,
            level: 'Doctorat',
            fundingSource: 'Organisation',
            country: 'Belgique',
          },
          {
            id: 4,
            title: 'Bourse de Recherche en Sciences Sociales',
            thumbnail: '/assets/scholarship4.jpg',
            deadline: '2024-07-15',
            isOpen: true,
            level: 'Doctorat',
            fundingSource: 'Organisation',
            country: 'Suisse',
          },
          {
            id: 5,
            title: 'Programme d\'Échange International',
            thumbnail: '/assets/scholarship5.jpg',
            deadline: '2024-05-30',
            isOpen: true,
            level: 'Licence',
            fundingSource: 'Gouvernement',
            country: 'Allemagne',
          },
          {
            id: 6,
            title: 'Bourse d\'Excellence Académique',
            thumbnail: '/assets/scholarship6.jpg',
            deadline: '2024-06-15',
            isOpen: true,
            level: 'Master',
            fundingSource: 'Université',
            country: 'Espagne',
          },
        ];

        setLatestScholarships(fallbackData);
        setGovernmentScholarships(fallbackData.filter(s => s.fundingSource === 'Gouvernement'));
        setUniversityScholarships(fallbackData.filter(s => s.fundingSource === 'Université'));
        setOrganizationScholarships(fallbackData.filter(s => s.fundingSource === 'Organisation'));
      } finally {
        setLoading(false);
      }
    };

    fetchAllScholarships();
  }, []);

  const handleScholarshipClick = (id: number, slug?: string) => {
    // Navigate to scholarship details page using slug if available
    if (slug) {
      navigate(`/bourse/${slug}`);
    } else {
      navigate(`/scholarships/${id}`);
    }
  };

  return (
    <div className="bg-white">
      {/* Enhanced Hero Section */}
      <EnhancedHeroSection scrollToLatestScholarships={scrollToLatestScholarships} />

      {/* Enhanced Latest Scholarships Section */}
      <EnhancedLatestScholarshipsSection
        scholarships={latestScholarships}
        loading={loading}
        onScholarshipClick={handleScholarshipClick}
      />

      {/* Enhanced Study Level Section */}
      <EnhancedStudyLevelSection />

      {/* Enhanced Funding Sources Section */}
      <EnhancedFundingSourcesSection
        governmentScholarships={governmentScholarships}
        universityScholarships={universityScholarships}
        organizationScholarships={organizationScholarships}
        loading={loading}
        onScholarshipClick={handleScholarshipClick}
      />

      {/* Feature Highlights Section */}
      <FeatureHighlightsSection />



      {/* Enhanced Newsletter Section */}
      <EnhancedNewsletterSection />
    </div>
  );
};

export default EnhancedHome;
