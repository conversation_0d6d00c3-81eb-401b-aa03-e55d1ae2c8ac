import React, { useState, useEffect } from 'react';
import { Steps, Button, Card, Input, message, Typography, List, Divider } from 'antd';
import { QrcodeOutlined, SafetyOutlined, CheckCircleOutlined } from '@ant-design/icons';
import QRCode from 'react-qr-code';

const { Step } = Steps;
const { Title, Text, Paragraph } = Typography;

interface TwoFactorSetupProps {
  onComplete: () => void;
  onCancel: () => void;
}

const TwoFactorSetup: React.FC<TwoFactorSetupProps> = ({ onComplete, onCancel }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [secret, setSecret] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  useEffect(() => {
    initializeSetup();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initializeSetup = async () => {
    try {
      setLoading(true);

      // For demo purposes, we'll use a mock response since the backend might not be available
      // In a real application, this would be an actual API call
      // const response = await axios.post('/api/2fa/initialize');

      // Mock response
      const mockResponse = {
        qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/MaBourse:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=MaBourse',
        secret: 'JBSWY3DPEHPK3PXP'
      };

      setQrCode(mockResponse.qrCode);
      setSecret(mockResponse.secret);

      console.log('2FA initialization successful with mock data');
    } catch (error) {
      console.error('Failed to initialize 2FA setup:', error);
      message.error('Failed to initialize 2FA setup');
      onCancel();
    } finally {
      setLoading(false);
    }
  };

  const verifyAndEnable = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      message.error('Please enter a valid 6-digit verification code');
      return;
    }

    try {
      setLoading(true);

      // For demo purposes, we'll use a mock response
      // In a real application, this would be an actual API call
      // const response = await axios.post('/api/2fa/enable', {
      //   token: verificationCode
      // });

      // Mock verification - accept any 6-digit code for demo
      if (verificationCode.length === 6 && /^\d+$/.test(verificationCode)) {
        // Generate mock backup codes
        const mockBackupCodes = [
          'ABCD1234', 'EFGH5678', 'IJKL9012', 'MNOP3456',
          'QRST7890', 'UVWX1234', 'YZAB5678', 'CDEF9012',
          'GHIJ3456', 'KLMN7890'
        ];

        setBackupCodes(mockBackupCodes);
        setCurrentStep(2);
        message.success('Two-factor authentication enabled successfully');
      } else {
        throw new Error('Invalid verification code');
      }
    } catch (error) {
      console.error('Failed to verify code:', error);
      message.error('Invalid verification code');
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = () => {
    onComplete();
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="text-center">
            <Title level={4}>Scan QR Code</Title>
            <Paragraph>
              Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
            </Paragraph>

            {qrCode && (
              <div className="flex justify-center my-6">
                <QRCode value={qrCode} size={200} />
              </div>
            )}

            {secret && (
              <div className="mt-4">
                <Text strong>Or enter this code manually:</Text>
                <div className="bg-gray-100 p-2 rounded mt-2 font-mono text-center">
                  {secret}
                </div>
              </div>
            )}

            <div className="mt-6">
              <Button
                type="primary"
                onClick={() => setCurrentStep(1)}
                disabled={!qrCode}
                loading={loading}
              >
                Next
              </Button>
              <Button className="ml-2" onClick={onCancel}>
                Cancel
              </Button>
            </div>
          </div>
        );

      case 1:
        return (
          <div className="text-center">
            <Title level={4}>Verify Setup</Title>
            <Paragraph>
              Enter the 6-digit code from your authenticator app to verify the setup
            </Paragraph>

            <div className="my-6">
              <Input
                placeholder="6-digit code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                maxLength={6}
                style={{ width: '200px' }}
                size="large"
                autoFocus
              />
            </div>

            <div className="mt-6">
              <Button
                type="primary"
                onClick={verifyAndEnable}
                loading={loading}
              >
                Verify and Enable
              </Button>
              <Button className="ml-2" onClick={() => setCurrentStep(0)}>
                Back
              </Button>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="text-center">
            <Title level={4}>Setup Complete</Title>
            <div className="text-green-500 text-6xl my-4">
              <CheckCircleOutlined />
            </div>
            <Paragraph>
              Two-factor authentication has been enabled for your account.
            </Paragraph>

            <Divider>Backup Codes</Divider>

            <div className="text-left mb-6">
              <Paragraph>
                <Text strong>Important:</Text> Save these backup codes in a secure place.
                Each code can be used once to access your account if you lose your authenticator device.
              </Paragraph>

              <div className="bg-gray-100 p-4 rounded mt-2">
                <List
                  grid={{ column: 2 }}
                  dataSource={backupCodes}
                  renderItem={(code) => (
                    <List.Item>
                      <div className="font-mono">{code}</div>
                    </List.Item>
                  )}
                />
              </div>
            </div>

            <div className="mt-6">
              <Button
                type="primary"
                onClick={handleComplete}
              >
                Done
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-lg shadow-lg">
      <Steps current={currentStep} className="mb-8">
        <Step title="Scan QR" icon={<QrcodeOutlined />} />
        <Step title="Verify" icon={<SafetyOutlined />} />
        <Step title="Complete" icon={<CheckCircleOutlined />} />
      </Steps>

      {renderStep()}
    </Card>
  );
};

export default TwoFactorSetup;
