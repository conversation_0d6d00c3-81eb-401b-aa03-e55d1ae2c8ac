import React, { useState, useEffect } from 'react';
import { Card, Form, Switch, Button, Input, Select, message, Divider, Space, Typography } from 'antd';

import { MailOutlined, SaveOutlined, SendOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface NotificationSettings {
  enabled: boolean;
  sendOnNewScholarship: boolean;
  sendOnScholarshipUpdate: boolean;
  emailTemplate: string;
  frequency: 'immediate' | 'daily' | 'weekly';
  lastSent: string | null;
}

const EmailNotificationSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings>({
    enabled: false,
    sendOnNewScholarship: true,
    sendOnScholarshipUpdate: false,
    emailTemplate: 'New scholarship available: {{scholarshipTitle}} - Check it out now!',
    frequency: 'immediate',
    lastSent: null
  });

  // No API needed for mock implementation

  useEffect(() => {
    fetchSettings();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);

      // Use mock data instead of API call
      console.log('Fetching notification settings (mock)');

      // Get settings from localStorage or use defaults
      const savedSettings = localStorage.getItem('email_notification_settings');

      // Default settings
      const data: NotificationSettings = {
        enabled: false,
        sendOnNewScholarship: true,
        sendOnScholarshipUpdate: false,
        emailTemplate: 'New scholarship available: {{scholarshipTitle}} - Check it out now!',
        frequency: 'immediate',
        lastSent: null
      };

      // If we have saved settings, use them
      if (savedSettings) {
        try {
          const parsedSettings = JSON.parse(savedSettings);
          if (typeof parsedSettings.enabled === 'boolean') data.enabled = parsedSettings.enabled;
          if (typeof parsedSettings.sendOnNewScholarship === 'boolean') data.sendOnNewScholarship = parsedSettings.sendOnNewScholarship;
          if (typeof parsedSettings.sendOnScholarshipUpdate === 'boolean') data.sendOnScholarshipUpdate = parsedSettings.sendOnScholarshipUpdate;
          if (typeof parsedSettings.emailTemplate === 'string') data.emailTemplate = parsedSettings.emailTemplate;
          if (typeof parsedSettings.frequency === 'string') data.frequency = parsedSettings.frequency as any;
          if (parsedSettings.lastSent) data.lastSent = parsedSettings.lastSent;
        } catch (e) {
          console.error('Error parsing saved settings:', e);
        }
      }

      setSettings(data);
      form.setFieldsValue(data);
      console.log('Notification settings loaded:', data);
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      message.error('Failed to load notification settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: any) => {
    try {
      setLoading(true);

      // Save settings to localStorage
      console.log('Saving notification settings:', values);
      localStorage.setItem('email_notification_settings', JSON.stringify(values));

      // Update state
      setSettings(values);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      message.success('Notification settings saved successfully');
    } catch (error) {
      console.error('Error saving notification settings:', error);
      message.error('Failed to save notification settings');
    } finally {
      setLoading(false);
    }
  };

  const sendTestEmail = async () => {
    try {
      setTestEmailLoading(true);

      // Get current form values
      const formValues = form.getFieldsValue();
      console.log('Sending test email with settings:', formValues);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success message with template preview
      const template = formValues.emailTemplate || 'New scholarship available: {{scholarshipTitle}} - Check it out now!';
      const previewTemplate = template
        .replace('{{scholarshipTitle}}', 'Example Scholarship')
        .replace('{{scholarshipLink}}', 'https://example.com/scholarship/123')
        .replace('{{scholarshipDeadline}}', '2024-12-31');

      message.success(
        <div>
          <div>Test email sent <NAME_EMAIL></div>
          <div style={{ marginTop: '8px', fontStyle: 'italic' }}>Preview: {previewTemplate}</div>
        </div>
      );
    } catch (error) {
      console.error('Error sending test email:', error);
      message.error('Failed to send test email');
    } finally {
      setTestEmailLoading(false);
    }
  };

  return (
    <Card title={
      <div className="flex items-center">
        <MailOutlined className="mr-2 text-blue-500" />
        <span>Email Notification Settings</span>
      </div>
    } className="shadow-md">
      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={handleSave}
      >
        <div className="mb-6">
          <Title level={5}>General Settings</Title>
          <Text type="secondary">Configure when and how email notifications are sent to subscribers</Text>
        </div>

        <Form.Item
          name="enabled"
          valuePropName="checked"
          label="Enable Email Notifications"
        >
          <Switch />
        </Form.Item>

        <Divider />

        <div className="mb-6">
          <Title level={5}>Notification Triggers</Title>
          <Text type="secondary">Select which events should trigger email notifications</Text>
        </div>

        <Form.Item
          name="sendOnNewScholarship"
          valuePropName="checked"
          label="New Scholarships"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="sendOnScholarshipUpdate"
          valuePropName="checked"
          label="Scholarship Updates"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="frequency"
          label="Notification Frequency"
        >
          <Select>
            <Option value="immediate">Immediate</Option>
            <Option value="daily">Daily Digest</Option>
            <Option value="weekly">Weekly Digest</Option>
          </Select>
        </Form.Item>

        <Divider />

        <div className="mb-6">
          <Title level={5}>Email Template</Title>
          <Text type="secondary">
            Customize the email template. You can use the following variables:
            <ul className="list-disc ml-5 mt-2">
              <li>{'{{scholarshipTitle}}'} - The title of the scholarship</li>
              <li>{'{{scholarshipLink}}'} - The link to the scholarship</li>
              <li>{'{{scholarshipDeadline}}'} - The deadline of the scholarship</li>
            </ul>
          </Text>
        </div>

        <Form.Item
          name="emailTemplate"
          label="Email Template"
        >
          <TextArea rows={4} />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Save Settings
            </Button>
            <Button
              onClick={sendTestEmail}
              loading={testEmailLoading}
              icon={<SendOutlined />}
            >
              Send Test Email
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default EmailNotificationSettings;
