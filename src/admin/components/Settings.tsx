import React, { useState } from 'react';
import { Card, Form, Input, Button, message, Typography, Divider, Alert, Modal, Space } from 'antd';
import { LockOutlined, MailOutlined, ArrowLeftOutlined, ClearOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
// localStorage clearing functionality removed - using HTTP-only cookies now

const { Title } = Typography;
const { confirm } = Modal;

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [resetPasswordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [resetPasswordMode, setResetPasswordMode] = useState(false);

  // Function to handle clearing all localStorage data
  const handleClearAllData = () => {
    confirm({
      title: 'Are you sure you want to clear all application data?',
      icon: <ExclamationCircleOutlined />,
      content: 'This will remove all locally stored data including admin accounts, messages, and scholarships. This action cannot be undone.',
      okText: 'Yes, Clear All Data',
      okType: 'danger',
      cancelText: 'No, Cancel',
      onOk() {
        // With HTTP-only cookies, data clearing is handled server-side
        message.info('Data clearing is now handled through secure server-side operations. Please contact system administrator for data management.');
      },
    });
  };

  // Function to handle clearing only mock data
  const handleClearMockData = () => {
    confirm({
      title: 'Clear only mock data?',
      icon: <ExclamationCircleOutlined />,
      content: 'This will remove only mock data (messages, scholarships) but keep your admin account information.',
      okText: 'Yes, Clear Mock Data',
      okType: 'primary',
      cancelText: 'No, Cancel',
      onOk() {
        // With HTTP-only cookies, data clearing is now handled server-side
        message.info('Data clearing is now handled through secure server-side operations. Please contact system administrator for data management.');
      },
    });
  };

  const handleResetPassword = async (values: any) => {
    setLoading(true);
    try {
      // Mock API call
      console.log('Resetting password with values:', values);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the password in localStorage
      localStorage.setItem('admin_password', values.newPassword);

      message.success('Password reset successfully');
      resetPasswordForm.resetFields();
      setResetPasswordMode(false);
    } catch (error) {
      message.error('Failed to reset password');
      console.error('Error resetting password:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCredentials = async (values: any) => {
    setLoading(true);
    try {
      // Mock API call
      console.log('Updating credentials with values:', values);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get the main admin from localStorage
      const savedAdmins = localStorage.getItem('admins');
      if (!savedAdmins) {
        throw new Error('Failed to retrieve admin information');
      }

      const admins = JSON.parse(savedAdmins);
      const mainAdmin = admins.find((admin: any) => admin.isMainAdmin);

      if (!mainAdmin) {
        throw new Error('Main admin account not found');
      }

      // For demo purposes, we'll store the password in localStorage
      // In a real app, this would be handled securely on the server
      if (!localStorage.getItem('admin_password')) {
        // Set default password if none exists
        localStorage.setItem('admin_password', 'admin123');
      }

      const currentStoredPassword = localStorage.getItem('admin_password');

      // Validate current password
      if (values.currentPassword !== currentStoredPassword) {
        throw new Error('Current password is incorrect');
      }

      // Validate that new password is different from current
      if (values.currentPassword === values.newPassword) {
        throw new Error('New password must be different from current password');
      }

      // Update the password in localStorage
      localStorage.setItem('admin_password', values.newPassword);

      message.success('Credentials updated successfully');
      form.resetFields();
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('Failed to update credentials');
      }
      console.error('Error updating credentials:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="shadow-lg">
        <Title level={2} className="text-center mb-8">Admin Settings</Title>

        {/* Data Management Section - Only visible to main admin */}
        {localStorage.getItem('adminInfo') && JSON.parse(localStorage.getItem('adminInfo') || '{}').isMainAdmin && (
          <div className="mb-8">
            <Divider orientation="left">Data Management</Divider>
            <Alert
              message="Warning: Data Reset Options"
              description="Use these options to clear application data when experiencing issues. Clearing all data will log you out."
              type="warning"
              showIcon
              className="mb-4"
            />
            <Space className="w-full justify-between">
              <Button
                type="primary"
                danger
                icon={<ClearOutlined />}
                onClick={handleClearAllData}
              >
                Clear All Application Data
              </Button>
              <Button
                type="default"
                icon={<ClearOutlined />}
                onClick={handleClearMockData}
              >
                Clear Only Mock Data
              </Button>
            </Space>
          </div>
        )}

        {resetPasswordMode ? (
          <>
            <div className="mb-4 flex justify-between items-center">
              <Title level={4}>Reset Password</Title>
              <Button
                onClick={() => setResetPasswordMode(false)}
                icon={<ArrowLeftOutlined />}
              >
                Back to Normal Mode
              </Button>
            </div>

            <Alert
              message="Reset Password Mode"
              description="Use this form to reset your password if you've forgotten it. This is only available for the main admin account."
              type="info"
              showIcon
              className="mb-4"
            />

            <Form
              form={resetPasswordForm}
              layout="vertical"
              onFinish={handleResetPassword}
              className="space-y-6"
            >
              <Form.Item
                name="newPassword"
                label="New Password"
                rules={[
                  { required: true, message: 'Please input your new password!' },
                  { min: 8, message: 'Password must be at least 8 characters!' }
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="Enter new password" />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label="Confirm New Password"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: 'Please confirm your new password!' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('The two passwords do not match!'));
                    },
                  }),
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="Confirm new password" />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  danger
                  className="w-full"
                >
                  Reset Password
                </Button>
              </Form.Item>
            </Form>
          </>
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdateCredentials}
            className="space-y-6"
          >
            <div className="flex justify-between items-center">
              <Divider orientation="left">Update Login Credentials</Divider>
              <Button
                type="link"
                onClick={() => setResetPasswordMode(true)}
                className="text-red-500"
              >
                Forgot Password?
              </Button>
            </div>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please input your email!' },
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input prefix={<MailOutlined />} placeholder="Enter your email" />
            </Form.Item>

            <Form.Item
              name="currentPassword"
              label="Current Password"
              rules={[{ required: true, message: 'Please input your current password!' }]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="Enter current password" />
            </Form.Item>

            <Form.Item
              name="newPassword"
              label="New Password"
              rules={[
                { required: true, message: 'Please input your new password!' },
                { min: 8, message: 'Password must be at least 8 characters!' }
              ]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="Enter new password" />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="Confirm New Password"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: 'Please confirm your new password!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('The two passwords do not match!'));
                  },
                }),
              ]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="Confirm new password" />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 border-none hover:from-blue-700 hover:to-indigo-700"
              >
                Update Credentials
              </Button>
            </Form.Item>
          </Form>
        )}
      </Card>
    </div>
  );
};

export default Settings;