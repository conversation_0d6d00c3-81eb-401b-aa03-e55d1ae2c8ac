import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  BookOutlined,
  UserOutlined,
  MessageOutlined,
  LogoutOutlined,
  TeamOutlined,
  MailOutlined
} from '@ant-design/icons';

const Sidebar: React.FC = () => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/admin/dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard'
    },
    {
      path: '/admin/scholarships',
      icon: <BookOutlined />,
      label: 'Scholarships'
    },
    {
      path: '/admin/admins',
      icon: <TeamOutlined />,
      label: 'Admin Management'
    },
    {
      path: '/admin/messages',
      icon: <MessageOutlined />,
      label: 'Messages'
    },
    {
      path: '/admin/newsletter',
      icon: <MailOutlined />,
      label: 'Newsletter'
    }
  ];

  return (
    <div className="h-screen w-64 bg-white shadow-lg fixed left-0 top-0">
      <div className="p-6">
        <h1 className="text-2xl font-bold text-blue-600">Admin Portal</h1>
      </div>
      
      <nav className="mt-6">
        {menuItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`flex items-center px-6 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors ${
              location.pathname === item.path ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600' : ''
            }`}
          >
            <span className="mr-3 text-lg">{item.icon}</span>
            {item.label}
          </Link>
        ))}
      </nav>

      <div className="absolute bottom-0 w-full p-6">
        <button
          className="flex items-center w-full px-6 py-3 text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors rounded"
          onClick={() => {
            // Handle logout
            localStorage.removeItem('adminToken');
            window.location.href = '/admin/login';
          }}
        >
          <LogoutOutlined className="mr-3 text-lg" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default Sidebar; 