import React, { useState, useEffect } from 'react';
import { Pagination, Input, Button, Spin, message, Alert, Modal, Form, Radio, Checkbox, Space, Tooltip, Select } from 'antd';
import { SearchOutlined, DownloadOutlined, PlusOutlined, UploadOutlined, FileExcelOutlined, FileTextOutlined, FilePdfOutlined, InfoCircleOutlined } from '@ant-design/icons';

import { CSVLink } from 'react-csv';
import { newsletterAPI } from '../../services/api';

interface Subscriber {
  id: number;
  email: string;
  createdAt: string;
}

const { Search } = Input;
const { TextArea } = Input;

const NewsletterManager: React.FC = () => {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [filteredSubscribers, setFilteredSubscribers] = useState<Subscriber[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isBulkImportModalVisible, setIsBulkImportModalVisible] = useState(false);
  const [isExportModalVisible, setIsExportModalVisible] = useState(false);
  const [bulkEmails, setBulkEmails] = useState('');
  const [exportFormat, setExportFormat] = useState<'csv' | 'json'>('csv');
  const [exportFields, setExportFields] = useState<string[]>(['email', 'createdAt']);
  const [exportFileName, setExportFileName] = useState('newsletter_subscribers');
  const [form] = Form.useForm();

  // Use the newsletterAPI directly instead of useAdminApi

  useEffect(() => {
    fetchSubscribers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    applyFilters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscribers, searchTerm]);

  const fetchSubscribers = async () => {
    try {
      setLoading(true);
      setError(null);

      try {
        // Fetch subscribers from API
        const data = await newsletterAPI.getSubscribers();

        if (Array.isArray(data)) {
          setSubscribers(data);
          setTotalItems(data.length);
          console.log('Subscribers fetched from API:', data);
        } else {
          throw new Error('No data received from API');
        }
      } catch (apiError: any) {
        console.error('Error fetching subscribers from API:', apiError);

        // Check if it's an authentication error
        if (apiError.response && apiError.response.status === 401) {
          setError('Authentication error. Please log in again to view subscribers.');
          return;
        }

        // For development or when API fails, use mock data
        if (process.env.NODE_ENV === 'development' || apiError) {
          // Mock data for subscribers
          const mockSubscribers: Subscriber[] = [
            {
              id: 1,
              email: '<EMAIL>',
              createdAt: '2023-10-15T08:30:00Z'
            },
            {
              id: 2,
              email: '<EMAIL>',
              createdAt: '2023-10-18T14:45:00Z'
            },
            {
              id: 3,
              email: '<EMAIL>',
              createdAt: '2023-10-20T11:20:00Z'
            },
            {
              id: 4,
              email: '<EMAIL>',
              createdAt: '2023-10-22T09:15:00Z'
            },
            {
              id: 5,
              email: '<EMAIL>',
              createdAt: '2023-10-25T16:30:00Z'
            }
          ];

          setSubscribers(mockSubscribers);
          setTotalItems(mockSubscribers.length);
          console.log('Using mock subscriber data');
        }
      }
    } catch (error) {
      console.error('Error in fetchSubscribers:', error);
      setError('Failed to load subscribers. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    if (searchTerm) {
      const filtered = subscribers.filter(subscriber =>
        subscriber.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSubscribers(filtered);
      setTotalItems(filtered.length);
    } else {
      setFilteredSubscribers(subscribers);
      setTotalItems(subscribers.length);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: 'Remove Subscriber',
      content: 'Are you sure you want to remove this subscriber?',
      okText: 'Yes, Remove',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          console.log('Removing subscriber with ID:', id);

          // Call API to delete subscriber
          await newsletterAPI.removeSubscriber(id);

          // Update local state
          const updatedSubscribers = subscribers.filter(subscriber => subscriber.id !== id);
          setSubscribers(updatedSubscribers);

          message.success('Subscriber removed successfully');
        } catch (error) {
          console.error('Error deleting subscriber:', error);
          message.error('Failed to remove subscriber');
        }
      }
    });
  };

  // Add a new subscriber
  const handleAddSubscriber = async (values: { email: string }) => {
    try {
      // Call API to add subscriber
      await newsletterAPI.addSubscriber(values.email);

      // Refresh the list
      fetchSubscribers();

      // Reset form and close modal
      form.resetFields();
      setIsModalVisible(false);

      message.success('Subscriber added successfully');
    } catch (error) {
      console.error('Error adding subscriber:', error);
      message.error('Failed to add subscriber');
    }
  };

  // Handle bulk import
  const handleBulkImport = async () => {
    try {
      // Split emails by newline, comma, or semicolon
      const emails = bulkEmails
        .split(/[\n,;]/)
        .map(email => email.trim())
        .filter(email => email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email));

      if (emails.length === 0) {
        message.error('No valid emails found');
        return;
      }

      // Show loading message
      const loadingMessage = message.loading(`Importing ${emails.length} subscribers...`, 0);

      try {
        // Use the bulk import endpoint
        const response = await newsletterAPI.bulkImport(emails);

        // Close loading message
        loadingMessage();

        // Show results
        if (response.results.success > 0) {
          message.success(`Successfully imported ${response.results.success} subscribers`);
        }

        if (response.results.duplicates > 0) {
          message.warning(`Skipped ${response.results.duplicates} duplicate subscribers`);
        }

        if (response.results.failures > 0) {
          message.error(`Failed to import ${response.results.failures} subscribers`);
        }
      } catch (error) {
        // Close loading message
        loadingMessage();
        console.error('Error calling bulk import API:', error);
        message.error('Failed to process bulk import');
      }

      // Refresh the list
      fetchSubscribers();

      // Reset form and close modal
      setBulkEmails('');
      setIsBulkImportModalVisible(false);
    } catch (error) {
      console.error('Error in bulk import:', error);
      message.error('Failed to process bulk import');
    }
  };

  // Prepare data for export
  const prepareExportData = () => {
    // Filter subscribers based on current search/filter
    const dataToExport = searchTerm ? filteredSubscribers : subscribers;

    // Format data based on selected fields
    return dataToExport.map(subscriber => {
      const formattedData: Record<string, any> = {};

      if (exportFields.includes('id')) {
        formattedData.id = subscriber.id;
      }

      if (exportFields.includes('email')) {
        formattedData.email = subscriber.email;
      }

      if (exportFields.includes('createdAt')) {
        formattedData.subscribed_date = new Date(subscriber.createdAt).toLocaleDateString();
      }

      return formattedData;
    });
  };

  // Handle export
  const handleExport = () => {
    try {
      const exportData = prepareExportData();

      if (exportFormat === 'json') {
        // For JSON export, create and download a JSON file
        const jsonData = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${exportFileName}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success('Subscribers exported successfully as JSON');
      } else {
        // CSV export is handled by CSVLink component
        // Just close the modal
        message.success('Subscribers exported successfully as CSV');
      }

      setIsExportModalVisible(false);
    } catch (error) {
      console.error('Error exporting subscribers:', error);
      message.error('Failed to export subscribers');
    }
  };

  // Get CSV headers based on selected fields
  const getCSVHeaders = () => {
    const headers = [];

    if (exportFields.includes('id')) {
      headers.push({ label: 'ID', key: 'id' });
    }

    if (exportFields.includes('email')) {
      headers.push({ label: 'Email', key: 'email' });
    }

    if (exportFields.includes('createdAt')) {
      headers.push({ label: 'Subscription Date', key: 'subscribed_date' });
    }

    return headers;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Newsletter Subscribers</h2>
        <div className="space-x-2">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsModalVisible(true)}
          >
            Add Subscriber
          </Button>
          <Button
            type="default"
            icon={<UploadOutlined />}
            onClick={() => setIsBulkImportModalVisible(true)}
          >
            Bulk Import
          </Button>
          <Button
            type="default"
            icon={<DownloadOutlined />}
            onClick={() => setIsExportModalVisible(true)}
          >
            Export Data
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="mb-4"
        />
      )}

      {/* Search */}
      <div className="bg-white p-4 rounded-lg shadow-md mb-6">
        <Search
          placeholder="Search subscribers by email..."
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          onSearch={handleSearch}
          className="w-full"
        />
      </div>

      {/* Subscribers Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribed Since</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSubscribers.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="px-6 py-4 text-center text-gray-500">
                      No subscribers found
                    </td>
                  </tr>
                ) : (
                  filteredSubscribers
                    .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                    .map((subscriber) => (
                      <tr key={subscriber.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{subscriber.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(subscriber.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Button
                            type="link"
                            danger
                            onClick={() => handleDelete(subscriber.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Remove
                          </Button>
                        </td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>

            {/* Pagination */}
            <div className="px-6 py-4 flex justify-end">
              <Pagination
                current={currentPage}
                total={totalItems}
                pageSize={pageSize}
                onChange={handlePageChange}
                showSizeChanger
                showTotal={(total) => `Total ${total} subscribers`}
              />
            </div>
          </>
        )}
      </div>

      {/* Add Subscriber Modal */}
      <Modal
        title="Add New Subscriber"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddSubscriber}
        >
          <Form.Item
            name="email"
            label="Email Address"
            rules={[
              { required: true, message: 'Please enter an email address' },
              { type: 'email', message: 'Please enter a valid email address' }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>
          <Form.Item className="mb-0 flex justify-end">
            <Button type="default" className="mr-2" onClick={() => {
              setIsModalVisible(false);
              form.resetFields();
            }}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Add Subscriber
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Bulk Import Modal */}
      <Modal
        title="Bulk Import Subscribers"
        open={isBulkImportModalVisible}
        onCancel={() => {
          setIsBulkImportModalVisible(false);
          setBulkEmails('');
        }}
        onOk={handleBulkImport}
        okText="Import"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Enter email addresses separated by commas, semicolons, or new lines.
          </p>
          <TextArea
            rows={10}
            value={bulkEmails}
            onChange={(e) => setBulkEmails(e.target.value)}
            placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
          />
        </div>
      </Modal>

      {/* Export Modal */}
      <Modal
        title="Export Subscribers"
        open={isExportModalVisible}
        onCancel={() => setIsExportModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsExportModalVisible(false)}>
            Cancel
          </Button>,
          exportFormat === 'csv' ? (
            <CSVLink
              key="export"
              data={prepareExportData()}
              headers={getCSVHeaders()}
              filename={`${exportFileName}.csv`}
              className="ant-btn ant-btn-primary"
              onClick={() => {
                message.success('Subscribers exported successfully as CSV');
                setIsExportModalVisible(false);
              }}
            >
              Export CSV
            </CSVLink>
          ) : (
            <Button key="export" type="primary" onClick={handleExport}>
              Export JSON
            </Button>
          )
        ]}
      >
        <div className="space-y-6">
          <div>
            <h4 className="mb-2 font-medium">Export Format</h4>
            <Radio.Group
              value={exportFormat}
              onChange={(e) => setExportFormat(e.target.value)}
              className="mb-4"
            >
              <Space direction="vertical">
                <Radio value="csv">
                  <Space>
                    <FileExcelOutlined />
                    <span>CSV (Comma Separated Values)</span>
                    <Tooltip title="Compatible with Excel, Google Sheets, and most data processing tools">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                </Radio>
                <Radio value="json">
                  <Space>
                    <FileTextOutlined />
                    <span>JSON (JavaScript Object Notation)</span>
                    <Tooltip title="Best for developers or data integration">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                </Radio>
              </Space>
            </Radio.Group>
          </div>

          <div>
            <h4 className="mb-2 font-medium">Fields to Export</h4>
            <Checkbox.Group
              value={exportFields}
              onChange={(values) => setExportFields(values as string[])}
              className="mb-4"
            >
              <Space direction="vertical">
                <Checkbox value="id">ID</Checkbox>
                <Checkbox value="email">Email Address</Checkbox>
                <Checkbox value="createdAt">Subscription Date</Checkbox>
              </Space>
            </Checkbox.Group>
            {exportFields.length === 0 && (
              <Alert
                message="Please select at least one field to export"
                type="warning"
                showIcon
                className="mt-2"
              />
            )}
          </div>

          <div>
            <h4 className="mb-2 font-medium">File Name</h4>
            <Input
              value={exportFileName}
              onChange={(e) => setExportFileName(e.target.value)}
              placeholder="newsletter_subscribers"
              addonAfter={`.${exportFormat}`}
            />
          </div>

          <Alert
            message="Export Information"
            description={`This will export ${searchTerm ? filteredSubscribers.length : subscribers.length} subscribers in ${exportFormat.toUpperCase()} format.`}
            type="info"
            showIcon
          />
        </div>
      </Modal>
    </div>
  );
};

export default NewsletterManager;