import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Space,
  Card,
  Typography,
  Popconfirm,
  Tag
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface Admin {
  id: number;
  name: string;
  email: string;
  role: string;
  privileges: string[];
  isMainAdmin: boolean;
}

const AdminManagement: React.FC = () => {
  const { admin: currentAdmin } = useAuth();
  const isMainAdmin = currentAdmin?.isMainAdmin || false;
  const navigate = useNavigate();
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState<Admin | null>(null);
  const [form] = Form.useForm();

  // Redirect non-main admins away from this page
  useEffect(() => {
    if (!isMainAdmin) {
      message.error('Access denied. Only the main admin can access this page.');
      navigate('/admin/dashboard', { replace: true });
    }
  }, [isMainAdmin, navigate]);

  useEffect(() => {
    fetchAdmins();

    // Debug: Log when component mounts
    console.log('AdminManagement component mounted');

    // Add event listener to log localStorage changes
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
      console.log(`localStorage.setItem called with key "${key}":`, value);
      originalSetItem.call(this, key, value);
    };

    return () => {
      // Restore original function when component unmounts
      localStorage.setItem = originalSetItem;
      console.log('AdminManagement component unmounted');
    };
  }, []);

  const fetchAdmins = async () => {
    try {
      console.log('Fetching admins from API...');

      // Get the admin token from localStorage
      const token = localStorage.getItem('adminToken');
      if (!token) {
        console.error('No admin token found');
        message.error('Authentication error. Please log in again.');
        return;
      }

      // Fetch admins from the API
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/all`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch admins: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Admins loaded from API:', data);
      setAdmins(data);
    } catch (error) {
      console.error('Error in fetchAdmins:', error);
      message.error('Failed to fetch admins');
    }
  };

  const handleCreateAdmin = async (values: any) => {
    try {
      console.log('Creating admin with values:', values);

      // Check if we're trying to create a main admin
      if (values.email.toLowerCase() === '<EMAIL>') {
        message.error('Cannot create another Main Admin account');
        return;
      }

      // Get the admin token from localStorage
      const token = localStorage.getItem('adminToken');
      if (!token) {
        console.error('No admin token found');
        message.error('Authentication error. Please log in again.');
        return;
      }

      // Create admin through the API
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: values.name,
          email: values.email,
          password: values.password,
          role: values.role
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create admin: ${response.status} ${response.statusText}`);
      }

      const newAdmin = await response.json();
      console.log('New admin created via API:', newAdmin);

      // Refresh the admin list
      fetchAdmins();

      message.success('Admin created successfully');
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      if (error instanceof Error && error.message.includes('Admin already exists')) {
        message.error('An admin with this email already exists');
      } else {
        message.error('Failed to create admin');
        console.error('Error creating admin:', error);
      }
    }
  };

  const handleEdit = (record: Admin) => {
    setEditingAdmin(record);
    form.setFieldsValue({
      name: record.name,
      email: record.email,
      role: record.role,
    });
    setIsModalVisible(true);
  };

  const handleUpdateAdmin = async (values: any) => {
    if (!editingAdmin) return;

    try {
      console.log('Updating admin with ID:', editingAdmin.id, 'Values:', values);

      // Mock updating an admin
      const updatedAdmins = admins.map(admin => {
        if (admin.id === editingAdmin.id) {
          return {
            ...admin,
            name: values.name,
            email: values.email,
            role: values.role,
            privileges: values.role === 'super_admin' ? ['view', 'edit', 'delete'] : ['view', 'edit'],
          };
        }
        return admin;
      });

      // Save to localStorage
      localStorage.setItem('admins', JSON.stringify(updatedAdmins));

      setAdmins(updatedAdmins);

      message.success('Admin updated successfully');
      setIsModalVisible(false);
      form.resetFields();
      setEditingAdmin(null);
    } catch (error) {
      message.error('Failed to update admin');
      console.error('Error updating admin:', error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      console.log('Deleting admin with ID:', id);

      // Check if this is the main admin
      const adminToDelete = admins.find(admin => admin.id === id);
      if (adminToDelete?.isMainAdmin) {
        message.error('The main admin account cannot be deleted');
        return;
      }

      // Get the admin token from localStorage
      const token = localStorage.getItem('adminToken');
      if (!token) {
        console.error('No admin token found');
        message.error('Authentication error. Please log in again.');
        return;
      }

      // Delete admin through the API
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete admin: ${response.status} ${response.statusText}`);
      }

      // Refresh the admin list
      fetchAdmins();

      message.success('Admin deleted successfully');
    } catch (error) {
      message.error('Failed to delete admin');
      console.error('Error deleting admin:', error);
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: string, record: Admin) => (
        <div>
          {record.isMainAdmin ? (
            <>
              <span style={{ color: '#722ed1', fontWeight: 'bold' }}>Main Admin</span>
              <div style={{ marginTop: '4px' }}>
                <Tag color="gold">Current User (Cannot be removed)</Tag>
              </div>
            </>
          ) : (
            <span style={{
              color: role === 'super_admin' ? '#1890ff' : '#52c41a',
              fontWeight: 'bold'
            }}>
              {role}
            </span>
          )}
        </div>
      ),
    },
    {
      title: 'Privileges',
      dataIndex: 'privileges',
      key: 'privileges',
      render: (privileges: string[], record: Admin) => (
        <Space>
          {record.isMainAdmin ? (
            <Tag color="purple" style={{ fontWeight: 'bold' }}>All Privileges</Tag>
          ) : (
            privileges.map((privilege, index) => (
              <Tag key={index} color="blue">
                {privilege}
              </Tag>
            ))
          )}
        </Space>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Admin) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            disabled={record.isMainAdmin || record.id === currentAdmin?.id}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this admin?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              disabled={record.isMainAdmin || record.id === currentAdmin?.id}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Card className="mb-6">
        <div className="flex justify-between items-center mb-6">
          <Title level={2}>Admin Management</Title>
          <div className="flex space-x-4">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={(e) => {
                e.preventDefault(); // Prevent any default behavior
                setEditingAdmin(null);
                form.resetFields();
                setIsModalVisible(true);
              }}
              size="large"
            >
              Add New Admin
            </Button>

            <Button
              type="default"
              onClick={(e) => {
                e.preventDefault();
                // Refresh the admin list from the database
                fetchAdmins();
                message.success('Admin list refreshed from database');
              }}
              size="large"
            >
              Refresh Admin List
            </Button>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={admins}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      <Modal
        title={editingAdmin ? 'Edit Admin' : 'Add New Admin'}
        open={isModalVisible}
        onCancel={(e) => {
          e.preventDefault(); // Prevent any default behavior
          setIsModalVisible(false);
          form.resetFields();
          setEditingAdmin(null);
        }}
        maskClosable={false}
        destroyOnClose={true}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={editingAdmin ? handleUpdateAdmin : handleCreateAdmin}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please input admin name!' }]}
          >
            <Input placeholder="Enter admin name" />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please input admin email!' },
              { type: 'email', message: 'Please enter a valid email!' }
            ]}
          >
            <Input placeholder="Enter admin email" />
          </Form.Item>

          {!editingAdmin && (
            <>
              <Form.Item
                name="password"
                label="Password"
                rules={[
                  { required: true, message: 'Please input admin password!' },
                  { min: 8, message: 'Password must be at least 8 characters!' }
                ]}
              >
                <Input.Password placeholder="Enter admin password" />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                label="Confirm Password"
                dependencies={['password']}
                rules={[
                  { required: true, message: 'Please confirm admin password!' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('The two passwords do not match!'));
                    },
                  }),
                ]}
              >
                <Input.Password placeholder="Confirm admin password" />
              </Form.Item>
            </>
          )}

          <Form.Item
            name="role"
            label="Role"
            rules={[{ required: true, message: 'Please select admin role!' }]}
          >
            <Select placeholder="Select admin role">
              <Select.Option value="admin">Admin</Select.Option>
              <Select.Option value="super_admin">Super Admin</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <div className="flex justify-end space-x-4">
              <Button onClick={() => {
                setIsModalVisible(false);
                form.resetFields();
                setEditingAdmin(null);
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAdmin ? 'Update Admin' : 'Create Admin'}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminManagement;