import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Layout, Typography } from 'antd';
import { MailOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import axios from 'axios';
import logo from '../../assets/logo.png';

const { Content } = Layout;
const { Title, Text } = Typography;

const ForgotPassword: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const onFinish = async (values: { email: string }) => {
    setLoading(true);
    try {
      await axios.post(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/password/forgot-password`, {
        email: values.email
      });
      
      setEmailSent(true);
      message.success('If your email is registered, you will receive a password reset link');
    } catch (error) {
      console.error('Forgot password error:', error);
      message.error('An error occurred. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Content className="flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-lg">
          <div className="text-center mb-8">
            <img src={logo} alt="MaBourse Logo" className="h-16 mx-auto mb-4" />
            <Title level={2} className="text-gray-800">Reset Admin Password</Title>
            
            {!emailSent ? (
              <Text className="text-gray-600">
                Enter your email address and we'll send you a link to reset your password
              </Text>
            ) : (
              <Text className="text-green-600">
                If your email is registered, you will receive a password reset link shortly
              </Text>
            )}
          </div>

          {!emailSent ? (
            <Form
              name="forgot-password"
              onFinish={onFinish}
              layout="vertical"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: 'Please input your email!' },
                  { type: 'email', message: 'Please enter a valid email!' }
                ]}
              >
                <Input
                  prefix={<MailOutlined className="text-gray-400" />}
                  placeholder="Email"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  size="large"
                >
                  Send Reset Link
                </Button>
              </Form.Item>
            </Form>
          ) : (
            <div className="text-center">
              <Button type="link" onClick={() => setEmailSent(false)}>
                Send another reset link
              </Button>
            </div>
          )}

          <div className="text-center mt-4">
            <Link to="/admin/login" className="text-blue-600 hover:text-blue-800">
              Back to Login
            </Link>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default ForgotPassword;
