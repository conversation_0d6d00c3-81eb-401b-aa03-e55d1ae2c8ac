import React from 'react';
import { Typography, Breadcrumb } from 'antd';
import { AreaChartOutlined, HomeOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import AnalyticsDashboard from '../components/AnalyticsDashboard';

const { Title } = Typography;

const Analytics: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/admin/dashboard">
              <HomeOutlined /> Dashboard
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <AreaChartOutlined /> Analytics
          </Breadcrumb.Item>
        </Breadcrumb>
        
        <Title level={2} className="mt-4">
          <AreaChartOutlined className="mr-2" />
          Analytics
        </Title>
      </div>
      
      <AnalyticsDashboard />
    </div>
  );
};

export default Analytics;
