import React from 'react';
import { Typography, Breadcrumb, Row, Col } from 'antd';
import { MailOutlined, HomeOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import EmailNotificationSettings from '../components/EmailNotificationSettings';

const { Title } = Typography;

const EmailNotifications: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/admin/dashboard">
              <HomeOutlined /> Dashboard
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <MailOutlined /> Email Notifications
          </Breadcrumb.Item>
        </Breadcrumb>
        
        <Title level={2} className="mt-4">
          <MailOutlined className="mr-2" />
          Email Notifications
        </Title>
      </div>
      
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <EmailNotificationSettings />
        </Col>
      </Row>
    </div>
  );
};

export default EmailNotifications;
