import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, message, Layout, Typography, Result } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import logo from '../../assets/logo.png';

const { Content } = Layout;
const { Title, Text } = Typography;

const ResetPassword: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [validatingToken, setValidatingToken] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);

  useEffect(() => {
    const validateToken = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/password/validate-token/${token}`
        );
        
        setTokenValid(response.data.valid);
      } catch (error) {
        console.error('Token validation error:', error);
        setTokenValid(false);
        message.error('Invalid or expired reset token');
      } finally {
        setValidatingToken(false);
      }
    };

    if (token) {
      validateToken();
    } else {
      setValidatingToken(false);
      setTokenValid(false);
    }
  }, [token]);

  const onFinish = async (values: { password: string; confirmPassword: string }) => {
    if (values.password !== values.confirmPassword) {
      message.error('Passwords do not match');
      return;
    }

    setLoading(true);
    try {
      await axios.post(
        `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/password/reset-password`,
        {
          token,
          password: values.password
        }
      );
      
      setResetSuccess(true);
      message.success('Password has been reset successfully');
      
      // Redirect to login page after 3 seconds
      setTimeout(() => {
        navigate('/admin/login');
      }, 3000);
    } catch (error) {
      console.error('Reset password error:', error);
      message.error('Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (validatingToken) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="flex items-center justify-center p-4">
          <Card className="w-full max-w-md shadow-lg text-center">
            <div className="text-center mb-8">
              <img src={logo} alt="MaBourse Logo" className="h-16 mx-auto mb-4" />
              <Title level={2} className="text-gray-800">Reset Admin Password</Title>
            </div>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
            <Text className="block mt-4">Validating your reset token...</Text>
          </Card>
        </Content>
      </Layout>
    );
  }

  if (!tokenValid) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="flex items-center justify-center p-4">
          <Card className="w-full max-w-md shadow-lg">
            <Result
              status="error"
              title="Invalid Reset Token"
              subTitle="The password reset link is invalid or has expired."
              extra={[
                <Button type="primary" key="console" onClick={() => navigate('/admin/forgot-password')}>
                  Request New Reset Link
                </Button>,
                <Button key="login" onClick={() => navigate('/admin/login')}>
                  Back to Login
                </Button>,
              ]}
            />
          </Card>
        </Content>
      </Layout>
    );
  }

  if (resetSuccess) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="flex items-center justify-center p-4">
          <Card className="w-full max-w-md shadow-lg">
            <Result
              status="success"
              title="Password Reset Successful"
              subTitle="Your password has been reset successfully. You will be redirected to the login page."
              extra={[
                <Button type="primary" key="console" onClick={() => navigate('/admin/login')}>
                  Go to Login
                </Button>
              ]}
            />
          </Card>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Content className="flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-lg">
          <div className="text-center mb-8">
            <img src={logo} alt="MaBourse Logo" className="h-16 mx-auto mb-4" />
            <Title level={2} className="text-gray-800">Reset Admin Password</Title>
            <Text className="text-gray-600">
              Please enter your new password
            </Text>
          </div>

          <Form
            name="reset-password"
            onFinish={onFinish}
            layout="vertical"
          >
            <Form.Item
              name="password"
              rules={[
                { required: true, message: 'Please input your new password!' },
                { min: 6, message: 'Password must be at least 6 characters long' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="New Password"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              rules={[
                { required: true, message: 'Please confirm your new password!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('The two passwords do not match!'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="Confirm New Password"
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
              >
                Reset Password
              </Button>
            </Form.Item>
          </Form>

          <div className="text-center mt-4">
            <Link to="/admin/login" className="text-blue-600 hover:text-blue-800">
              Back to Login
            </Link>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default ResetPassword;
