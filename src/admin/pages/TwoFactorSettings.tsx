import React, { useState, useEffect } from 'react';
import { Card, Button, Alert, Spin, Input, Modal, message, Typography } from 'antd';
import { SecurityScanOutlined, LockOutlined } from '@ant-design/icons';
import TwoFactorSetup from '../components/TwoFactorSetup';

const { Title, Text, Paragraph } = Typography;

const TwoFactorSettings: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [backupCodesCount, setBackupCodesCount] = useState(0);
  const [showSetup, setShowSetup] = useState(false);
  const [showDisableModal, setShowDisableModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [disableLoading, setDisableLoading] = useState(false);

  useEffect(() => {
    fetchTwoFactorStatus();
  }, []);

  const fetchTwoFactorStatus = async () => {
    try {
      setLoading(true);

      // For demo purposes, we'll use mock data
      // In a real application, this would be an actual API call
      // const response = await axios.get('/api/2fa/status');

      // Mock response
      const mockEnabled = localStorage.getItem('demo_2fa_enabled') === 'true';
      const mockBackupCodesCount = mockEnabled ? 10 : 0;

      setTwoFactorEnabled(mockEnabled);
      setBackupCodesCount(mockBackupCodesCount);

      console.log('2FA status fetched:', { enabled: mockEnabled, backupCodesCount: mockBackupCodesCount });
    } catch (error) {
      console.error('Failed to fetch 2FA status:', error);
      message.error('Failed to load two-factor authentication status');
    } finally {
      setLoading(false);
    }
  };

  const handleSetupComplete = () => {
    setShowSetup(false);

    // Store 2FA status in localStorage for demo purposes
    localStorage.setItem('demo_2fa_enabled', 'true');

    fetchTwoFactorStatus();
    message.success('Two-factor authentication has been enabled');
  };

  const handleDisable2FA = async () => {
    if (!verificationCode) {
      message.error('Please enter your verification code');
      return;
    }

    try {
      setDisableLoading(true);

      // For demo purposes, we'll accept any 6-digit code
      if (verificationCode.length === 6 && /^\d+$/.test(verificationCode)) {
        // Store 2FA status in localStorage for demo purposes
        localStorage.setItem('demo_2fa_enabled', 'false');

        setShowDisableModal(false);
        setVerificationCode('');
        fetchTwoFactorStatus();
        message.success('Two-factor authentication has been disabled');
      } else {
        throw new Error('Invalid verification code');
      }
    } catch (error) {
      console.error('Failed to disable 2FA:', error);
      message.error('Invalid verification code');
    } finally {
      setDisableLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (showSetup) {
    return (
      <TwoFactorSetup
        onComplete={handleSetupComplete}
        onCancel={() => setShowSetup(false)}
      />
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="shadow-md">
        <div className="flex items-center mb-6">
          <SecurityScanOutlined className="text-2xl mr-2 text-blue-500" />
          <Title level={3} className="m-0">Two-Factor Authentication</Title>
        </div>

        <Paragraph>
          Two-factor authentication adds an extra layer of security to your account.
          When enabled, you'll need to provide a verification code from your authenticator app
          in addition to your password when signing in.
        </Paragraph>

        {twoFactorEnabled ? (
          <>
            <Alert
              message="Two-factor authentication is enabled"
              description="Your account is protected with an additional layer of security."
              type="success"
              showIcon
              className="mb-6"
            />

            <div className="mb-6">
              <Text strong>Backup Codes:</Text>
              <Paragraph>
                You have {backupCodesCount} backup codes remaining. Backup codes can be used to access your
                account if you lose your authenticator device.
              </Paragraph>
            </div>

            <Button
              danger
              onClick={() => setShowDisableModal(true)}
            >
              Disable Two-Factor Authentication
            </Button>
          </>
        ) : (
          <>
            <Alert
              message="Two-factor authentication is not enabled"
              description="Enable two-factor authentication to add an extra layer of security to your account."
              type="warning"
              showIcon
              className="mb-6"
            />

            <Button
              type="primary"
              onClick={() => setShowSetup(true)}
            >
              Enable Two-Factor Authentication
            </Button>
          </>
        )}
      </Card>

      <Modal
        title="Disable Two-Factor Authentication"
        open={showDisableModal}
        onCancel={() => {
          setShowDisableModal(false);
          setVerificationCode('');
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setShowDisableModal(false);
              setVerificationCode('');
            }}
          >
            Cancel
          </Button>,
          <Button
            key="disable"
            danger
            loading={disableLoading}
            onClick={handleDisable2FA}
          >
            Disable
          </Button>
        ]}
      >
        <div className="mb-4">
          <Paragraph>
            To disable two-factor authentication, please enter a verification code from your authenticator app.
          </Paragraph>
        </div>
        <Input
          prefix={<LockOutlined className="text-gray-400" />}
          placeholder="Verification code"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value)}
          maxLength={6}
        />
      </Modal>
    </div>
  );
};

export default TwoFactorSettings;
