import React, { useState, useEffect } from 'react';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Table, Space, Modal, Form, Input, Select, Switch, DatePicker, Tag, message } from 'antd';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  requirements?: string;
  benefits?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
  contactEmail?: string;
  website?: string;
  createdAt: string;
  updatedAt: string;
}

const OpportunityManager: React.FC = () => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOpportunity, setEditingOpportunity] = useState<Opportunity | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchOpportunities();
  }, []);

  const fetchOpportunities = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/opportunities?limit=100', {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.data.opportunities || []);
      } else {
        message.error('Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      message.error('Error fetching opportunities');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingOpportunity(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (opportunity: Opportunity) => {
    setEditingOpportunity(opportunity);
    form.setFieldsValue({
      ...opportunity,
      deadline: opportunity.deadline ? dayjs(opportunity.deadline) : null,
      startDate: opportunity.startDate ? dayjs(opportunity.startDate) : null,
      endDate: opportunity.endDate ? dayjs(opportunity.endDate) : null,
      tags: opportunity.tags?.join(', ') || ''
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: 'Delete Opportunity',
      content: 'Are you sure you want to delete this opportunity? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const response = await fetch(`/api/opportunities/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          });
          if (response.ok) {
            message.success('Opportunity deleted successfully');
            fetchOpportunities();
          } else {
            message.error('Failed to delete opportunity');
          }
        } catch (error) {
          console.error('Error deleting opportunity:', error);
          message.error('Error deleting opportunity');
        }
      }
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      const opportunityData = {
        ...values,
        deadline: values.deadline ? values.deadline.toISOString() : null,
        startDate: values.startDate ? values.startDate.toISOString() : null,
        endDate: values.endDate ? values.endDate.toISOString() : null,
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : []
      };

      const url = editingOpportunity ? `/api/opportunities/${editingOpportunity.id}` : '/api/opportunities';
      const method = editingOpportunity ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(opportunityData)
      });

      if (response.ok) {
        message.success(`Opportunity ${editingOpportunity ? 'updated' : 'created'} successfully`);
        setModalVisible(false);
        fetchOpportunities();
      } else {
        const errorData = await response.json();
        message.error(errorData.message || `Failed to ${editingOpportunity ? 'update' : 'create'} opportunity`);
      }
    } catch (error) {
      console.error('Error saving opportunity:', error);
      message.error('Error saving opportunity');
    }
  };

  const getTypeColor = (type: string) => {
    const colors = {
      internship: 'blue',
      training: 'green',
      conference: 'purple',
      workshop: 'orange',
      competition: 'red'
    };
    return colors[type as keyof typeof colors] || 'default';
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      internship: 'Internship',
      training: 'Training',
      conference: 'Conference',
      workshop: 'Workshop',
      competition: 'Competition'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const isExpired = (deadline: string) => {
    return new Date(deadline) < new Date();
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: '20%',
      render: (text: string, record: Opportunity) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.organization}</div>
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: '12%',
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {getTypeLabel(type)}
        </Tag>
      )
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: '15%',
      render: (location: string, record: Opportunity) => (
        <div>
          <div>{location}</div>
          {record.isRemote && <Tag className="text-xs" color="green">Remote</Tag>}
        </div>
      )
    },
    {
      title: 'Deadline',
      dataIndex: 'deadline',
      key: 'deadline',
      width: '12%',
      render: (deadline: string) => {
        const expired = isExpired(deadline);
        return (
          <div className={expired ? 'text-red-500' : ''}>
            {new Date(deadline).toLocaleDateString()}
            {expired && <div className="text-xs">Expired</div>}
          </div>
        );
      }
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: '10%',
      render: (isActive: boolean, record: Opportunity) => {
        const expired = isExpired(record.deadline);
        return (
          <Tag color={isActive && !expired ? 'green' : 'red'}>
            {expired ? 'Expired' : isActive ? 'Active' : 'Inactive'}
          </Tag>
        );
      }
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '15%',
      render: (tags: string[]) => (
        <div>
          {tags?.slice(0, 2).map((tag, index) => (
            <Tag key={index} className="text-xs">{tag}</Tag>
          ))}
          {tags?.length > 2 && <Tag className="text-xs">+{tags.length - 2}</Tag>}
        </div>
      )
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '10%',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '6%',
      render: (_: any, record: Opportunity) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => window.open(`/opportunities/${record.id}`, '_blank')}
            title="View"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="Edit"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
            title="Delete"
          />
        </Space>
      )
    }
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Opportunity Management</h1>
          <p className="text-gray-600">Manage internships, training, conferences, and competitions</p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
          size="large"
        >
          Create Opportunity
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={opportunities}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} opportunities`
        }}
        scroll={{ x: 1400 }}
      />

      <Modal
        title={editingOpportunity ? 'Edit Opportunity' : 'Create Opportunity'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={900}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isActive: true,
            isRemote: false,
            type: 'internship'
          }}
        >
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="title"
              label="Title"
              rules={[{ required: true, message: 'Please enter the title' }]}
            >
              <Input placeholder="Enter opportunity title" />
            </Form.Item>

            <Form.Item
              name="type"
              label="Type"
              rules={[{ required: true, message: 'Please select a type' }]}
            >
              <Select placeholder="Select type">
                <Option value="internship">Internship</Option>
                <Option value="training">Training</Option>
                <Option value="conference">Conference</Option>
                <Option value="workshop">Workshop</Option>
                <Option value="competition">Competition</Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter the description' }]}
          >
            <TextArea rows={4} placeholder="Detailed description of the opportunity" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="organization"
              label="Organization"
              rules={[{ required: true, message: 'Please enter the organization' }]}
            >
              <Input placeholder="Organization name" />
            </Form.Item>

            <Form.Item
              name="location"
              label="Location"
              rules={[{ required: true, message: 'Please enter the location' }]}
            >
              <Input placeholder="City, Country" />
            </Form.Item>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <Form.Item
              name="deadline"
              label="Application Deadline"
              rules={[{ required: true, message: 'Please select the deadline' }]}
            >
              <DatePicker className="w-full" />
            </Form.Item>

            <Form.Item
              name="startDate"
              label="Start Date"
            >
              <DatePicker className="w-full" />
            </Form.Item>

            <Form.Item
              name="endDate"
              label="End Date"
            >
              <DatePicker className="w-full" />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="applicationLink"
              label="Application Link"
            >
              <Input placeholder="https://example.com/apply" />
            </Form.Item>

            <Form.Item
              name="website"
              label="Website"
            >
              <Input placeholder="https://example.com" />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="contactEmail"
              label="Contact Email"
            >
              <Input placeholder="<EMAIL>" />
            </Form.Item>

            <Form.Item
              name="thumbnail"
              label="Thumbnail URL"
            >
              <Input placeholder="https://example.com/image.jpg" />
            </Form.Item>
          </div>

          <Form.Item
            name="requirements"
            label="Requirements"
          >
            <TextArea rows={3} placeholder="Requirements and qualifications" />
          </Form.Item>

          <Form.Item
            name="benefits"
            label="Benefits"
          >
            <TextArea rows={3} placeholder="Benefits and compensation" />
          </Form.Item>

          <Form.Item
            name="tags"
            label="Tags"
            help="Comma-separated tags"
          >
            <Input placeholder="internship, tech, remote" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="isRemote"
              label="Remote Work"
              valuePropName="checked"
            >
              <Switch checkedChildren="Remote" unCheckedChildren="On-site" />
            </Form.Item>

            <Form.Item
              name="isActive"
              label="Status"
              valuePropName="checked"
            >
              <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
            </Form.Item>
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button onClick={() => setModalVisible(false)}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              {editingOpportunity ? 'Update' : 'Create'} Opportunity
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default OpportunityManager;
