import React, { useState, useEffect } from 'react';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Table, Space, Modal, Form, Input, Select, Switch, InputNumber, Tag, message } from 'antd';

const { TextArea } = Input;
const { Option } = Select;

interface Guide {
  id: number;
  title: string;
  content: string;
  category: 'application' | 'documents' | 'preparation' | 'tips';
  slug: string;
  excerpt?: string;
  thumbnail?: string;
  isPublished: boolean;
  readTime?: number;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

const GuideManager: React.FC = () => {
  const [guides, setGuides] = useState<Guide[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingGuide, setEditingGuide] = useState<Guide | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchGuides();
  }, []);

  const fetchGuides = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/guides?limit=100', {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setGuides(data.data.guides || []);
      } else {
        message.error('Failed to fetch guides');
      }
    } catch (error) {
      console.error('Error fetching guides:', error);
      message.error('Error fetching guides');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingGuide(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (guide: Guide) => {
    setEditingGuide(guide);
    form.setFieldsValue({
      ...guide,
      tags: guide.tags?.join(', ') || ''
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: 'Delete Guide',
      content: 'Are you sure you want to delete this guide? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const response = await fetch(`/api/guides/${id}`, {
            method: 'DELETE',
            credentials: 'include'
          });
          if (response.ok) {
            message.success('Guide deleted successfully');
            fetchGuides();
          } else {
            message.error('Failed to delete guide');
          }
        } catch (error) {
          console.error('Error deleting guide:', error);
          message.error('Error deleting guide');
        }
      }
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      const guideData = {
        ...values,
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        slug: values.slug || values.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
      };

      const url = editingGuide ? `/api/guides/${editingGuide.id}` : '/api/guides';
      const method = editingGuide ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(guideData)
      });

      if (response.ok) {
        message.success(`Guide ${editingGuide ? 'updated' : 'created'} successfully`);
        setModalVisible(false);
        fetchGuides();
      } else {
        const errorData = await response.json();
        message.error(errorData.message || `Failed to ${editingGuide ? 'update' : 'create'} guide`);
      }
    } catch (error) {
      console.error('Error saving guide:', error);
      message.error('Error saving guide');
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      application: 'blue',
      documents: 'green',
      preparation: 'purple',
      tips: 'orange'
    };
    return colors[category as keyof typeof colors] || 'default';
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      application: 'Application',
      documents: 'Documents',
      preparation: 'Preparation',
      tips: 'Tips'
    };
    return labels[category as keyof typeof labels] || category;
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: '25%',
      render: (text: string, record: Guide) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">/{record.slug}</div>
        </div>
      )
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: '15%',
      render: (category: string) => (
        <Tag color={getCategoryColor(category)}>
          {getCategoryLabel(category)}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'isPublished',
      key: 'isPublished',
      width: '10%',
      render: (isPublished: boolean) => (
        <Tag color={isPublished ? 'green' : 'red'}>
          {isPublished ? 'Published' : 'Draft'}
        </Tag>
      )
    },
    {
      title: 'Read Time',
      dataIndex: 'readTime',
      key: 'readTime',
      width: '10%',
      render: (readTime: number) => readTime ? `${readTime} min` : '-'
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '20%',
      render: (tags: string[]) => (
        <div>
          {tags?.slice(0, 3).map((tag, index) => (
            <Tag key={index} className="text-xs">{tag}</Tag>
          ))}
          {tags?.length > 3 && <Tag className="text-xs">+{tags.length - 3}</Tag>}
        </div>
      )
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '10%',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '10%',
      render: (_: any, record: Guide) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => window.open(`/guides/${record.slug}`, '_blank')}
            title="View"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="Edit"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
            title="Delete"
          />
        </Space>
      )
    }
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Guide Management</h1>
          <p className="text-gray-600">Manage guides and educational content</p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreate}
          size="large"
        >
          Create Guide
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={guides}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} guides`
        }}
        scroll={{ x: 1200 }}
      />

      <Modal
        title={editingGuide ? 'Edit Guide' : 'Create Guide'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isPublished: true,
            category: 'application'
          }}
        >
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter the title' }]}
          >
            <Input placeholder="Enter guide title" />
          </Form.Item>

          <Form.Item
            name="slug"
            label="URL Slug"
            help="Leave empty to auto-generate from title"
          >
            <Input placeholder="url-friendly-slug" />
          </Form.Item>

          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select a category' }]}
          >
            <Select placeholder="Select category">
              <Option value="application">Application</Option>
              <Option value="documents">Documents</Option>
              <Option value="preparation">Preparation</Option>
              <Option value="tips">Tips</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="excerpt"
            label="Excerpt"
            help="Brief description for preview"
          >
            <TextArea rows={2} placeholder="Brief description of the guide" />
          </Form.Item>

          <Form.Item
            name="content"
            label="Content"
            rules={[{ required: true, message: 'Please enter the content' }]}
          >
            <TextArea rows={10} placeholder="Guide content (Markdown supported)" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="readTime"
              label="Read Time (minutes)"
            >
              <InputNumber min={1} max={120} placeholder="5" />
            </Form.Item>

            <Form.Item
              name="thumbnail"
              label="Thumbnail URL"
            >
              <Input placeholder="https://example.com/image.jpg" />
            </Form.Item>
          </div>

          <Form.Item
            name="tags"
            label="Tags"
            help="Comma-separated tags"
          >
            <Input placeholder="cv, application, tips" />
          </Form.Item>

          <Form.Item
            name="isPublished"
            label="Status"
            valuePropName="checked"
          >
            <Switch checkedChildren="Published" unCheckedChildren="Draft" />
          </Form.Item>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button onClick={() => setModalVisible(false)}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              {editingGuide ? 'Update' : 'Create'} Guide
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default GuideManager;
