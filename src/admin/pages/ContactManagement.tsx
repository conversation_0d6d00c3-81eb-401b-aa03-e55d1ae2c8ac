import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Typography,
  Popconfirm,
  Tag,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  HomeOutlined
} from '@ant-design/icons';

const { Title } = Typography;
const { TabPane } = Tabs;

interface ContactSetting {
  id: number;
  settingKey: string;
  settingValue: string;
  settingType: 'text' | 'email' | 'phone' | 'url';
  category: 'general' | 'social' | 'contact' | 'address';
  displayOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const ContactManagement: React.FC = () => {
  const [settings, setSettings] = useState<ContactSetting[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<ContactSetting | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/contact-settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSettings(data.data || []);
      } else {
        message.error('Failed to fetch contact settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      message.error('Error fetching contact settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const url = editingItem 
        ? `/api/contact-settings/${editingItem.id}`
        : '/api/contact-settings';
      
      const method = editingItem ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        message.success(`Contact setting ${editingItem ? 'updated' : 'created'} successfully`);
        setModalVisible(false);
        setEditingItem(null);
        form.resetFields();
        fetchSettings();
      } else {
        const errorData = await response.json();
        message.error(errorData.message || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving setting:', error);
      message.error('Error saving contact setting');
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await fetch(`/api/contact-settings/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        message.success('Contact setting deleted successfully');
        fetchSettings();
      } else {
        message.error('Failed to delete contact setting');
      }
    } catch (error) {
      console.error('Error deleting setting:', error);
      message.error('Error deleting contact setting');
    }
  };

  const openModal = (item?: ContactSetting) => {
    setEditingItem(item || null);
    if (item) {
      form.setFieldsValue(item);
    } else {
      form.resetFields();
    }
    setModalVisible(true);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'contact': return <PhoneOutlined />;
      case 'social': return <GlobalOutlined />;
      case 'address': return <HomeOutlined />;
      default: return <MailOutlined />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'contact': return 'blue';
      case 'social': return 'green';
      case 'address': return 'orange';
      default: return 'purple';
    }
  };

  const columns = [
    {
      title: 'Key',
      dataIndex: 'settingKey',
      key: 'settingKey',
      width: 200,
    },
    {
      title: 'Value',
      dataIndex: 'settingValue',
      key: 'settingValue',
      ellipsis: true,
    },
    {
      title: 'Type',
      dataIndex: 'settingType',
      key: 'settingType',
      width: 100,
      render: (type: string) => <Tag>{type}</Tag>
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: string) => (
        <Tag color={getCategoryColor(category)} icon={getCategoryIcon(category)}>
          {category}
        </Tag>
      )
    },
    {
      title: 'Order',
      dataIndex: 'displayOrder',
      key: 'displayOrder',
      width: 80,
      sorter: (a: ContactSetting, b: ContactSetting) => a.displayOrder - b.displayOrder,
    },
    {
      title: 'Active',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Yes' : 'No'}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: ContactSetting) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          />
          <Popconfirm
            title="Are you sure you want to delete this setting?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      )
    }
  ];

  const filterByCategory = (category: string) => {
    return settings.filter(setting => setting.category === category);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Contact Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          Add Contact Setting
        </Button>
      </div>

      <Tabs defaultActiveKey="all">
        <TabPane tab="All Settings" key="all">
          <Card>
            <Table
              columns={columns}
              dataSource={settings}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane tab={<span><PhoneOutlined /> Contact</span>} key="contact">
          <Card>
            <Table
              columns={columns}
              dataSource={filterByCategory('contact')}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane tab={<span><GlobalOutlined /> Social</span>} key="social">
          <Card>
            <Table
              columns={columns}
              dataSource={filterByCategory('social')}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane tab={<span><HomeOutlined /> Address</span>} key="address">
          <Card>
            <Table
              columns={columns}
              dataSource={filterByCategory('address')}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      <Modal
        title={editingItem ? 'Edit Contact Setting' : 'Add Contact Setting'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingItem(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            settingType: 'text',
            category: 'general',
            displayOrder: 1,
            isActive: true
          }}
        >
          <Form.Item
            name="settingKey"
            label="Setting Key"
            rules={[{ required: true, message: 'Please enter setting key' }]}
          >
            <Input placeholder="e.g., phone_number, email, facebook_url" />
          </Form.Item>

          <Form.Item
            name="settingValue"
            label="Setting Value"
            rules={[{ required: true, message: 'Please enter setting value' }]}
          >
            <Input placeholder="Enter the value for this setting" />
          </Form.Item>

          <Form.Item
            name="settingType"
            label="Setting Type"
            rules={[{ required: true, message: 'Please select setting type' }]}
          >
            <Select>
              <Select.Option value="text">Text</Select.Option>
              <Select.Option value="email">Email</Select.Option>
              <Select.Option value="phone">Phone</Select.Option>
              <Select.Option value="url">URL</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select category' }]}
          >
            <Select>
              <Select.Option value="general">General</Select.Option>
              <Select.Option value="contact">Contact</Select.Option>
              <Select.Option value="social">Social Media</Select.Option>
              <Select.Option value="address">Address</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="displayOrder"
            label="Display Order"
            rules={[{ required: true, message: 'Please enter display order' }]}
          >
            <Input type="number" min={1} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Active"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingItem ? 'Update' : 'Create'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingItem(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ContactManagement;
