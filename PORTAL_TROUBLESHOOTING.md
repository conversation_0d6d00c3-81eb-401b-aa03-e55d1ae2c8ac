# Portal Troubleshooting Guide

This guide provides solutions for common issues with the MaBourse portal, particularly related to duplicate data and login problems.

## Issues Fixed

1. **Duplicate Admin Accounts**: The system was creating duplicate admin accounts during initialization.
2. **Strange Data Appearing**: SMS messages, scholarships, and other data were being created unexpectedly.
3. **Login Portal Issues**: The login process was creating inconsistent admin sessions.
4. **Password Hashing Issues**: Admin passwords were sometimes stored as plain text instead of being properly hashed.

## How the Issues Were Fixed

1. **Database Initialization Consolidation**:
   - Modified server startup to use only one database initialization method
   - Added proper checks for existing data before creating defaults
   - Implemented cleanup utilities to remove duplicate data

2. **Login Component Fixes**:
   - Updated the login component to prevent creation of duplicate admin sessions
   - Ensured consistent data structure in localStorage
   - Fixed the "Reset ALL Data" button to properly clean up all data

3. **Password Hashing Fixes**:
   - Added checks to ensure admin passwords are properly hashed
   - Implemented automatic password hashing for plain text passwords
   - Fixed login issues caused by improperly stored passwords

4. **Automatic Data Cleanup**:
   - Added a data cleanup process that runs on server startup
   - This process automatically removes duplicate admin accounts, scholarships, and messages
   - Also checks and fixes improperly hashed passwords

## How to Use

### Normal Usage

The fixes have been implemented in the codebase, so you should no longer experience these issues during normal usage. The system will:

1. Automatically clean up duplicate data on server startup
2. Prevent creation of duplicate admin accounts
3. Ensure consistent login behavior

### If Issues Persist

If you still experience issues with duplicate data or login problems:

1. **Reset All Data**: Use the "Reset ALL Data" button on the login page to clear all localStorage data and reset to default settings.

2. **Run Cleanup Scripts Manually**: You can run the cleanup scripts manually if needed:
   ```bash
   cd backend
   npx ts-node src/scripts/cleanupDuplicateAdmins.ts
   npx ts-node src/scripts/cleanupDuplicateData.ts
   ```

3. **Check Database Content**: You can check the content of the database using Prisma Studio:
   ```bash
   cd backend
   npx prisma studio
   ```

## Login Credentials

The main admin credentials are:
- Email: `<EMAIL>`
- Password: `admin123`

Alternative login (for backward compatibility):
- Email: `<EMAIL>`
- Password: any password

## Technical Details

The application uses two database systems:
1. **Sequelize** with SQLite (legacy)
2. **Prisma** with SQLite (newer implementation)

The fixes ensure that both systems work together without creating duplicate data.

## Support

If you continue to experience issues, please contact the development team for further assistance.
