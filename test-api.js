// Test script to verify API endpoints
const axios = require('axios');

const API_URL = 'http://localhost:5000';
let adminToken = null;

// Login to get admin token
async function login() {
  try {
    const response = await axios.post(`${API_URL}/api/admin/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('Login response:', response.data);
    
    if (response.data.token) {
      adminToken = response.data.token;
      console.log('Admin token obtained');
      return true;
    } else {
      console.log('No token in response');
      return false;
    }
  } catch (error) {
    console.error('Login error:', error.response?.data || error.message);
    return false;
  }
}

// Test getting admin stats
async function testAdminStats() {
  try {
    const response = await axios.get(`${API_URL}/api/admin/stats`, {
      headers: {
        Authorization: `Bearer ${adminToken}`
      }
    });
    
    console.log('Admin stats:', response.data);
    return response.data;
  } catch (error) {
    console.error('Admin stats error:', error.response?.data || error.message);
    return null;
  }
}

// Test getting admin info
async function testAdminMe() {
  try {
    const response = await axios.get(`${API_URL}/api/admin/me`, {
      headers: {
        Authorization: `Bearer ${adminToken}`
      }
    });
    
    console.log('Admin info:', response.data);
    return response.data;
  } catch (error) {
    console.error('Admin info error:', error.response?.data || error.message);
    return null;
  }
}

// Test getting scholarships
async function testScholarships() {
  try {
    const response = await axios.get(`${API_URL}/api/scholarships`, {
      headers: {
        Authorization: `Bearer ${adminToken}`
      }
    });
    
    console.log('Scholarships:', response.data);
    return response.data;
  } catch (error) {
    console.error('Scholarships error:', error.response?.data || error.message);
    return null;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting API tests...');
  
  // Login first
  const loggedIn = await login();
  if (!loggedIn) {
    console.log('Login failed, cannot continue tests');
    return;
  }
  
  // Run tests
  await testAdminStats();
  await testAdminMe();
  await testScholarships();
  
  console.log('All tests completed');
}

runTests();
