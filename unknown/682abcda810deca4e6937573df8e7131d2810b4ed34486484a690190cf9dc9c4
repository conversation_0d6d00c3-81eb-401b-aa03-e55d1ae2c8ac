#!/usr/bin/env ts-node

/**
 * Migration script for API Security Features
 * Adds API keys table and enhanced security features
 */

import { query, closeDatabase, initializeDatabase } from '../config/database';

async function migrateAPISecurityFeatures() {
  try {
    console.log('🔄 Starting API Security Features migration...');
    
    // Initialize database connection
    await initializeDatabase();

    // Create API keys table
    console.log('Creating api_keys table...');
    await query(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id SERIAL PRIMARY KEY,
        admin_id INTEGER NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
        key_id VARCHAR(255) NOT NULL UNIQUE,
        secret_key VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        permissions JSONB DEFAULT '[]'::jsonb,
        is_active BOOLEAN DEFAULT TRUE,
        expires_at TIMESTAMP,
        last_used TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create indexes for API keys table
    console.log('Creating indexes for API keys...');
    await query(`
      CREATE INDEX IF NOT EXISTS idx_api_keys_admin_id ON api_keys(admin_id);
      CREATE INDEX IF NOT EXISTS idx_api_keys_key_id ON api_keys(key_id);
      CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active);
      CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at);
      CREATE INDEX IF NOT EXISTS idx_api_keys_last_used ON api_keys(last_used);
      CREATE INDEX IF NOT EXISTS idx_api_keys_created_at ON api_keys(created_at);
    `);

    // Add rate limiting tracking table
    console.log('Creating rate_limit_tracking table...');
    await query(`
      CREATE TABLE IF NOT EXISTS rate_limit_tracking (
        id SERIAL PRIMARY KEY,
        identifier VARCHAR(255) NOT NULL, -- IP or user ID
        endpoint VARCHAR(255) NOT NULL,
        request_count INTEGER DEFAULT 1,
        window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        window_end TIMESTAMP DEFAULT CURRENT_TIMESTAMP + INTERVAL '15 minutes',
        risk_level VARCHAR(20) DEFAULT 'low',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create indexes for rate limiting
    await query(`
      CREATE INDEX IF NOT EXISTS idx_rate_limit_identifier ON rate_limit_tracking(identifier);
      CREATE INDEX IF NOT EXISTS idx_rate_limit_endpoint ON rate_limit_tracking(endpoint);
      CREATE INDEX IF NOT EXISTS idx_rate_limit_window_end ON rate_limit_tracking(window_end);
      CREATE INDEX IF NOT EXISTS idx_rate_limit_risk_level ON rate_limit_tracking(risk_level);
    `);

    // Add request signatures tracking table
    console.log('Creating request_signatures table...');
    await query(`
      CREATE TABLE IF NOT EXISTS request_signatures (
        id SERIAL PRIMARY KEY,
        key_id VARCHAR(255) NOT NULL,
        nonce VARCHAR(255) NOT NULL,
        timestamp BIGINT NOT NULL,
        endpoint VARCHAR(255) NOT NULL,
        method VARCHAR(10) NOT NULL,
        signature_valid BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(nonce, timestamp)
      );
    `);

    // Create indexes for request signatures
    await query(`
      CREATE INDEX IF NOT EXISTS idx_request_signatures_key_id ON request_signatures(key_id);
      CREATE INDEX IF NOT EXISTS idx_request_signatures_nonce ON request_signatures(nonce);
      CREATE INDEX IF NOT EXISTS idx_request_signatures_timestamp ON request_signatures(timestamp);
      CREATE INDEX IF NOT EXISTS idx_request_signatures_created_at ON request_signatures(created_at);
    `);

    // Create a cleanup function for old signatures (prevent replay attacks)
    console.log('Creating signature cleanup function...');
    await query(`
      CREATE OR REPLACE FUNCTION cleanup_old_signatures()
      RETURNS void AS $$
      BEGIN
        DELETE FROM request_signatures 
        WHERE created_at < NOW() - INTERVAL '1 hour';
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create a scheduled job to clean up old signatures (if pg_cron is available)
    try {
      await query(`
        SELECT cron.schedule('cleanup-signatures', '*/15 * * * *', 'SELECT cleanup_old_signatures();');
      `);
      console.log('✅ Scheduled signature cleanup job');
    } catch (error) {
      console.log('⚠️  Could not schedule cleanup job (pg_cron not available)');
    }

    // Add enhanced security event types
    console.log('Adding enhanced security event tracking...');
    await query(`
      ALTER TABLE security_events 
      ADD COLUMN IF NOT EXISTS api_key_id VARCHAR(255),
      ADD COLUMN IF NOT EXISTS request_signature_valid BOOLEAN,
      ADD COLUMN IF NOT EXISTS rate_limit_exceeded BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS adaptive_threshold INTEGER;
    `);

    // Create indexes for new security event fields
    await query(`
      CREATE INDEX IF NOT EXISTS idx_security_events_api_key_id ON security_events(api_key_id);
      CREATE INDEX IF NOT EXISTS idx_security_events_signature_valid ON security_events(request_signature_valid);
      CREATE INDEX IF NOT EXISTS idx_security_events_rate_limit ON security_events(rate_limit_exceeded);
    `);

    // Create API security dashboard view
    console.log('Creating API security dashboard view...');
    await query(`
      CREATE OR REPLACE VIEW api_security_dashboard AS
      SELECT 
        ak.admin_id,
        ak.name as api_key_name,
        ak.key_id,
        ak.is_active,
        ak.last_used,
        ak.created_at,
        COUNT(se.id) as total_requests,
        COUNT(se.id) FILTER (WHERE se.request_signature_valid = true) as valid_requests,
        COUNT(se.id) FILTER (WHERE se.request_signature_valid = false) as invalid_requests,
        COUNT(se.id) FILTER (WHERE se.rate_limit_exceeded = true) as rate_limited_requests,
        AVG(se.risk_score) as avg_risk_score,
        MAX(se.timestamp) as last_request_time
      FROM api_keys ak
      LEFT JOIN security_events se ON se.api_key_id = ak.key_id
        AND se.timestamp > NOW() - INTERVAL '24 hours'
      GROUP BY ak.admin_id, ak.name, ak.key_id, ak.is_active, ak.last_used, ak.created_at
      ORDER BY ak.created_at DESC;
    `);

    // Create rate limiting analytics view
    await query(`
      CREATE OR REPLACE VIEW rate_limiting_analytics AS
      SELECT 
        DATE_TRUNC('hour', created_at) as hour,
        endpoint,
        risk_level,
        COUNT(*) as request_count,
        COUNT(DISTINCT identifier) as unique_identifiers,
        AVG(request_count) as avg_requests_per_identifier
      FROM rate_limit_tracking
      WHERE created_at > NOW() - INTERVAL '24 hours'
      GROUP BY DATE_TRUNC('hour', created_at), endpoint, risk_level
      ORDER BY hour DESC, request_count DESC;
    `);

    // Insert sample API permissions
    console.log('Setting up API permission templates...');
    await query(`
      CREATE TABLE IF NOT EXISTS api_permission_templates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        permissions JSONB NOT NULL,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Insert default permission templates
    const permissionTemplates = [
      {
        name: 'Read Only',
        description: 'Read-only access to all resources',
        permissions: ['read:scholarships', 'read:admins', 'read:analytics'],
        isDefault: true
      },
      {
        name: 'Scholarship Manager',
        description: 'Full access to scholarship management',
        permissions: ['read:scholarships', 'write:scholarships', 'delete:scholarships', 'read:analytics']
      },
      {
        name: 'Admin Manager',
        description: 'Full access to admin management',
        permissions: ['read:admins', 'write:admins', 'delete:admins', 'read:security']
      },
      {
        name: 'Full Access',
        description: 'Complete access to all resources',
        permissions: ['*']
      }
    ];

    for (const template of permissionTemplates) {
      try {
        await query(`
          INSERT INTO api_permission_templates (name, description, permissions, is_default)
          VALUES ($1, $2, $3, $4)
        `, [template.name, template.description, JSON.stringify(template.permissions), template.isDefault]);
      } catch (error) {
        // Template might already exist, continue
        console.log(`Template "${template.name}" already exists, skipping...`);
      }
    }

    console.log('✅ API Security Features migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Error migrating API Security Features:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateAPISecurityFeatures()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export default migrateAPISecurityFeatures;
