<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Admin Login Test</h1>
    
    <div class="test-section">
        <h3>Backend Health Check</h3>
        <button onclick="testBackendHealth()">Test Backend Health</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h3>Admin Login Test</h3>
        <button onclick="testAdminLogin()">Test Admin Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>Get Admin Profile Test</h3>
        <button onclick="testGetAdminProfile()">Test Get Admin Profile</button>
        <div id="profile-result"></div>
    </div>

    <div class="test-section">
        <h3>Admin Logout Test</h3>
        <button onclick="testAdminLogout()">Test Admin Logout</button>
        <div id="logout-result"></div>
    </div>

    <script>
        async function testBackendHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Backend Health Check Successful</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Backend Health Check Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Backend Health Check Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAdminLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>Testing admin login...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include', // Important for cookies
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Login Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Login Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Login Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testGetAdminProfile() {
            const resultDiv = document.getElementById('profile-result');
            resultDiv.innerHTML = '<p>Testing get admin profile...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/current', {
                    method: 'GET',
                    credentials: 'include', // Important for cookies
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Get Admin Profile Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Get Admin Profile Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Get Admin Profile Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAdminLogout() {
            const resultDiv = document.getElementById('logout-result');
            resultDiv.innerHTML = '<p>Testing admin logout...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/logout', {
                    method: 'POST',
                    credentials: 'include', // Important for cookies
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Admin Logout Successful</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Admin Logout Failed</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Admin Logout Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
